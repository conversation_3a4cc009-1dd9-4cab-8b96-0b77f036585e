-- 永盛制冷维修有限公司-家电售卖与维修数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS appliance_repair CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE appliance_repair;

-- 客户表
CREATE TABLE IF NOT EXISTS customers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码',
  name VARCHAR(100) NOT NULL COMMENT '姓名',
  phone VARCHAR(20) NOT NULL COMMENT '手机号',
  address TEXT COMMENT '地址',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  last_login TIMESTAMP NULL COMMENT '最后登录时间'
) COMMENT '客户表';

-- 工人表
CREATE TABLE IF NOT EXISTS workers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码',
  name VARCHAR(100) NOT NULL COMMENT '姓名',
  phone VARCHAR(20) NOT NULL COMMENT '手机号',
  skills TEXT COMMENT '技能描述',
  commission_rate DECIMAL(5,2) DEFAULT 0.10 COMMENT '提成比例',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  last_login TIMESTAMP NULL COMMENT '最后登录时间'
) COMMENT '工人表';

-- 管理员表
CREATE TABLE IF NOT EXISTS admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码',
  name VARCHAR(100) NOT NULL COMMENT '姓名',
  phone VARCHAR(20) COMMENT '手机号',
  role ENUM('super_admin', 'admin') DEFAULT 'admin' COMMENT '角色',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  last_login TIMESTAMP NULL COMMENT '最后登录时间'
) COMMENT '管理员表';

-- 工单表
CREATE TABLE IF NOT EXISTS orders (
  id INT PRIMARY KEY AUTO_INCREMENT,
  customer_id INT NOT NULL COMMENT '客户ID',
  worker_id INT NULL COMMENT '工人ID',
  appliance_type VARCHAR(100) NOT NULL COMMENT '家电类型',
  brand_model VARCHAR(200) COMMENT '品牌型号',
  problem_description TEXT NOT NULL COMMENT '问题描述',
  contact_name VARCHAR(100) NOT NULL COMMENT '联系人姓名',
  contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
  address TEXT NOT NULL COMMENT '维修地址',
  purchase_date DATE COMMENT '购买日期',
  preferred_date DATE COMMENT '预约日期',
  preferred_time ENUM('morning', 'afternoon', 'evening') COMMENT '预约时段',
  urgency ENUM('low', 'medium', 'high') DEFAULT 'low' COMMENT '紧急程度',
  remarks TEXT COMMENT '备注信息',
  status ENUM('pending', 'accepted', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
  rating INT NULL COMMENT '客户评分(1-5)',
  comment TEXT COMMENT '客户评价',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  accepted_at TIMESTAMP NULL COMMENT '接单时间',
  completed_at TIMESTAMP NULL COMMENT '完成时间',
  FOREIGN KEY (customer_id) REFERENCES customers(id),
  FOREIGN KEY (worker_id) REFERENCES workers(id)
) COMMENT '工单表';

-- 维修进度表
CREATE TABLE IF NOT EXISTS repair_progress (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL COMMENT '工单ID',
  worker_id INT NOT NULL COMMENT '工人ID',
  title VARCHAR(200) NOT NULL COMMENT '进度标题',
  description TEXT NOT NULL COMMENT '进度描述',
  estimated_completion DATETIME COMMENT '预计完成时间',
  required_parts TEXT COMMENT '需要配件',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  FOREIGN KEY (order_id) REFERENCES orders(id),
  FOREIGN KEY (worker_id) REFERENCES workers(id)
) COMMENT '维修进度表';

-- 维修记录表
CREATE TABLE IF NOT EXISTS repair_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL COMMENT '工单ID',
  worker_id INT NOT NULL COMMENT '工人ID',
  result ENUM('success', 'partial', 'failed') NOT NULL COMMENT '维修结果',
  cause TEXT NOT NULL COMMENT '问题原因',
  solution TEXT NOT NULL COMMENT '解决方案',
  replaced_parts TEXT COMMENT '更换配件',
  cost DECIMAL(10,2) DEFAULT 0 COMMENT '维修费用',
  summary TEXT NOT NULL COMMENT '维修总结',
  suggestions TEXT COMMENT '使用建议',
  warranty_period INT COMMENT '保修期限(月)',
  repair_time DATETIME NOT NULL COMMENT '维修时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  FOREIGN KEY (order_id) REFERENCES orders(id),
  FOREIGN KEY (worker_id) REFERENCES workers(id)
) COMMENT '维修记录表';

-- 文件上传表
CREATE TABLE IF NOT EXISTS file_uploads (
  id INT PRIMARY KEY AUTO_INCREMENT,
  related_type ENUM('order', 'repair_record') NOT NULL COMMENT '关联类型',
  related_id INT NOT NULL COMMENT '关联ID',
  file_name VARCHAR(255) NOT NULL COMMENT '文件名',
  file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
  file_type VARCHAR(50) NOT NULL COMMENT '文件类型',
  file_size INT NOT NULL COMMENT '文件大小',
  uploaded_by INT NOT NULL COMMENT '上传者ID',
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间'
) COMMENT '文件上传表';

-- 工人薪资配置表
CREATE TABLE IF NOT EXISTS worker_salary_config (
  id INT PRIMARY KEY AUTO_INCREMENT,
  worker_id INT NOT NULL,
  base_salary DECIMAL(10,2) DEFAULT 3000.00 COMMENT '基础工资',
  commission_rate DECIMAL(5,4) DEFAULT 0.1500 COMMENT '提成比例',
  performance_bonus_rate DECIMAL(5,4) DEFAULT 0.0500 COMMENT '绩效奖金比例',
  attendance_bonus DECIMAL(10,2) DEFAULT 200.00 COMMENT '全勤奖',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (worker_id) REFERENCES workers(id) ON DELETE CASCADE,
  UNIQUE KEY unique_worker (worker_id)
) COMMENT '工人薪资配置表';

-- 月度薪资记录表
CREATE TABLE IF NOT EXISTS monthly_salary (
  id INT PRIMARY KEY AUTO_INCREMENT,
  worker_id INT NOT NULL,
  year INT NOT NULL COMMENT '年份',
  month INT NOT NULL COMMENT '月份',
  base_salary DECIMAL(10,2) NOT NULL COMMENT '基础工资',
  commission_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '提成金额',
  performance_bonus DECIMAL(10,2) DEFAULT 0.00 COMMENT '绩效奖金',
  attendance_bonus DECIMAL(10,2) DEFAULT 0.00 COMMENT '全勤奖',
  overtime_pay DECIMAL(10,2) DEFAULT 0.00 COMMENT '加班费',
  deductions DECIMAL(10,2) DEFAULT 0.00 COMMENT '扣款',
  total_salary DECIMAL(10,2) NOT NULL COMMENT '总薪资',
  completed_orders INT DEFAULT 0 COMMENT '完成工单数',
  total_revenue DECIMAL(10,2) DEFAULT 0.00 COMMENT '总收入',
  avg_rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均评分',
  attendance_days INT DEFAULT 0 COMMENT '出勤天数',
  status ENUM('draft', 'confirmed', 'paid') DEFAULT 'draft' COMMENT '状态',
  calculated_at TIMESTAMP NULL COMMENT '计算时间',
  confirmed_at TIMESTAMP NULL COMMENT '确认时间',
  paid_at TIMESTAMP NULL COMMENT '发放时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (worker_id) REFERENCES workers(id) ON DELETE CASCADE,
  UNIQUE KEY unique_worker_month (worker_id, year, month)
) COMMENT '月度薪资记录表';

-- 薪资明细表
CREATE TABLE IF NOT EXISTS salary_details (
  id INT PRIMARY KEY AUTO_INCREMENT,
  monthly_salary_id INT NOT NULL,
  order_id INT NULL COMMENT '关联工单ID',
  item_type ENUM('base', 'commission', 'performance', 'attendance', 'overtime', 'deduction') NOT NULL COMMENT '项目类型',
  item_name VARCHAR(100) NOT NULL COMMENT '项目名称',
  amount DECIMAL(10,2) NOT NULL COMMENT '金额',
  description TEXT COMMENT '说明',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (monthly_salary_id) REFERENCES monthly_salary(id) ON DELETE CASCADE,
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL
) COMMENT '薪资明细表';

-- 考勤记录表
CREATE TABLE IF NOT EXISTS attendance_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  worker_id INT NOT NULL,
  date DATE NOT NULL COMMENT '日期',
  check_in_time TIME COMMENT '签到时间',
  check_out_time TIME COMMENT '签退时间',
  work_hours DECIMAL(4,2) DEFAULT 0.00 COMMENT '工作小时数',
  overtime_hours DECIMAL(4,2) DEFAULT 0.00 COMMENT '加班小时数',
  status ENUM('present', 'absent', 'late', 'leave') DEFAULT 'present' COMMENT '状态',
  notes TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (worker_id) REFERENCES workers(id) ON DELETE CASCADE,
  UNIQUE KEY unique_worker_date (worker_id, date)
) COMMENT '考勤记录表';

-- 更新workers表，添加薪资相关字段（如果字段不存在则添加）
ALTER TABLE workers
ADD COLUMN hourly_rate DECIMAL(8,2) DEFAULT 50.00 COMMENT '小时工资',
ADD COLUMN overtime_rate DECIMAL(8,2) DEFAULT 75.00 COMMENT '加班小时工资';

-- 为orders表添加服务费字段（如果字段不存在则添加）
ALTER TABLE orders
ADD COLUMN service_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '服务费用';

-- 创建索引
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_orders_worker_id ON orders(worker_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_preferred_date ON orders(preferred_date);
CREATE INDEX idx_repair_progress_order_id ON repair_progress(order_id);
CREATE INDEX idx_repair_progress_worker_id ON repair_progress(worker_id);
CREATE INDEX idx_repair_records_order_id ON repair_records(order_id);
CREATE INDEX idx_repair_records_worker_id ON repair_records(worker_id);
CREATE INDEX idx_file_uploads_related ON file_uploads(related_type, related_id);
CREATE INDEX idx_worker_salary_config_worker_id ON worker_salary_config(worker_id);
CREATE INDEX idx_monthly_salary_worker_id ON monthly_salary(worker_id);
CREATE INDEX idx_monthly_salary_year_month ON monthly_salary(year, month);
CREATE INDEX idx_salary_details_monthly_salary_id ON salary_details(monthly_salary_id);
CREATE INDEX idx_attendance_records_worker_id ON attendance_records(worker_id);
CREATE INDEX idx_attendance_records_date ON attendance_records(date);

-- 插入默认薪资配置
INSERT IGNORE INTO worker_salary_config (worker_id, base_salary, commission_rate, performance_bonus_rate, attendance_bonus)
SELECT id, 3000.00, 0.1500, 0.0500, 200.00 FROM workers;

-- 更新现有工单的服务费
UPDATE orders SET service_fee =
  CASE appliance_type
    WHEN 'air_conditioner' THEN 150.00
    WHEN 'washing_machine' THEN 120.00
    WHEN 'refrigerator' THEN 180.00
    WHEN 'water_heater' THEN 100.00
    WHEN 'television' THEN 80.00
    WHEN 'microwave' THEN 60.00
    ELSE 100.00
  END
WHERE service_fee = 0.00 OR service_fee IS NULL;
