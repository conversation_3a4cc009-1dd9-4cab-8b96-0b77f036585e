-- 工人薪资计算相关表

USE appliance_repair;

-- 工人薪资配置表
CREATE TABLE IF NOT EXISTS worker_salary_config (
  id INT PRIMARY KEY AUTO_INCREMENT,
  worker_id INT NOT NULL,
  base_salary DECIMAL(10,2) DEFAULT 3000.00 COMMENT '基础工资',
  commission_rate DECIMAL(5,4) DEFAULT 0.1500 COMMENT '提成比例',
  performance_bonus_rate DECIMAL(5,4) DEFAULT 0.0500 COMMENT '绩效奖金比例',
  attendance_bonus DECIMAL(10,2) DEFAULT 200.00 COMMENT '全勤奖',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (worker_id) REFERENCES workers(id) ON DELETE CASCADE,
  UNIQUE KEY unique_worker (worker_id)
) COMMENT '工人薪资配置表';

-- 月度薪资记录表
CREATE TABLE IF NOT EXISTS monthly_salary (
  id INT PRIMARY KEY AUTO_INCREMENT,
  worker_id INT NOT NULL,
  year INT NOT NULL COMMENT '年份',
  month INT NOT NULL COMMENT '月份',
  base_salary DECIMAL(10,2) NOT NULL COMMENT '基础工资',
  commission_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '提成金额',
  performance_bonus DECIMAL(10,2) DEFAULT 0.00 COMMENT '绩效奖金',
  attendance_bonus DECIMAL(10,2) DEFAULT 0.00 COMMENT '全勤奖',
  overtime_pay DECIMAL(10,2) DEFAULT 0.00 COMMENT '加班费',
  deductions DECIMAL(10,2) DEFAULT 0.00 COMMENT '扣款',
  total_salary DECIMAL(10,2) NOT NULL COMMENT '总薪资',
  completed_orders INT DEFAULT 0 COMMENT '完成工单数',
  total_revenue DECIMAL(10,2) DEFAULT 0.00 COMMENT '总收入',
  avg_rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均评分',
  attendance_days INT DEFAULT 0 COMMENT '出勤天数',
  status ENUM('draft', 'confirmed', 'paid') DEFAULT 'draft' COMMENT '状态',
  calculated_at TIMESTAMP NULL COMMENT '计算时间',
  confirmed_at TIMESTAMP NULL COMMENT '确认时间',
  paid_at TIMESTAMP NULL COMMENT '发放时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (worker_id) REFERENCES workers(id) ON DELETE CASCADE,
  UNIQUE KEY unique_worker_month (worker_id, year, month)
) COMMENT '月度薪资记录表';

-- 薪资明细表
CREATE TABLE IF NOT EXISTS salary_details (
  id INT PRIMARY KEY AUTO_INCREMENT,
  monthly_salary_id INT NOT NULL,
  order_id INT NULL COMMENT '关联工单ID',
  item_type ENUM('base', 'commission', 'performance', 'attendance', 'overtime', 'deduction') NOT NULL COMMENT '项目类型',
  item_name VARCHAR(100) NOT NULL COMMENT '项目名称',
  amount DECIMAL(10,2) NOT NULL COMMENT '金额',
  description TEXT COMMENT '说明',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (monthly_salary_id) REFERENCES monthly_salary(id) ON DELETE CASCADE,
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL
) COMMENT '薪资明细表';

-- 考勤记录表
CREATE TABLE IF NOT EXISTS attendance_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  worker_id INT NOT NULL,
  date DATE NOT NULL COMMENT '日期',
  check_in_time TIME COMMENT '签到时间',
  check_out_time TIME COMMENT '签退时间',
  work_hours DECIMAL(4,2) DEFAULT 0.00 COMMENT '工作小时数',
  overtime_hours DECIMAL(4,2) DEFAULT 0.00 COMMENT '加班小时数',
  status ENUM('present', 'absent', 'late', 'leave') DEFAULT 'present' COMMENT '状态',
  notes TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (worker_id) REFERENCES workers(id) ON DELETE CASCADE,
  UNIQUE KEY unique_worker_date (worker_id, date)
) COMMENT '考勤记录表';

-- 插入默认薪资配置
INSERT IGNORE INTO worker_salary_config (worker_id, base_salary, commission_rate, performance_bonus_rate, attendance_bonus)
SELECT id, 3000.00, 0.1500, 0.0500, 200.00 FROM workers;

-- 更新workers表，添加薪资相关字段
ALTER TABLE workers 
ADD COLUMN IF NOT EXISTS hourly_rate DECIMAL(8,2) DEFAULT 50.00 COMMENT '小时工资',
ADD COLUMN IF NOT EXISTS overtime_rate DECIMAL(8,2) DEFAULT 75.00 COMMENT '加班小时工资';

-- 为orders表添加服务费字段（如果不存在）
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS service_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '服务费用';

-- 更新现有工单的服务费（示例数据）
UPDATE orders SET service_fee = 
  CASE appliance_type
    WHEN 'air_conditioner' THEN 150.00
    WHEN 'washing_machine' THEN 120.00
    WHEN 'refrigerator' THEN 180.00
    WHEN 'water_heater' THEN 100.00
    WHEN 'television' THEN 80.00
    WHEN 'microwave' THEN 60.00
    ELSE 100.00
  END
WHERE service_fee = 0.00;
