-- 薪资相关测试数据
USE appliance_repair;

-- 1. 插入工人薪资配置数据
INSERT INTO worker_salary_config (worker_id, base_salary, commission_rate, performance_bonus_rate, attendance_bonus) VALUES
(1, 3500.00, 0.1500, 0.0500, 300.00),  -- 张师傅：基础工资3500，提成15%，绩效5%，全勤奖300
(2, 3200.00, 0.1200, 0.0400, 250.00)   -- 王师傅：基础工资3200，提成12%，绩效4%，全勤奖250
ON DUPLICATE KEY UPDATE 
  base_salary = VALUES(base_salary),
  commission_rate = VALUES(commission_rate),
  performance_bonus_rate = VALUES(performance_bonus_rate),
  attendance_bonus = VALUES(attendance_bonus);

-- 2. 更新工人表的小时工资
UPDATE workers SET 
  hourly_rate = 60.00,
  overtime_rate = 90.00
WHERE id = 1;

UPDATE workers SET 
  hourly_rate = 55.00,
  overtime_rate = 82.50
WHERE id = 2;

-- 3. 更新现有工单的服务费用
UPDATE orders SET service_fee = 
  CASE 
    WHEN appliance_type = 'air_conditioner' THEN 180.00
    WHEN appliance_type = 'washing_machine' THEN 150.00
    WHEN appliance_type = 'refrigerator' THEN 200.00
    WHEN appliance_type = 'water_heater' THEN 120.00
    WHEN appliance_type = 'television' THEN 100.00
    WHEN appliance_type = 'microwave' THEN 80.00
    ELSE 120.00
  END
WHERE service_fee = 0.00 OR service_fee IS NULL;

-- 4. 插入考勤记录数据（最近3个月）
-- 2024年1月考勤记录
INSERT INTO attendance_records (worker_id, date, check_in_time, check_out_time, work_hours, overtime_hours, status) VALUES
-- 张师傅1月考勤（工作22天）
(1, '2024-01-02', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-03', '08:45:00', '18:00:00', 8.0, 0.5, 'present'),
(1, '2024-01-04', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-05', '08:30:00', '19:00:00', 8.0, 2.0, 'present'),
(1, '2024-01-08', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-09', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-10', '08:30:00', '18:30:00', 8.0, 1.0, 'present'),
(1, '2024-01-11', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-12', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-15', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-16', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-17', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-18', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-19', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-22', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-23', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-24', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-25', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-26', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-29', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-30', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-01-31', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),

-- 王师傅1月考勤（工作20天，有2天请假）
(2, '2024-01-02', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-03', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-04', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-05', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-08', NULL, NULL, 0.0, 0.0, 'leave'),
(2, '2024-01-09', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-10', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-11', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-12', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-15', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-16', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-17', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-18', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-19', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-22', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-23', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-24', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-25', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-26', NULL, NULL, 0.0, 0.0, 'leave'),
(2, '2024-01-29', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-30', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-01-31', '09:00:00', '18:00:00', 8.0, 0.0, 'present');

-- 5. 插入月度薪资记录
-- 2024年1月薪资记录
INSERT INTO monthly_salary (
  worker_id, year, month, base_salary, commission_amount, performance_bonus,
  attendance_bonus, overtime_pay, total_salary, completed_orders,
  total_revenue, avg_rating, attendance_days, status, calculated_at
) VALUES
-- 张师傅1月薪资
(1, 2024, 1, 3500.00, 450.00, 150.00, 300.00, 225.00, 4625.00, 
 8, 3000.00, 4.7, 22, 'paid', '2024-02-01 10:00:00'),

-- 王师傅1月薪资
(2, 2024, 1, 3200.00, 288.00, 0.00, 0.00, 0.00, 3488.00, 
 6, 2400.00, 4.3, 20, 'paid', '2024-02-01 10:00:00');

-- 6. 插入薪资明细记录
-- 张师傅1月薪资明细
INSERT INTO salary_details (monthly_salary_id, item_type, item_name, amount, description) VALUES
(1, 'base', '基础工资', 3500.00, '2024年1月基础工资'),
(1, 'commission', '提成收入', 450.00, '完成8单工单，营收3000元，提成15%'),
(1, 'performance', '绩效奖金', 150.00, '平均评分4.7分，绩效奖金5%'),
(1, 'attendance', '全勤奖', 300.00, '出勤22天，获得全勤奖'),
(1, 'overtime', '加班费', 225.00, '加班2.5小时，90元/小时');

-- 王师傅1月薪资明细
INSERT INTO salary_details (monthly_salary_id, item_type, item_name, amount, description) VALUES
(2, 'base', '基础工资', 3200.00, '2024年1月基础工资'),
(2, 'commission', '提成收入', 288.00, '完成6单工单，营收2400元，提成12%');

-- 7. 插入更多历史工单数据（用于薪资计算）
INSERT INTO orders (
  customer_id, worker_id, appliance_type, brand_model, problem_description,
  contact_name, contact_phone, address, purchase_date, preferred_date,
  preferred_time, urgency, status, service_fee, rating, comment,
  created_at, accepted_at, completed_at
) VALUES
-- 张师傅完成的工单
(1, 1, 'air_conditioner', '美的KFR-26GW', '空调不制冷', '张三', '13900000001', 
 '北京市朝阳区xxx小区1号楼101', '2023-05-15', '2024-01-15', 'morning', 'high', 
 'completed', 180.00, 5, '服务很好，师傅很专业', 
 '2024-01-14 09:00:00', '2024-01-14 10:00:00', '2024-01-15 15:30:00'),

(2, 1, 'washing_machine', 'TCL XQG75-P300B', '洗衣机漏水', '李四', '13900000002', 
 '北京市海淀区xxx小区2号楼202', '2023-08-20', '2024-01-18', 'afternoon', 'medium', 
 'completed', 150.00, 4, '问题解决了，谢谢师傅', 
 '2024-01-17 14:00:00', '2024-01-17 15:00:00', '2024-01-18 17:00:00'),

(1, 1, 'refrigerator', '海尔BCD-470WDPG', '冰箱噪音大', '王五', '13900000003', 
 '北京市西城区xxx小区3号楼303', '2022-12-10', '2024-01-22', 'evening', 'low', 
 'completed', 200.00, 5, '师傅很细心，问题彻底解决', 
 '2024-01-21 16:00:00', '2024-01-21 17:00:00', '2024-01-22 19:30:00'),

-- 王师傅完成的工单
(2, 2, 'water_heater', '万和JSQ24-12ET10', '热水器不点火', '赵六', '13900000004', 
 '北京市东城区xxx小区4号楼404', '2023-03-25', '2024-01-20', 'morning', 'high', 
 'completed', 120.00, 4, '师傅很准时，服务态度好', 
 '2024-01-19 08:00:00', '2024-01-19 09:00:00', '2024-01-20 11:00:00'),

(1, 2, 'television', '小米L55M5-4A', '电视无法开机', '孙七', '13900000005', 
 '北京市丰台区xxx小区5号楼505', '2023-11-15', '2024-01-25', 'afternoon', 'medium', 
 'completed', 100.00, 4, '修好了，感谢师傅', 
 '2024-01-24 13:00:00', '2024-01-24 14:00:00', '2024-01-25 16:00:00');

-- 8. 更新工人评分
UPDATE workers SET rating = (
  SELECT AVG(rating) FROM orders WHERE worker_id = workers.id AND rating IS NOT NULL
) WHERE id IN (1, 2);

-- 9. 插入当前月份的考勤记录（2024年12月，部分数据）
INSERT INTO attendance_records (worker_id, date, check_in_time, check_out_time, work_hours, overtime_hours, status) VALUES
-- 张师傅12月考勤（到目前为止）
(1, '2024-12-02', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-12-03', '08:30:00', '18:00:00', 8.0, 0.5, 'present'),
(1, '2024-12-04', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-12-05', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-12-06', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-12-09', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-12-10', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-12-11', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-12-12', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),
(1, '2024-12-13', '08:30:00', '17:30:00', 8.0, 0.0, 'present'),

-- 王师傅12月考勤（到目前为止）
(2, '2024-12-02', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-12-03', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-12-04', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-12-05', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-12-06', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-12-09', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-12-10', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-12-11', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-12-12', '09:00:00', '18:00:00', 8.0, 0.0, 'present'),
(2, '2024-12-13', '09:00:00', '18:00:00', 8.0, 0.0, 'present');

-- 显示插入结果
SELECT '薪资配置数据插入完成' as message;
SELECT worker_id, base_salary, commission_rate FROM worker_salary_config;

SELECT '考勤记录插入完成' as message;
SELECT worker_id, COUNT(*) as attendance_days FROM attendance_records WHERE YEAR(date) = 2024 AND MONTH(date) = 1 GROUP BY worker_id;

SELECT '月度薪资记录插入完成' as message;
SELECT worker_id, year, month, total_salary, status FROM monthly_salary;

SELECT '工单服务费更新完成' as message;
SELECT appliance_type, AVG(service_fee) as avg_service_fee FROM orders GROUP BY appliance_type;
