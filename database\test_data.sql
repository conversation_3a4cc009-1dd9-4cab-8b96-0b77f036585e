-- 永盛制冷维修有限公司-家电售卖与维修测试数据

USE appliance_repair;

-- 插入测试管理员 (密码: admin123)
INSERT INTO admins (username, password, name, phone, role) VALUES
('admin', '$2a$10$Kh6vmPQXX9dHy9t4wyITf.kfsW61buN.8rky627A3omApQA7YQcva', '系统管理员', '13800000000', 'super_admin');

-- 插入测试工人 (密码: worker123)
INSERT INTO workers (username, password, name, phone, skills, commission_rate) VALUES
('worker1', '$2a$10$30M6S3S1zJMVjZtAFsipKOzFyymrZKYi5FT5OTQPjFwqNmQ6NPJsO', '张师傅', '13800000001', '空调维修,洗衣机维修', 0.15),
('worker2', '$2a$10$30M6S3S1zJMVjZtAFsipKOzFyymrZKYi5FT5OTQPjFwqNmQ6NPJsO', '李师傅', '13800000002', '冰箱维修,电视维修', 0.12),
('worker3', '$2a$10$30M6S3S1zJMVjZtAFsipKOzFyymrZKYi5FT5OTQPjFwqNmQ6NPJsO', '王师傅', '13800000003', '热水器维修,微波炉维修', 0.10);

-- 插入测试客户 (密码: customer123)
INSERT INTO customers (username, password, name, phone, address) VALUES
('customer1', '$2a$10$JCFXyA.BkTYh/N2pS6F.QO269dGqXdY60ZJmKBHOlzp5HyniBC6za', '张三', '13900000001', '北京市朝阳区xxx小区1号楼101'),
('customer2', '$2a$10$JCFXyA.BkTYh/N2pS6F.QO269dGqXdY60ZJmKBHOlzp5HyniBC6za', '李四', '13900000002', '北京市海淀区xxx小区2号楼202'),
('customer3', '$2a$10$JCFXyA.BkTYh/N2pS6F.QO269dGqXdY60ZJmKBHOlzp5HyniBC6za', '王五', '13900000003', '北京市西城区xxx小区3号楼303');

-- 插入测试工单
INSERT INTO orders (
  customer_id, appliance_type, brand_model, problem_description,
  contact_name, contact_phone, address, purchase_date,
  preferred_date, preferred_time, urgency, remarks, status
) VALUES
(1, 'air_conditioner', '格力KFR-35GW', '空调制冷效果差，开机后房间温度下降很慢，怀疑是制冷剂不足。',
 '张三', '13900000001', '北京市朝阳区xxx小区1号楼101', '2022-06-10',
 '2024-01-20', 'afternoon', 'high', '家里有老人，希望师傅轻声一些', 'pending'),

(2, 'washing_machine', '海尔XQG80-B12866', '洗衣机不能启动，按电源键没有反应，指示灯不亮。已经检查过电源插座，确认有电。',
 '李四', '13900000002', '北京市海淀区xxx小区2号楼202', '2023-01-15',
 '2024-01-21', 'morning', 'medium', '', 'pending'),

(3, 'refrigerator', '美的BCD-215TM', '冰箱冷藏室不制冷，冷冻室正常工作。',
 '王五', '13900000003', '北京市西城区xxx小区3号楼303', '2021-03-20',
 '2024-01-19', 'evening', 'low', '', 'in_progress'),

(1, 'washing_machine', '小天鹅TB80V23H', '洗衣机甩干时噪音很大，有异响，怀疑是轴承问题。',
 '张三', '13900000001', '北京市朝阳区xxx小区1号楼101', '2023-05-15',
 '2024-01-22', 'morning', 'medium', '', 'accepted'),

(2, 'water_heater', '美的F6021-X1(S)', '热水器不加热，显示屏正常，但水温一直是冷的。',
 '李四', '13900000002', '北京市海淀区xxx小区2号楼202', '2023-03-10',
 '2024-01-23', 'afternoon', 'high', '', 'completed');

-- 更新工单状态和工人分配
UPDATE orders SET worker_id = 1, accepted_at = '2024-01-18 15:00:00' WHERE id = 3;
UPDATE orders SET worker_id = 1, accepted_at = '2024-01-21 09:00:00' WHERE id = 4;
UPDATE orders SET worker_id = 2, accepted_at = '2024-01-22 10:00:00', completed_at = '2024-01-22 16:30:00', rating = 5, comment = '师傅很专业，服务态度好，问题解决得很彻底' WHERE id = 5;

-- 插入维修进度记录
INSERT INTO repair_progress (order_id, worker_id, title, description, estimated_completion, created_at) VALUES
(3, 1, '师傅已接单', '张师傅已接受您的维修请求，将在预约时间上门服务', '2024-01-19 18:00:00', '2024-01-18 15:00:00'),
(3, 1, '开始维修', '师傅已到达现场，开始检查冰箱问题', '2024-01-19 20:00:00', '2024-01-19 18:30:00'),
(3, 1, '问题诊断', '经检查发现是温控器故障，需要更换配件', '2024-01-20 14:00:00', '2024-01-19 19:15:00');

-- 插入完成的维修记录
INSERT INTO repair_records (
  order_id, worker_id, result, cause, solution, replaced_parts,
  cost, summary, suggestions, warranty_period, repair_time
) VALUES
(5, 2, 'success', '热水器加热管老化，导致无法正常加热',
 '更换了新的加热管，并清洁了内胆水垢', '加热管 x1, 密封圈 x2',
 180.00, '成功更换加热管，热水器恢复正常工作，水温稳定',
 '建议每年清洁一次内胆，避免水垢堆积影响加热效果', 6, '2024-01-22 16:30:00');
