/** @deprecated */
export const builtinRules: Map<string, import("./index.js").Rule.RuleModule>;
/** @deprecated */
export class FileEnumerator {
    constructor(
        params?: {
            cwd?: string;
            configArrayFactory?: any;
            extensions?: any;
            globInputPaths?: boolean;
            errorOnUnmatchedPattern?: boolean;
            ignore?: boolean;
        },
    );
    isTargetPath(filePath: string, providedConfig?: any): boolean;
    iterateFiles(
        patternOrPatterns: string | string[],
    ): IterableIterator<{ config: any; filePath: string; ignored: boolean }>;
}
