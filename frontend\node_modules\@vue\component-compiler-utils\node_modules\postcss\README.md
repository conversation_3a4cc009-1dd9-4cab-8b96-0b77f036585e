# PostCSS [![Gitter][chat-img]][chat]

<img align="right" width="95" height="95"
     alt="Philosopher’s stone, logo of PostCSS"
     src="http://postcss.github.io/postcss/logo.svg">

[chat-img]: https://img.shields.io/badge/Gitter-Join_the_PostCSS_chat-brightgreen.svg
[chat]:     https://gitter.im/postcss/postcss

PostCSS is a tool for transforming styles with JS plugins.
These plugins can lint your CSS, support variables and mixins,
transpile future CSS syntax, inline images, and more.

PostCSS is used by industry leaders including Wikipedia, Twitter, Alibaba,
and JetBrains. The [Autoprefixer] PostCSS plugin is one of the most popular
CSS processors.

PostCSS takes a CSS file and provides an API to analyze and modify its rules
(by transforming them into an [Abstract Syntax Tree]).
This API can then be used by [plugins] to do a lot of useful things,
e.g. to find errors automatically insert vendor prefixes.

**Support / Discussion:** [Gitter](https://gitter.im/postcss/postcss)<br>
**Twitter account:**      [@postcss](https://twitter.com/postcss)<br>
**VK.com page:**          [postcss](https://vk.com/postcss)<br>
**中文翻译**:              [`README-cn.md`](./README-cn.md)

For PostCSS commercial support (consulting, improving the front-end culture
of your company, PostCSS plugins), contact [Evil Martians]
at <<EMAIL>>.

[Abstract Syntax Tree]: https://en.wikipedia.org/wiki/Abstract_syntax_tree
[Evil Martians]:        https://evilmartians.com/?utm_source=postcss
[Autoprefixer]:         https://github.com/postcss/autoprefixer
[plugins]:              https://github.com/postcss/postcss#plugins

<a href="https://evilmartians.com/?utm_source=postcss">
  <img src="https://evilmartians.com/badges/sponsored-by-evil-martians.svg"
       alt="Sponsored by Evil Martians" width="236" height="54">
</a>

## Docs
Read **[full docs](https://github.com/postcss/postcss#readme)** on GitHub.
