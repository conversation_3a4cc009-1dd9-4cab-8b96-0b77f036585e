{"version": 3, "file": "additionalProperties.js", "sourceRoot": "", "sources": ["../../../lib/vocabularies/applicator/additionalProperties.ts"], "names": [], "mappings": ";;AAOA,kCAAsE;AACtE,mDAAiE;AACjE,+CAAmC;AAEnC,6CAA0E;AAQ1E,MAAM,KAAK,GAA2B;IACpC,OAAO,EAAE,qCAAqC;IAC9C,MAAM,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE,CAAC,IAAA,WAAC,EAAA,wBAAwB,MAAM,CAAC,kBAAkB,GAAG;CAC5E,CAAA;AAED,MAAM,GAAG,GAAmD;IAC1D,OAAO,EAAE,sBAAsB;IAC/B,IAAI,EAAE,CAAC,QAAQ,CAAC;IAChB,UAAU,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;IACjC,cAAc,EAAE,IAAI;IACpB,WAAW,EAAE,IAAI;IACjB,KAAK;IACL,IAAI,CAAC,GAAG;QACN,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;QAC5D,wBAAwB;QACxB,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAC3D,MAAM,EAAC,SAAS,EAAE,IAAI,EAAC,GAAG,EAAE,CAAA;QAC5B,EAAE,CAAC,KAAK,GAAG,IAAI,CAAA;QACf,IAAI,IAAI,CAAC,gBAAgB,KAAK,KAAK,IAAI,IAAA,wBAAiB,EAAC,EAAE,EAAE,MAAM,CAAC;YAAE,OAAM;QAC5E,MAAM,KAAK,GAAG,IAAA,0BAAmB,EAAC,YAAY,CAAC,UAAU,CAAC,CAAA;QAC1D,MAAM,QAAQ,GAAG,IAAA,0BAAmB,EAAC,YAAY,CAAC,iBAAiB,CAAC,CAAA;QACpE,yBAAyB,EAAE,CAAA;QAC3B,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,SAAS,QAAQ,eAAC,CAAC,MAAM,EAAE,CAAC,CAAA;QAEvC,SAAS,yBAAyB;YAChC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,GAAS,EAAE,EAAE;gBACnC,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM;oBAAE,sBAAsB,CAAC,GAAG,CAAC,CAAA;;oBAC7D,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;YACnE,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,SAAS,YAAY,CAAC,GAAS;YAC7B,IAAI,WAAiB,CAAA;YACrB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,gDAAgD;gBAChD,MAAM,WAAW,GAAG,IAAA,qBAAc,EAAC,EAAE,EAAE,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAA;gBAC7E,WAAW,GAAG,IAAA,oBAAa,EAAC,GAAG,EAAE,WAAmB,EAAE,GAAG,CAAC,CAAA;YAC5D,CAAC;iBAAM,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACxB,WAAW,GAAG,IAAA,YAAE,EAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA;YAC3D,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,aAAG,CAAA;YACnB,CAAC;YACD,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACpB,WAAW,GAAG,IAAA,YAAE,EAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAA;YAC9F,CAAC;YACD,OAAO,IAAA,aAAG,EAAC,WAAW,CAAC,CAAA;QACzB,CAAC;QAED,SAAS,gBAAgB,CAAC,GAAS;YACjC,GAAG,CAAC,IAAI,CAAC,IAAA,WAAC,EAAA,UAAU,IAAI,IAAI,GAAG,GAAG,CAAC,CAAA;QACrC,CAAC;QAED,SAAS,sBAAsB,CAAC,GAAS;YACvC,IAAI,IAAI,CAAC,gBAAgB,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,CAAC;gBACnF,gBAAgB,CAAC,GAAG,CAAC,CAAA;gBACrB,OAAM;YACR,CAAC;YAED,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;gBACrB,GAAG,CAAC,SAAS,CAAC,EAAC,kBAAkB,EAAE,GAAG,EAAC,CAAC,CAAA;gBACxC,GAAG,CAAC,KAAK,EAAE,CAAA;gBACX,IAAI,CAAC,SAAS;oBAAE,GAAG,CAAC,KAAK,EAAE,CAAA;gBAC3B,OAAM;YACR,CAAC;YAED,IAAI,OAAO,MAAM,IAAI,QAAQ,IAAI,CAAC,IAAA,wBAAiB,EAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;gBAChE,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC/B,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;oBACxC,qBAAqB,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;oBACxC,GAAG,CAAC,EAAE,CAAC,IAAA,aAAG,EAAC,KAAK,CAAC,EAAE,GAAG,EAAE;wBACtB,GAAG,CAAC,KAAK,EAAE,CAAA;wBACX,gBAAgB,CAAC,GAAG,CAAC,CAAA;oBACvB,CAAC,CAAC,CAAA;gBACJ,CAAC;qBAAM,CAAC;oBACN,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;oBACjC,IAAI,CAAC,SAAS;wBAAE,GAAG,CAAC,EAAE,CAAC,IAAA,aAAG,EAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAA;gBACvD,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,qBAAqB,CAAC,GAAS,EAAE,KAAW,EAAE,MAAc;YACnE,MAAM,SAAS,GAAkB;gBAC/B,OAAO,EAAE,sBAAsB;gBAC/B,QAAQ,EAAE,GAAG;gBACb,YAAY,EAAE,WAAI,CAAC,GAAG;aACvB,CAAA;YACD,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;gBACrB,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE;oBACvB,aAAa,EAAE,IAAI;oBACnB,YAAY,EAAE,KAAK;oBACnB,SAAS,EAAE,KAAK;iBACjB,CAAC,CAAA;YACJ,CAAC;YACD,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;QACjC,CAAC;IACH,CAAC;CACF,CAAA;AAED,kBAAe,GAAG,CAAA"}