[{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constant": "APPVEYOR", "env": "APPVEYOR", "pr": "APPVEYOR_PULL_REQUEST_NUMBER"}, {"name": "Bamboo", "constant": "BAMBOO", "env": "bamboo_planKey"}, {"name": "Bitbucket Pipelines", "constant": "BITBUCKET", "env": "BITBUCKET_COMMIT"}, {"name": "Bitrise", "constant": "BITRISE", "env": "BITRISE_IO", "pr": "BITRISE_PULL_REQUEST"}, {"name": "<PERSON>", "constant": "BUDDY", "env": "BUDDY_WORKSPACE_ID", "pr": "BUDDY_EXECUTION_PULL_REQUEST_ID"}, {"name": "Buildkite", "constant": "BUILDKITE", "env": "BUILDKITE", "pr": {"env": "BUILDKITE_PULL_REQUEST", "ne": "false"}}, {"name": "CircleCI", "constant": "CIRCLE", "env": "CIRCLECI", "pr": "CIRCLE_PULL_REQUEST"}, {"name": "Cirrus CI", "constant": "CIRRUS", "env": "CIRRUS_CI", "pr": "CIRRUS_PR"}, {"name": "AWS CodeBuild", "constant": "CODEBUILD", "env": "CODEBUILD_BUILD_ARN"}, {"name": "Codeship", "constant": "CODESHIP", "env": {"CI_NAME": "codeship"}}, {"name": "Drone", "constant": "DRONE", "env": "DRONE", "pr": {"DRONE_BUILD_EVENT": "pull_request"}}, {"name": "<PERSON><PERSON><PERSON>", "constant": "DSARI", "env": "DSARI"}, {"name": "GitLab CI", "constant": "GITLAB", "env": "GITLAB_CI"}, {"name": "GoCD", "constant": "GOCD", "env": "GO_PIPELINE_LABEL"}, {"name": "<PERSON>", "constant": "HUDSON", "env": "HUDSON_URL"}, {"name": "<PERSON>", "constant": "JENKINS", "env": ["JENKINS_URL", "BUILD_ID"], "pr": {"any": ["ghprbPullId", "CHANGE_ID"]}}, {"name": "Magnum CI", "constant": "MAGNUM", "env": "MAGNUM"}, {"name": "Sail CI", "constant": "SAIL", "env": "SAILCI", "pr": "SAIL_PULL_REQUEST_NUMBER"}, {"name": "Semaphore", "constant": "SEMAPHORE", "env": "SEMAPHORE", "pr": "PULL_REQUEST_NUMBER"}, {"name": "Shippable", "constant": "SHIPPABLE", "env": "SHIPPABLE", "pr": {"IS_PULL_REQUEST": "true"}}, {"name": "Solano CI", "constant": "SOLANO", "env": "TDDIUM", "pr": "TDDIUM_PR_ID"}, {"name": "Strider CD", "constant": "STRIDER", "env": "STRIDER"}, {"name": "TaskCluster", "constant": "TASKCLUSTER", "env": ["TASK_ID", "RUN_ID"]}, {"name": "Solano CI", "constant": "TDDIUM", "env": "TDDIUM", "pr": "TDDIUM_PR_ID", "deprecated": true}, {"name": "TeamCity", "constant": "TEAMCITY", "env": "TEAMCITY_VERSION"}, {"name": "Team Foundation Server", "constant": "TFS", "env": "TF_BUILD"}, {"name": "Travis CI", "constant": "TRAVIS", "env": "TRAVIS", "pr": {"env": "TRAVIS_PULL_REQUEST", "ne": "false"}}]