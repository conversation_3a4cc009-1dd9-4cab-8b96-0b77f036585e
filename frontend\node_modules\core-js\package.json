{"name": "core-js", "description": "Standard library", "version": "2.6.12", "repository": {"type": "git", "url": "https://github.com/zloirock/core-js.git"}, "main": "index.js", "devDependencies": {"LiveScript": "1.3.x", "es-observable-tests": "0.2.x", "eslint": "4.19.x", "eslint-plugin-import": "2.12.x", "grunt": "^1.0.2", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-uglify": "3.3.x", "grunt-contrib-watch": "^1.0.0", "grunt-karma": "^2.0.0", "grunt-livescript": "0.6.x", "karma": "^2.0.0", "karma-qunit": "^2.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "karma-ie-launcher": "^1.0.0", "karma-phantomjs-launcher": "1.0.x", "phantomjs-prebuilt": "2.1.x", "promises-aplus-tests": "^2.1.2", "qunit": "2.6.x", "temp": "^0.8.3", "webpack": "^3.11.0"}, "scripts": {"grunt": "grunt", "lint": "eslint ./", "promises-tests": "promises-aplus-tests tests/promises-aplus/adapter", "observables-tests": "node tests/observables/adapter && node tests/observables/adapter-library", "test": "npm run grunt clean copy && npm run lint && npm run grunt livescript client karma:default && npm run grunt library karma:library && npm run promises-tests && npm run observables-tests && lsc tests/commonjs", "postinstall": "node -e \"try{require('./postinstall')}catch(e){}\""}, "license": "MIT", "keywords": ["ES3", "ES5", "ES6", "ES7", "ES2015", "ES2016", "ES2017", "ECMAScript 3", "ECMAScript 5", "ECMAScript 6", "ECMAScript 7", "ECMAScript 2015", "ECMAScript 2016", "ECMAScript 2017", "Harmony", "<PERSON><PERSON><PERSON>", "Map", "Set", "WeakMap", "WeakSet", "Promise", "Symbol", "TypedArray", "setImmediate", "Dict", "polyfill", "shim"]}