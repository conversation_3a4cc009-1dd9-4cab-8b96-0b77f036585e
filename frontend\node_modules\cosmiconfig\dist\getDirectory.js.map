{"version": 3, "sources": ["../src/getDirectory.ts"], "names": ["getDirectory", "filepath", "filePathIsDirectory", "directory", "path", "dirname", "getDirectorySync"], "mappings": ";;;;;;;;AAAA;;AACA;;;;AAEA,eAAeA,YAAf,CAA4BC,QAA5B,EAA+D;AAC7D,QAAMC,mBAAmB,GAAG,MAAM,2BAAYD,QAAZ,CAAlC;;AAEA,MAAIC,mBAAmB,KAAK,IAA5B,EAAkC;AAChC,WAAOD,QAAP;AACD;;AAED,QAAME,SAAS,GAAGC,cAAKC,OAAL,CAAaJ,QAAb,CAAlB;;AAEA,SAAOE,SAAP;AACD;;AAED,SAASG,gBAAT,CAA0BL,QAA1B,EAAoD;AAClD,QAAMC,mBAAmB,GAAG,+BAAgBD,QAAhB,CAA5B;;AAEA,MAAIC,mBAAmB,KAAK,IAA5B,EAAkC;AAChC,WAAOD,QAAP;AACD;;AAED,QAAME,SAAS,GAAGC,cAAKC,OAAL,CAAaJ,QAAb,CAAlB;;AAEA,SAAOE,SAAP;AACD", "sourcesContent": ["import path from 'path';\nimport { isDirectory, isDirectorySync } from 'path-type';\n\nasync function getDirectory(filepath: string): Promise<string> {\n  const filePathIsDirectory = await isDirectory(filepath);\n\n  if (filePathIsDirectory === true) {\n    return filepath;\n  }\n\n  const directory = path.dirname(filepath);\n\n  return directory;\n}\n\nfunction getDirectorySync(filepath: string): string {\n  const filePathIsDirectory = isDirectorySync(filepath);\n\n  if (filePathIsDirectory === true) {\n    return filepath;\n  }\n\n  const directory = path.dirname(filepath);\n\n  return directory;\n}\n\nexport { getDirectory, getDirectorySync };\n"], "file": "getDirectory.js"}