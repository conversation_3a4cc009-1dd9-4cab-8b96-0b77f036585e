"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const select_1 = __importDefault(require("../definitions/select"));
const select = (ajv, opts) => {
    (0, select_1.default)(opts).forEach((d) => ajv.addKeyword(d));
    return ajv;
};
exports.default = select;
module.exports = select;
//# sourceMappingURL=select.js.map