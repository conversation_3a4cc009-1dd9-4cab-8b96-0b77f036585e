!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).csso={})}(this,(function(e){"use strict";"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var t=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e,t){e.exports=function(){function e(e){return{prev:null,next:null,data:e}}function t(e,t,n){var a;return null!==r?(a=r,r=r.cursor,a.prev=t,a.next=n,a.cursor=e.cursor):a={prev:t,next:n,cursor:e.cursor},e.cursor=a,a}function n(e){var t=e.cursor;e.cursor=t.cursor,t.prev=null,t.next=null,t.cursor=r,r=t}var r=null,a=function(){this.cursor=null,this.head=null,this.tail=null};a.createItem=e,a.prototype.createItem=e,a.prototype.updateCursors=function(e,t,n,r){for(var a=this.cursor;null!==a;)a.prev===e&&(a.prev=t),a.next===n&&(a.next=r),a=a.cursor},a.prototype.getSize=function(){for(var e=0,t=this.head;t;)e++,t=t.next;return e},a.prototype.fromArray=function(t){var n=null;this.head=null;for(var r=0;r<t.length;r++){var a=e(t[r]);null!==n?n.next=a:this.head=a,a.prev=n,n=a}return this.tail=n,this},a.prototype.toArray=function(){for(var e=this.head,t=[];e;)t.push(e.data),e=e.next;return t},a.prototype.toJSON=a.prototype.toArray,a.prototype.isEmpty=function(){return null===this.head},a.prototype.first=function(){return this.head&&this.head.data},a.prototype.last=function(){return this.tail&&this.tail.data},a.prototype.each=function(e,r){var a;void 0===r&&(r=this);for(var i=t(this,null,this.head);null!==i.next;)a=i.next,i.next=a.next,e.call(r,a.data,a,this);n(this)},a.prototype.forEach=a.prototype.each,a.prototype.eachRight=function(e,r){var a;void 0===r&&(r=this);for(var i=t(this,this.tail,null);null!==i.prev;)a=i.prev,i.prev=a.prev,e.call(r,a.data,a,this);n(this)},a.prototype.forEachRight=a.prototype.eachRight,a.prototype.reduce=function(e,r,a){var i;void 0===a&&(a=this);for(var o=t(this,null,this.head),s=r;null!==o.next;)i=o.next,o.next=i.next,s=e.call(a,s,i.data,i,this);return n(this),s},a.prototype.reduceRight=function(e,r,a){var i;void 0===a&&(a=this);for(var o=t(this,this.tail,null),s=r;null!==o.prev;)i=o.prev,o.prev=i.prev,s=e.call(a,s,i.data,i,this);return n(this),s},a.prototype.nextUntil=function(e,r,a){if(null!==e){var i;void 0===a&&(a=this);for(var o=t(this,null,e);null!==o.next&&(i=o.next,o.next=i.next,!r.call(a,i.data,i,this)););n(this)}},a.prototype.prevUntil=function(e,r,a){if(null!==e){var i;void 0===a&&(a=this);for(var o=t(this,e,null);null!==o.prev&&(i=o.prev,o.prev=i.prev,!r.call(a,i.data,i,this)););n(this)}},a.prototype.some=function(e,t){var n=this.head;for(void 0===t&&(t=this);null!==n;){if(e.call(t,n.data,n,this))return!0;n=n.next}return!1},a.prototype.map=function(e,t){var n=new a,r=this.head;for(void 0===t&&(t=this);null!==r;)n.appendData(e.call(t,r.data,r,this)),r=r.next;return n},a.prototype.filter=function(e,t){var n=new a,r=this.head;for(void 0===t&&(t=this);null!==r;)e.call(t,r.data,r,this)&&n.appendData(r.data),r=r.next;return n},a.prototype.clear=function(){this.head=null,this.tail=null},a.prototype.copy=function(){for(var t=new a,n=this.head;null!==n;)t.insert(e(n.data)),n=n.next;return t},a.prototype.prepend=function(e){return this.updateCursors(null,e,this.head,e),null!==this.head?(this.head.prev=e,e.next=this.head):this.tail=e,this.head=e,this},a.prototype.prependData=function(t){return this.prepend(e(t))},a.prototype.append=function(e){return this.insert(e)},a.prototype.appendData=function(t){return this.insert(e(t))},a.prototype.insert=function(e,t){if(null!=t)if(this.updateCursors(t.prev,e,t,e),null===t.prev){if(this.head!==t)throw new Error("before doesn't belong to list");this.head=e,t.prev=e,e.next=t,this.updateCursors(null,e)}else t.prev.next=e,e.prev=t.prev,t.prev=e,e.next=t;else this.updateCursors(this.tail,e,null,e),null!==this.tail?(this.tail.next=e,e.prev=this.tail):this.head=e,this.tail=e;return this},a.prototype.insertData=function(t,n){return this.insert(e(t),n)},a.prototype.remove=function(e){if(this.updateCursors(e,e.prev,e,e.next),null!==e.prev)e.prev.next=e.next;else{if(this.head!==e)throw new Error("item doesn't belong to list");this.head=e.next}if(null!==e.next)e.next.prev=e.prev;else{if(this.tail!==e)throw new Error("item doesn't belong to list");this.tail=e.prev}return e.prev=null,e.next=null,e},a.prototype.push=function(t){this.insert(e(t))},a.prototype.pop=function(){if(null!==this.tail)return this.remove(this.tail)},a.prototype.unshift=function(t){this.prepend(e(t))},a.prototype.shift=function(){if(null!==this.head)return this.remove(this.head)},a.prototype.prependList=function(e){return this.insertList(e,this.head)},a.prototype.appendList=function(e){return this.insertList(e)},a.prototype.insertList=function(e,t){return null===e.head||(null!=t?(this.updateCursors(t.prev,e.tail,t,e.head),null!==t.prev?(t.prev.next=e.head,e.head.prev=t.prev):this.head=e.head,t.prev=e.tail,e.tail.next=t):(this.updateCursors(this.tail,e.tail,null,e.head),null!==this.tail?(this.tail.next=e.head,e.head.prev=this.tail):this.head=e.head,this.tail=e.tail),e.head=null,e.tail=null),this},a.prototype.replace=function(e,t){"head"in t?this.insertList(t,e):this.insert(t,e),this.remove(e)};var i=a,o=function(e,t){var n=Object.create(SyntaxError.prototype),r=new Error;return n.name=e,n.message=t,Object.defineProperty(n,"stack",{get:function(){return(r.stack||"").replace(/^(.+\n){1,3}/,e+": "+t+"\n")}}),n};function s(e,t){function n(e,t){return r.slice(e,t).map((function(t,n){for(var r=String(e+n+1);r.length<l;)r=" "+r;return r+" |"+t})).join("\n")}var r=e.source.split(/\r\n?|\n|\f/),a=e.line,i=e.column,o=Math.max(1,a-t)-1,s=Math.min(a+t,r.length+1),l=Math.max(4,String(s).length)+1,c=0;(i+=("    ".length-1)*(r[a-1].substr(0,i-1).match(/\t/g)||[]).length)>100&&(c=i-60+3,i=58);for(var u=o;u<=s;u++)u>=0&&u<r.length&&(r[u]=r[u].replace(/\t/g,"    "),r[u]=(c>0&&r[u].length>c?"…":"")+r[u].substr(c,98)+(r[u].length>c+100-1?"…":""));return[n(o,a),new Array(i+l+2).join("-")+"^",n(a,s)].filter(Boolean).join("\n")}var l=function(e,t,n,r,a){var i=o("SyntaxError",e);return i.source=t,i.offset=n,i.line=r,i.column=a,i.sourceFragment=function(e){return s(i,isNaN(e)?0:e)},Object.defineProperty(i,"formattedMessage",{get:function(){return"Parse error: "+i.message+"\n"+s(i,2)}}),i.parseError={offset:n,line:r,column:a},i},c={EOF:0,Ident:1,Function:2,AtKeyword:3,Hash:4,String:5,BadString:6,Url:7,BadUrl:8,Delim:9,Number:10,Percentage:11,Dimension:12,WhiteSpace:13,CDO:14,CDC:15,Colon:16,Semicolon:17,Comma:18,LeftSquareBracket:19,RightSquareBracket:20,LeftParenthesis:21,RightParenthesis:22,LeftCurlyBracket:23,RightCurlyBracket:24,Comment:25},u=Object.keys(c).reduce((function(e,t){return e[c[t]]=t,e}),{}),h={TYPE:c,NAME:u};function d(e){return e>=48&&e<=57}function p(e){return e>=65&&e<=90}function m(e){return e>=97&&e<=122}function f(e){return p(e)||m(e)}function g(e){return e>=128}function b(e){return f(e)||g(e)||95===e}function y(e){return e>=0&&e<=8||11===e||e>=14&&e<=31||127===e}function v(e){return 10===e||13===e||12===e}function k(e){return v(e)||32===e||9===e}function w(e,t){return 92===e&&!v(t)&&0!==t}var x=new Array(128);C.Eof=128,C.WhiteSpace=130,C.Digit=131,C.NameStart=132,C.NonPrintable=133;for(var S=0;S<x.length;S++)switch(!0){case k(S):x[S]=C.WhiteSpace;break;case d(S):x[S]=C.Digit;break;case b(S):x[S]=C.NameStart;break;case y(S):x[S]=C.NonPrintable;break;default:x[S]=S||C.Eof}function C(e){return e<128?x[e]:C.NameStart}var A={isDigit:d,isHexDigit:function(e){return d(e)||e>=65&&e<=70||e>=97&&e<=102},isUppercaseLetter:p,isLowercaseLetter:m,isLetter:f,isNonAscii:g,isNameStart:b,isName:function(e){return b(e)||d(e)||45===e},isNonPrintable:y,isNewline:v,isWhiteSpace:k,isValidEscape:w,isIdentifierStart:function(e,t,n){return 45===e?b(t)||45===t||w(t,n):!!b(e)||92===e&&w(e,t)},isNumberStart:function(e,t,n){return 43===e||45===e?d(t)?2:46===t&&d(n)?3:0:46===e?d(t)?2:0:d(e)?1:0},isBOM:function(e){return 65279===e||65534===e?1:0},charCodeCategory:C},z=A.isDigit,P=A.isHexDigit,L=A.isUppercaseLetter,T=A.isName,E=A.isWhiteSpace,O=A.isValidEscape;function D(e,t){return t<e.length?e.charCodeAt(t):0}function I(e,t,n){return 13===n&&10===D(e,t+1)?2:1}function R(e,t,n){var r=e.charCodeAt(t);return L(r)&&(r|=32),r===n}function N(e,t){for(;t<e.length&&z(e.charCodeAt(t));t++);return t}function B(e,t){if(P(D(e,(t+=2)-1))){for(var n=Math.min(e.length,t+5);t<n&&P(D(e,t));t++);var r=D(e,t);E(r)&&(t+=I(e,t,r))}return t}var M={consumeEscaped:B,consumeName:function(e,t){for(;t<e.length;t++){var n=e.charCodeAt(t);if(!T(n)){if(!O(n,D(e,t+1)))break;t=B(e,t)-1}}return t},consumeNumber:function(e,t){var n=e.charCodeAt(t);if(43!==n&&45!==n||(n=e.charCodeAt(t+=1)),z(n)&&(t=N(e,t+1),n=e.charCodeAt(t)),46===n&&z(e.charCodeAt(t+1))&&(n=e.charCodeAt(t+=2),t=N(e,t)),R(e,t,101)){var r=0;45!==(n=e.charCodeAt(t+1))&&43!==n||(r=1,n=e.charCodeAt(t+2)),z(n)&&(t=N(e,t+1+r+1))}return t},consumeBadUrlRemnants:function(e,t){for(;t<e.length;t++){var n=e.charCodeAt(t);if(41===n){t++;break}O(n,D(e,t+1))&&(t=B(e,t))}return t},cmpChar:R,cmpStr:function(e,t,n,r){if(n-t!==r.length)return!1;if(t<0||n>e.length)return!1;for(var a=t;a<n;a++){var i=e.charCodeAt(a),o=r.charCodeAt(a-t);if(L(i)&&(i|=32),i!==o)return!1}return!0},getNewlineLength:I,findWhiteSpaceStart:function(e,t){for(;t>=0&&E(e.charCodeAt(t));t--);return t+1},findWhiteSpaceEnd:function(e,t){for(;t<e.length&&E(e.charCodeAt(t));t++);return t}},j=h.TYPE,_=h.NAME,q=M.cmpStr,W=j.EOF,F=j.WhiteSpace,U=j.Comment,Y=function(){this.offsetAndType=null,this.balance=null,this.reset()};Y.prototype={reset:function(){this.eof=!1,this.tokenIndex=-1,this.tokenType=0,this.tokenStart=this.firstCharOffset,this.tokenEnd=this.firstCharOffset},lookupType:function(e){return(e+=this.tokenIndex)<this.tokenCount?this.offsetAndType[e]>>24:W},lookupOffset:function(e){return(e+=this.tokenIndex)<this.tokenCount?16777215&this.offsetAndType[e-1]:this.source.length},lookupValue:function(e,t){return(e+=this.tokenIndex)<this.tokenCount&&q(this.source,16777215&this.offsetAndType[e-1],16777215&this.offsetAndType[e],t)},getTokenStart:function(e){return e===this.tokenIndex?this.tokenStart:e>0?e<this.tokenCount?16777215&this.offsetAndType[e-1]:16777215&this.offsetAndType[this.tokenCount]:this.firstCharOffset},getRawLength:function(e,t){var n,r=e,a=16777215&this.offsetAndType[Math.max(r-1,0)];e:for(;r<this.tokenCount&&!((n=this.balance[r])<e);r++)switch(t(this.offsetAndType[r]>>24,this.source,a)){case 1:break e;case 2:r++;break e;default:a=16777215&this.offsetAndType[r],this.balance[n]===r&&(r=n)}return r-this.tokenIndex},isBalanceEdge:function(e){return this.balance[this.tokenIndex]<e},isDelim:function(e,t){return t?this.lookupType(t)===j.Delim&&this.source.charCodeAt(this.lookupOffset(t))===e:this.tokenType===j.Delim&&this.source.charCodeAt(this.tokenStart)===e},getTokenValue:function(){return this.source.substring(this.tokenStart,this.tokenEnd)},getTokenLength:function(){return this.tokenEnd-this.tokenStart},substrToCursor:function(e){return this.source.substring(e,this.tokenStart)},skipWS:function(){for(var e=this.tokenIndex,t=0;e<this.tokenCount&&this.offsetAndType[e]>>24===F;e++,t++);t>0&&this.skip(t)},skipSC:function(){for(;this.tokenType===F||this.tokenType===U;)this.next()},skip:function(e){var t=this.tokenIndex+e;t<this.tokenCount?(this.tokenIndex=t,this.tokenStart=16777215&this.offsetAndType[t-1],t=this.offsetAndType[t],this.tokenType=t>>24,this.tokenEnd=16777215&t):(this.tokenIndex=this.tokenCount,this.next())},next:function(){var e=this.tokenIndex+1;e<this.tokenCount?(this.tokenIndex=e,this.tokenStart=this.tokenEnd,e=this.offsetAndType[e],this.tokenType=e>>24,this.tokenEnd=16777215&e):(this.tokenIndex=this.tokenCount,this.eof=!0,this.tokenType=W,this.tokenStart=this.tokenEnd=this.source.length)},forEachToken(e){for(var t=0,n=this.firstCharOffset;t<this.tokenCount;t++){var r=n,a=this.offsetAndType[t],i=16777215&a;n=i,e(a>>24,r,i,t)}},dump(){var e=new Array(this.tokenCount);return this.forEachToken((t,n,r,a)=>{e[a]={idx:a,type:_[t],chunk:this.source.substring(n,r),balance:this.balance[a]}}),e}};var H=Y;function V(e){return e}var K=function(e,t){var n=V,r=!1,a=!1;return"function"==typeof t?n=t:t&&(r=Boolean(t.forceBraces),a=Boolean(t.compact),"function"==typeof t.decorate&&(n=t.decorate)),function e(t,n,r,a){var i,o;switch(t.type){case"Group":i=function(t,n,r,a){var i=" "===t.combinator||a?t.combinator:" "+t.combinator+" ",o=t.terms.map((function(t){return e(t,n,r,a)})).join(i);return(t.explicit||r)&&(o=(a||","===o[0]?"[":"[ ")+o+(a?"]":" ]")),o}(t,n,r,a)+(t.disallowEmpty?"!":"");break;case"Multiplier":return e(t.term,n,r,a)+n(0===(o=t).min&&0===o.max?"*":0===o.min&&1===o.max?"?":1===o.min&&0===o.max?o.comma?"#":"+":1===o.min&&1===o.max?"":(o.comma?"#":"")+(o.min===o.max?"{"+o.min+"}":"{"+o.min+","+(0!==o.max?o.max:"")+"}"),t);case"Type":i="<"+t.name+(t.opts?n(function(e){switch(e.type){case"Range":return" ["+(null===e.min?"-∞":e.min)+","+(null===e.max?"∞":e.max)+"]";default:throw new Error("Unknown node type `"+e.type+"`")}}(t.opts),t.opts):"")+">";break;case"Property":i="<'"+t.name+"'>";break;case"Keyword":i=t.name;break;case"AtKeyword":i="@"+t.name;break;case"Function":i=t.name+"(";break;case"String":case"Token":i=t.value;break;case"Comma":i=",";break;default:throw new Error("Unknown node type `"+t.type+"`")}return n(i,t)}(e,n,r,a)};const G={offset:0,line:1,column:1};function $(e,t){const n=e&&e.loc&&e.loc[t];return n?"line"in n?Q(n):n:null}function Q({offset:e,line:t,column:n},r){const a={offset:e,line:t,column:n};if(r){const e=r.split(/\n|\r\n?|\f/);a.offset+=r.length,a.line+=e.length-1,a.column=1===e.length?a.column+r.length:e.pop().length+1}return a}var X=Object.prototype.hasOwnProperty,Z=Object.create(null),J=Object.create(null);function ee(e,t){return t=t||0,e.length-t>=2&&45===e.charCodeAt(t)&&45===e.charCodeAt(t+1)}function te(e,t){if(t=t||0,e.length-t>=3&&45===e.charCodeAt(t)&&45!==e.charCodeAt(t+1)){var n=e.indexOf("-",t+2);if(-1!==n)return e.substring(t,n+1)}return""}var ne={keyword:function(e){if(X.call(Z,e))return Z[e];var t=e.toLowerCase();if(X.call(Z,t))return Z[e]=Z[t];var n=ee(t,0),r=n?"":te(t,0);return Z[e]=Object.freeze({basename:t.substr(r.length),name:t,vendor:r,prefix:r,custom:n})},property:function(e){if(X.call(J,e))return J[e];var t=e,n=e[0];"/"===n?n="/"===e[1]?"//":"/":"_"!==n&&"*"!==n&&"$"!==n&&"#"!==n&&"+"!==n&&"&"!==n&&(n="");var r=ee(t,n.length);if(!r&&(t=t.toLowerCase(),X.call(J,t)))return J[e]=J[t];var a=r?"":te(t,n.length),i=t.substr(0,n.length+a.length);return J[e]=Object.freeze({basename:t.substr(i.length),name:t.substr(n.length),hack:n,vendor:a,prefix:i,custom:r})},isCustomProperty:ee,vendorPrefix:te},re="undefined"!=typeof Uint32Array?Uint32Array:Array,ae=function(e,t){return null===e||e.length<t?new re(Math.max(t+1024,16384)):e},ie=h.TYPE,oe=A.isNewline,se=A.isName,le=A.isValidEscape,ce=A.isNumberStart,ue=A.isIdentifierStart,he=A.charCodeCategory,de=A.isBOM,pe=M.cmpStr,me=M.getNewlineLength,fe=M.findWhiteSpaceEnd,ge=M.consumeEscaped,be=M.consumeName,ye=M.consumeNumber,ve=M.consumeBadUrlRemnants;function ke(e,t){function n(t){return t<o?e.charCodeAt(t):0}function r(){return h=ye(e,h),ue(n(h),n(h+1),n(h+2))?(g=ie.Dimension,void(h=be(e,h))):37===n(h)?(g=ie.Percentage,void h++):void(g=ie.Number)}function a(){const t=h;return h=be(e,h),pe(e,t,h,"url")&&40===n(h)?34===n(h=fe(e,h+1))||39===n(h)?(g=ie.Function,void(h=t+4)):void function(){for(g=ie.Url,h=fe(e,h);h<e.length;h++){var t=e.charCodeAt(h);switch(he(t)){case 41:return void h++;case he.Eof:return;case he.WhiteSpace:return 41===n(h=fe(e,h))||h>=e.length?void(h<e.length&&h++):(h=ve(e,h),void(g=ie.BadUrl));case 34:case 39:case 40:case he.NonPrintable:return h=ve(e,h),void(g=ie.BadUrl);case 92:if(le(t,n(h+1))){h=ge(e,h)-1;break}return h=ve(e,h),void(g=ie.BadUrl)}}}():40===n(h)?(g=ie.Function,void h++):void(g=ie.Ident)}function i(t){for(t||(t=n(h++)),g=ie.String;h<e.length;h++){var r=e.charCodeAt(h);switch(he(r)){case t:return void h++;case he.Eof:return;case he.WhiteSpace:if(oe(r))return h+=me(e,h,r),void(g=ie.BadString);break;case 92:if(h===e.length-1)break;var a=n(h+1);oe(a)?h+=me(e,h+1,a):le(r,a)&&(h=ge(e,h)-1)}}}t||(t=new H);for(var o=(e=String(e||"")).length,s=ae(t.offsetAndType,o+1),l=ae(t.balance,o+1),c=0,u=de(n(0)),h=u,d=0,p=0,m=0;h<o;){var f=e.charCodeAt(h),g=0;switch(l[c]=o,he(f)){case he.WhiteSpace:g=ie.WhiteSpace,h=fe(e,h+1);break;case 34:i();break;case 35:se(n(h+1))||le(n(h+1),n(h+2))?(g=ie.Hash,h=be(e,h+1)):(g=ie.Delim,h++);break;case 39:i();break;case 40:g=ie.LeftParenthesis,h++;break;case 41:g=ie.RightParenthesis,h++;break;case 43:ce(f,n(h+1),n(h+2))?r():(g=ie.Delim,h++);break;case 44:g=ie.Comma,h++;break;case 45:ce(f,n(h+1),n(h+2))?r():45===n(h+1)&&62===n(h+2)?(g=ie.CDC,h+=3):ue(f,n(h+1),n(h+2))?a():(g=ie.Delim,h++);break;case 46:ce(f,n(h+1),n(h+2))?r():(g=ie.Delim,h++);break;case 47:42===n(h+1)?(g=ie.Comment,1===(h=e.indexOf("*/",h+2)+2)&&(h=e.length)):(g=ie.Delim,h++);break;case 58:g=ie.Colon,h++;break;case 59:g=ie.Semicolon,h++;break;case 60:33===n(h+1)&&45===n(h+2)&&45===n(h+3)?(g=ie.CDO,h+=4):(g=ie.Delim,h++);break;case 64:ue(n(h+1),n(h+2),n(h+3))?(g=ie.AtKeyword,h=be(e,h+1)):(g=ie.Delim,h++);break;case 91:g=ie.LeftSquareBracket,h++;break;case 92:le(f,n(h+1))?a():(g=ie.Delim,h++);break;case 93:g=ie.RightSquareBracket,h++;break;case 123:g=ie.LeftCurlyBracket,h++;break;case 125:g=ie.RightCurlyBracket,h++;break;case he.Digit:r();break;case he.NameStart:a();break;case he.Eof:break;default:g=ie.Delim,h++}switch(g){case d:for(d=(p=l[m=16777215&p])>>24,l[c]=m,l[m++]=c;m<c;m++)l[m]===o&&(l[m]=c);break;case ie.LeftParenthesis:case ie.Function:l[c]=p,p=(d=ie.RightParenthesis)<<24|c;break;case ie.LeftSquareBracket:l[c]=p,p=(d=ie.RightSquareBracket)<<24|c;break;case ie.LeftCurlyBracket:l[c]=p,p=(d=ie.RightCurlyBracket)<<24|c}s[c++]=g<<24|h}for(s[c]=ie.EOF<<24|h,l[c]=o,l[o]=o;0!==p;)p=l[m=16777215&p],l[m]=o;return t.source=e,t.firstCharOffset=u,t.offsetAndType=s,t.tokenCount=c,t.balance=l,t.reset(),t.next(),t}Object.keys(h).forEach((function(e){ke[e]=h[e]})),Object.keys(A).forEach((function(e){ke[e]=A[e]})),Object.keys(M).forEach((function(e){ke[e]=M[e]}));var we=ke,xe=we.isDigit,Se=we.cmpChar,Ce=we.TYPE,Ae=Ce.Delim,ze=Ce.WhiteSpace,Pe=Ce.Comment,Le=Ce.Ident,Te=Ce.Number,Ee=Ce.Dimension;function Oe(e,t){return null!==e&&e.type===Ae&&e.value.charCodeAt(0)===t}function De(e,t,n){for(;null!==e&&(e.type===ze||e.type===Pe);)e=n(++t);return t}function Ie(e,t,n,r){if(!e)return 0;var a=e.value.charCodeAt(t);if(43===a||45===a){if(n)return 0;t++}for(;t<e.value.length;t++)if(!xe(e.value.charCodeAt(t)))return 0;return r+1}function Re(e,t,n){var r=!1,a=De(e,t,n);if(null===(e=n(a)))return t;if(e.type!==Te){if(!Oe(e,43)&&!Oe(e,45))return t;if(r=!0,a=De(n(++a),a,n),null===(e=n(a))&&e.type!==Te)return 0}if(!r){var i=e.value.charCodeAt(0);if(43!==i&&45!==i)return 0}return Ie(e,r?0:1,r,a)}var Ne=we.isHexDigit,Be=we.cmpChar,Me=we.TYPE,je=Me.Ident,_e=Me.Delim,qe=Me.Number,We=Me.Dimension;function Fe(e,t){return null!==e&&e.type===_e&&e.value.charCodeAt(0)===t}function Ue(e,t){return e.value.charCodeAt(0)===t}function Ye(e,t,n){for(var r=t,a=0;r<e.value.length;r++){var i=e.value.charCodeAt(r);if(45===i&&n&&0!==a)return Ye(e,t+a+1,!1)>0?6:0;if(!Ne(i))return 0;if(++a>6)return 0}return a}function He(e,t,n){if(!e)return 0;for(;Fe(n(t),63);){if(++e>6)return 0;t++}return t}var Ve=we.isIdentifierStart,Ke=we.isHexDigit,Ge=we.isDigit,$e=we.cmpStr,Qe=we.consumeNumber,Xe=we.TYPE,Ze=["unset","initial","inherit"],Je=["calc(","-moz-calc(","-webkit-calc("];function et(e,t){return t<e.length?e.charCodeAt(t):0}function tt(e,t){return $e(e,0,e.length,t)}function nt(e,t){for(var n=0;n<t.length;n++)if(tt(e,t[n]))return!0;return!1}function rt(e,t){return t===e.length-2&&92===e.charCodeAt(t)&&Ge(e.charCodeAt(t+1))}function at(e,t,n){if(e&&"Range"===e.type){var r=Number(void 0!==n&&n!==t.length?t.substr(0,n):t);if(isNaN(r))return!0;if(null!==e.min&&r<e.min)return!0;if(null!==e.max&&r>e.max)return!0}return!1}function it(e,t){var n=e.index,r=0;do{if(r++,e.balance<=n)break}while(e=t(r));return r}function ot(e){return function(t,n,r){return null===t?0:t.type===Xe.Function&&nt(t.value,Je)?it(t,n):e(t,n,r)}}function st(e){return function(t){return null===t||t.type!==e?0:1}}function lt(e){return function(t,n,r){if(null===t||t.type!==Xe.Dimension)return 0;var a=Qe(t.value,0);if(null!==e){var i=t.value.indexOf("\\",a),o=-1!==i&&rt(t.value,i)?t.value.substring(a,i):t.value.substr(a);if(!1===e.hasOwnProperty(o.toLowerCase()))return 0}return at(r,t.value,a)?0:1}}function ct(e){return"function"!=typeof e&&(e=function(){return 0}),function(t,n,r){return null!==t&&t.type===Xe.Number&&0===Number(t.value)?1:e(t,n,r)}}var ut={"ident-token":st(Xe.Ident),"function-token":st(Xe.Function),"at-keyword-token":st(Xe.AtKeyword),"hash-token":st(Xe.Hash),"string-token":st(Xe.String),"bad-string-token":st(Xe.BadString),"url-token":st(Xe.Url),"bad-url-token":st(Xe.BadUrl),"delim-token":st(Xe.Delim),"number-token":st(Xe.Number),"percentage-token":st(Xe.Percentage),"dimension-token":st(Xe.Dimension),"whitespace-token":st(Xe.WhiteSpace),"CDO-token":st(Xe.CDO),"CDC-token":st(Xe.CDC),"colon-token":st(Xe.Colon),"semicolon-token":st(Xe.Semicolon),"comma-token":st(Xe.Comma),"[-token":st(Xe.LeftSquareBracket),"]-token":st(Xe.RightSquareBracket),"(-token":st(Xe.LeftParenthesis),")-token":st(Xe.RightParenthesis),"{-token":st(Xe.LeftCurlyBracket),"}-token":st(Xe.RightCurlyBracket),string:st(Xe.String),ident:st(Xe.Ident),"custom-ident":function(e){if(null===e||e.type!==Xe.Ident)return 0;var t=e.value.toLowerCase();return nt(t,Ze)||tt(t,"default")?0:1},"custom-property-name":function(e){return null===e||e.type!==Xe.Ident||45!==et(e.value,0)||45!==et(e.value,1)?0:1},"hex-color":function(e){if(null===e||e.type!==Xe.Hash)return 0;var t=e.value.length;if(4!==t&&5!==t&&7!==t&&9!==t)return 0;for(var n=1;n<t;n++)if(!Ke(e.value.charCodeAt(n)))return 0;return 1},"id-selector":function(e){return null===e||e.type!==Xe.Hash?0:Ve(et(e.value,1),et(e.value,2),et(e.value,3))?1:0},"an-plus-b":function(e,t){var n=0;if(!e)return 0;if(e.type===Te)return Ie(e,0,!1,n);if(e.type===Le&&45===e.value.charCodeAt(0)){if(!Se(e.value,1,110))return 0;switch(e.value.length){case 2:return Re(t(++n),n,t);case 3:return 45!==e.value.charCodeAt(2)?0:(n=De(t(++n),n,t),Ie(e=t(n),0,!0,n));default:return 45!==e.value.charCodeAt(2)?0:Ie(e,3,!0,n)}}else if(e.type===Le||Oe(e,43)&&t(n+1).type===Le){if(e.type!==Le&&(e=t(++n)),null===e||!Se(e.value,0,110))return 0;switch(e.value.length){case 1:return Re(t(++n),n,t);case 2:return 45!==e.value.charCodeAt(1)?0:(n=De(t(++n),n,t),Ie(e=t(n),0,!0,n));default:return 45!==e.value.charCodeAt(1)?0:Ie(e,2,!0,n)}}else if(e.type===Ee){for(var r=e.value.charCodeAt(0),a=43===r||45===r?1:0,i=a;i<e.value.length&&xe(e.value.charCodeAt(i));i++);return i===a?0:Se(e.value,i,110)?i+1===e.value.length?Re(t(++n),n,t):45!==e.value.charCodeAt(i+1)?0:i+2===e.value.length?(n=De(t(++n),n,t),Ie(e=t(n),0,!0,n)):Ie(e,i+2,!0,n):0}return 0},urange:function(e,t){var n=0;if(null===e||e.type!==je||!Be(e.value,0,117))return 0;if(null===(e=t(++n)))return 0;if(Fe(e,43))return null===(e=t(++n))?0:e.type===je?He(Ye(e,0,!0),++n,t):Fe(e,63)?He(1,++n,t):0;if(e.type===qe){if(!Ue(e,43))return 0;var r=Ye(e,1,!0);return 0===r?0:null===(e=t(++n))?n:e.type===We||e.type===qe?Ue(e,45)&&Ye(e,1,!1)?n+1:0:He(r,n,t)}return e.type===We&&Ue(e,43)?He(Ye(e,1,!0),++n,t):0},"declaration-value":function(e,t){if(!e)return 0;var n=0,r=0,a=e.index;e:do{switch(e.type){case Xe.BadString:case Xe.BadUrl:break e;case Xe.RightCurlyBracket:case Xe.RightParenthesis:case Xe.RightSquareBracket:if(e.balance>e.index||e.balance<a)break e;r--;break;case Xe.Semicolon:if(0===r)break e;break;case Xe.Delim:if("!"===e.value&&0===r)break e;break;case Xe.Function:case Xe.LeftParenthesis:case Xe.LeftSquareBracket:case Xe.LeftCurlyBracket:r++}if(n++,e.balance<=a)break}while(e=t(n));return n},"any-value":function(e,t){if(!e)return 0;var n=e.index,r=0;e:do{switch(e.type){case Xe.BadString:case Xe.BadUrl:break e;case Xe.RightCurlyBracket:case Xe.RightParenthesis:case Xe.RightSquareBracket:if(e.balance>e.index||e.balance<n)break e}if(r++,e.balance<=n)break}while(e=t(r));return r},dimension:ot(lt(null)),angle:ot(lt({deg:!0,grad:!0,rad:!0,turn:!0})),decibel:ot(lt({db:!0})),frequency:ot(lt({hz:!0,khz:!0})),flex:ot(lt({fr:!0})),length:ot(ct(lt({px:!0,mm:!0,cm:!0,in:!0,pt:!0,pc:!0,q:!0,em:!0,ex:!0,ch:!0,rem:!0,vh:!0,vw:!0,vmin:!0,vmax:!0,vm:!0}))),resolution:ot(lt({dpi:!0,dpcm:!0,dppx:!0,x:!0})),semitones:ot(lt({st:!0})),time:ot(lt({s:!0,ms:!0})),percentage:ot((function(e,t,n){return null===e||e.type!==Xe.Percentage||at(n,e.value,e.value.length-1)?0:1})),zero:ct(),number:ot((function(e,t,n){if(null===e)return 0;var r=Qe(e.value,0);return r===e.value.length||rt(e.value,r)?at(n,e.value,r)?0:1:0})),integer:ot((function(e,t,n){if(null===e||e.type!==Xe.Number)return 0;for(var r=43===e.value.charCodeAt(0)||45===e.value.charCodeAt(0)?1:0;r<e.value.length;r++)if(!Ge(e.value.charCodeAt(r)))return 0;return at(n,e.value,r)?0:1})),"-ms-legacy-expression":function(e,t){return null!==e&&tt(e.value,"expression(")?it(e,t):0}},ht=function(e,t,n){var r=o("SyntaxError",e);return r.input=t,r.offset=n,r.rawMessage=e,r.message=r.rawMessage+"\n  "+r.input+"\n--"+new Array((r.offset||r.input.length)+1).join("-")+"^",r},dt=function(e){this.str=e,this.pos=0};dt.prototype={charCodeAt:function(e){return e<this.str.length?this.str.charCodeAt(e):0},charCode:function(){return this.charCodeAt(this.pos)},nextCharCode:function(){return this.charCodeAt(this.pos+1)},nextNonWsCode:function(e){return this.charCodeAt(this.findWsEnd(e))},findWsEnd:function(e){for(;e<this.str.length;e++){var t=this.str.charCodeAt(e);if(13!==t&&10!==t&&12!==t&&32!==t&&9!==t)break}return e},substringToPos:function(e){return this.str.substring(this.pos,this.pos=e)},eat:function(e){this.charCode()!==e&&this.error("Expect `"+String.fromCharCode(e)+"`"),this.pos++},peek:function(){return this.pos<this.str.length?this.str.charAt(this.pos++):""},error:function(e){throw new ht(e,this.str,this.pos)}};var pt=dt,mt=function(e){for(var t="function"==typeof Uint32Array?new Uint32Array(128):new Array(128),n=0;n<128;n++)t[n]=e(String.fromCharCode(n))?1:0;return t}((function(e){return/[a-zA-Z0-9\-]/.test(e)})),ft={" ":1,"&&":2,"||":3,"|":4};function gt(e){return e.substringToPos(e.findWsEnd(e.pos))}function bt(e){for(var t=e.pos;t<e.str.length;t++){var n=e.str.charCodeAt(t);if(n>=128||0===mt[n])break}return e.pos===t&&e.error("Expect a keyword"),e.substringToPos(t)}function yt(e){for(var t=e.pos;t<e.str.length;t++){var n=e.str.charCodeAt(t);if(n<48||n>57)break}return e.pos===t&&e.error("Expect a number"),e.substringToPos(t)}function vt(e){var t=e.str.indexOf("'",e.pos+1);return-1===t&&(e.pos=e.str.length,e.error("Expect an apostrophe")),e.substringToPos(t+1)}function kt(e){var t,n=null;return e.eat(123),t=yt(e),44===e.charCode()?(e.pos++,125!==e.charCode()&&(n=yt(e))):n=t,e.eat(125),{min:Number(t),max:n?Number(n):0}}function wt(e,t){var n=function(e){var t=null,n=!1;switch(e.charCode()){case 42:e.pos++,t={min:0,max:0};break;case 43:e.pos++,t={min:1,max:0};break;case 63:e.pos++,t={min:0,max:1};break;case 35:e.pos++,n=!0,t=123===e.charCode()?kt(e):{min:1,max:0};break;case 123:t=kt(e);break;default:return null}return{type:"Multiplier",comma:n,min:t.min,max:t.max,term:null}}(e);return null!==n?(n.term=t,n):t}function xt(e){var t=e.peek();return""===t?null:{type:"Token",value:t}}function St(e,t){function n(e,t){return{type:"Group",terms:e,combinator:t,disallowEmpty:!1,explicit:!1}}for(t=Object.keys(t).sort((function(e,t){return ft[e]-ft[t]}));t.length>0;){for(var r=t.shift(),a=0,i=0;a<e.length;a++){var o=e[a];"Combinator"===o.type&&(o.value===r?(-1===i&&(i=a-1),e.splice(a,1),a--):(-1!==i&&a-i>1&&(e.splice(i,a-i,n(e.slice(i,a),r)),a=i+1),i=-1))}-1!==i&&t.length&&e.splice(i,a-i,n(e.slice(i,a),r))}return r}function Ct(e){for(var t,n=[],r={},a=null,i=e.pos;t=At(e);)"Spaces"!==t.type&&("Combinator"===t.type?(null!==a&&"Combinator"!==a.type||(e.pos=i,e.error("Unexpected combinator")),r[t.value]=!0):null!==a&&"Combinator"!==a.type&&(r[" "]=!0,n.push({type:"Combinator",value:" "})),n.push(t),a=t,i=e.pos);return null!==a&&"Combinator"===a.type&&(e.pos-=i,e.error("Unexpected combinator")),{type:"Group",terms:n,combinator:St(n,r)||" ",disallowEmpty:!1,explicit:!1}}function At(e){var t=e.charCode();if(t<128&&1===mt[t])return function(e){var t;return t=bt(e),40===e.charCode()?(e.pos++,{type:"Function",name:t}):wt(e,{type:"Keyword",name:t})}(e);switch(t){case 93:break;case 91:return wt(e,function(e){var t;return e.eat(91),t=Ct(e),e.eat(93),t.explicit=!0,33===e.charCode()&&(e.pos++,t.disallowEmpty=!0),t}(e));case 60:return 39===e.nextCharCode()?function(e){var t;return e.eat(60),e.eat(39),t=bt(e),e.eat(39),e.eat(62),wt(e,{type:"Property",name:t})}(e):function(e){var t,n=null;return e.eat(60),t=bt(e),40===e.charCode()&&41===e.nextCharCode()&&(e.pos+=2,t+="()"),91===e.charCodeAt(e.findWsEnd(e.pos))&&(gt(e),n=function(e){var t=null,n=null,r=1;return e.eat(91),45===e.charCode()&&(e.peek(),r=-1),-1==r&&8734===e.charCode()?e.peek():t=r*Number(yt(e)),gt(e),e.eat(44),gt(e),8734===e.charCode()?e.peek():(r=1,45===e.charCode()&&(e.peek(),r=-1),n=r*Number(yt(e))),e.eat(93),null===t&&null===n?null:{type:"Range",min:t,max:n}}(e)),e.eat(62),wt(e,{type:"Type",name:t,opts:n})}(e);case 124:return{type:"Combinator",value:e.substringToPos(124===e.nextCharCode()?e.pos+2:e.pos+1)};case 38:return e.pos++,e.eat(38),{type:"Combinator",value:"&&"};case 44:return e.pos++,{type:"Comma"};case 39:return wt(e,{type:"String",value:vt(e)});case 32:case 9:case 10:case 13:case 12:return{type:"Spaces",value:gt(e)};case 64:return(t=e.nextCharCode())<128&&1===mt[t]?(e.pos++,{type:"AtKeyword",name:bt(e)}):xt(e);case 42:case 43:case 63:case 35:case 33:break;case 123:if((t=e.nextCharCode())<48||t>57)return xt(e);break;default:return xt(e)}}function zt(e){var t=new pt(e),n=Ct(t);return t.pos!==e.length&&t.error("Unexpected input"),1===n.terms.length&&"Group"===n.terms[0].type&&(n=n.terms[0]),n}zt("[a&&<b>#|<'c'>*||e() f{2} /,(% g#{1,2} h{2,})]!");var Pt=zt,Lt=function(){};function Tt(e){return"function"==typeof e?e:Lt}var Et=function(e,t,n){var r=Lt,a=Lt;if("function"==typeof t?r=t:t&&(r=Tt(t.enter),a=Tt(t.leave)),r===Lt&&a===Lt)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");!function e(t){switch(r.call(n,t),t.type){case"Group":t.terms.forEach(e);break;case"Multiplier":e(t.term);break;case"Type":case"Property":case"Keyword":case"AtKeyword":case"Function":case"String":case"Token":case"Comma":break;default:throw new Error("Unknown type: "+t.type)}a.call(n,t)}(e)},Ot=new H,Dt={decorator:function(e){var t=null,n={len:0,node:null},r=[n],a="";return{children:e.children,node:function(n){var r=t;t=n,e.node.call(this,n),t=r},chunk:function(e){a+=e,n.node!==t?r.push({len:e.length,node:t}):n.len+=e.length},result:function(){return It(a,r)}}}};function It(e,t){var n=[],r=0,a=0,i=t?t[a].node:null;for(we(e,Ot);!Ot.eof;){if(t)for(;a<t.length&&r+t[a].len<=Ot.tokenStart;)r+=t[a++].len,i=t[a].node;n.push({type:Ot.tokenType,value:Ot.getTokenValue(),index:Ot.tokenIndex,balance:Ot.balance[Ot.tokenIndex],node:i}),Ot.next()}return n}var Rt={type:"Match"},Nt={type:"Mismatch"},Bt={type:"DisallowEmpty"};function Mt(e,t,n){return t===Rt&&n===Nt||e===Rt&&t===Rt&&n===Rt?e:("If"===e.type&&e.else===Nt&&t===Rt&&(t=e.then,e=e.match),{type:"If",match:e,then:t,else:n})}function jt(e){return e.length>2&&40===e.charCodeAt(e.length-2)&&41===e.charCodeAt(e.length-1)}function _t(e){return"Keyword"===e.type||"AtKeyword"===e.type||"Function"===e.type||"Type"===e.type&&jt(e.name)}function qt(e){if("function"==typeof e)return{type:"Generic",fn:e};switch(e.type){case"Group":var t=function e(t,n,r){switch(t){case" ":for(var a=Rt,i=n.length-1;i>=0;i--)a=Mt(l=n[i],a,Nt);return a;case"|":a=Nt;var o=null;for(i=n.length-1;i>=0;i--){if(_t(l=n[i])&&(null===o&&i>0&&_t(n[i-1])&&(a=Mt({type:"Enum",map:o=Object.create(null)},Rt,a)),null!==o)){var s=(jt(l.name)?l.name.slice(0,-1):l.name).toLowerCase();if(s in o==0){o[s]=l;continue}}o=null,a=Mt(l,Rt,a)}return a;case"&&":if(n.length>5)return{type:"MatchOnce",terms:n,all:!0};for(a=Nt,i=n.length-1;i>=0;i--){var l=n[i];c=n.length>1?e(t,n.filter((function(e){return e!==l})),!1):Rt,a=Mt(l,c,a)}return a;case"||":if(n.length>5)return{type:"MatchOnce",terms:n,all:!1};for(a=r?Rt:Nt,i=n.length-1;i>=0;i--){var c;l=n[i],c=n.length>1?e(t,n.filter((function(e){return e!==l})),!0):Rt,a=Mt(l,c,a)}return a}}(e.combinator,e.terms.map(qt),!1);return e.disallowEmpty&&(t=Mt(t,Bt,Nt)),t;case"Multiplier":return function(e){var t=Rt,n=qt(e.term);if(0===e.max)n=Mt(n,Bt,Nt),(t=Mt(n,null,Nt)).then=Mt(Rt,Rt,t),e.comma&&(t.then.else=Mt({type:"Comma",syntax:e},t,Nt));else for(var r=e.min||1;r<=e.max;r++)e.comma&&t!==Rt&&(t=Mt({type:"Comma",syntax:e},t,Nt)),t=Mt(n,Mt(Rt,Rt,t),Nt);if(0===e.min)t=Mt(Rt,Rt,t);else for(r=0;r<e.min-1;r++)e.comma&&t!==Rt&&(t=Mt({type:"Comma",syntax:e},t,Nt)),t=Mt(n,t,Nt);return t}(e);case"Type":case"Property":return{type:e.type,name:e.name,syntax:e};case"Keyword":return{type:e.type,name:e.name.toLowerCase(),syntax:e};case"AtKeyword":return{type:e.type,name:"@"+e.name.toLowerCase(),syntax:e};case"Function":return{type:e.type,name:e.name.toLowerCase()+"(",syntax:e};case"String":return 3===e.value.length?{type:"Token",value:e.value.charAt(1),syntax:e}:{type:e.type,value:e.value.substr(1,e.value.length-2).replace(/\\'/g,"'"),syntax:e};case"Token":return{type:e.type,value:e.value,syntax:e};case"Comma":return{type:e.type,syntax:e};default:throw new Error("Unknown node type:",e.type)}}var Wt=Rt,Ft=Nt,Ut=Bt,Yt=Object.prototype.hasOwnProperty,Ht=Wt,Vt=Ft,Kt=Ut,Gt=h.TYPE;function $t(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);if(r>=65&&r<=90&&(r|=32),r!==t.charCodeAt(n))return!1}return!0}function Qt(e){return null===e||e.type===Gt.Comma||e.type===Gt.Function||e.type===Gt.LeftParenthesis||e.type===Gt.LeftSquareBracket||e.type===Gt.LeftCurlyBracket||function(e){return e.type===Gt.Delim&&"?"!==e.value}(e)}function Xt(e){return null===e||e.type===Gt.RightParenthesis||e.type===Gt.RightSquareBracket||e.type===Gt.RightCurlyBracket||e.type===Gt.Delim}function Zt(e){function t(e){return null!==e&&("Type"===e.type||"Property"===e.type||"Keyword"===e.type)}var n=null;return null!==this.matched&&function r(a){if(Array.isArray(a.match)){for(var i=0;i<a.match.length;i++)if(r(a.match[i]))return t(a.syntax)&&n.unshift(a.syntax),!0}else if(a.node===e)return n=t(a.syntax)?[a.syntax]:[],!0;return!1}(this.matched),n}function Jt(e,t,n){var r=Zt.call(e,t);return null!==r&&r.some(n)}var en={getTrace:Zt,isType:function(e,t){return Jt(this,e,(function(e){return"Type"===e.type&&e.name===t}))},isProperty:function(e,t){return Jt(this,e,(function(e){return"Property"===e.type&&e.name===t}))},isKeyword:function(e){return Jt(this,e,(function(e){return"Keyword"===e.type}))}},tn=function(e,t,n,r,a){var o=[];return null!==n.matched&&function n(s){if(null!==s.syntax&&s.syntax.type===r&&s.syntax.name===a){var l=function e(t){return"node"in t?t.node:e(t.match[0])}(s),c=function e(t){return"node"in t?t.node:e(t.match[t.match.length-1])}(s);e.syntax.walk(t,(function(e,t,n){if(e===l){var r=new i;do{if(r.appendData(t.data),t.data===c)break;t=t.next}while(null!==t);o.push({parent:n,nodes:r})}}))}Array.isArray(s.match)&&s.match.forEach(n)}(n.matched),o},nn=Object.prototype.hasOwnProperty;function rn(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&e>=0}function an(e){return Boolean(e)&&rn(e.offset)&&rn(e.line)&&rn(e.column)}function on(e,t){return function(n,r){if(!n||n.constructor!==Object)return r(n,"Type of node should be an Object");for(var a in n){var o=!0;if(!1!==nn.call(n,a)){if("type"===a)n.type!==e&&r(n,"Wrong node type `"+n.type+"`, expected `"+e+"`");else if("loc"===a){if(null===n.loc)continue;if(n.loc&&n.loc.constructor===Object)if("string"!=typeof n.loc.source)a+=".source";else if(an(n.loc.start)){if(an(n.loc.end))continue;a+=".end"}else a+=".start";o=!1}else if(t.hasOwnProperty(a)){var s=0;for(o=!1;!o&&s<t[a].length;s++){var l=t[a][s];switch(l){case String:o="string"==typeof n[a];break;case Boolean:o="boolean"==typeof n[a];break;case null:o=null===n[a];break;default:"string"==typeof l?o=n[a]&&n[a].type===l:Array.isArray(l)&&(o=n[a]instanceof i)}}}else r(n,"Unknown field `"+a+"` for "+e+" node type");o||r(n,"Bad value for `"+e+"."+a+"`")}}for(var a in t)nn.call(t,a)&&!1===nn.call(n,a)&&r(n,"Field `"+e+"."+a+"` is missed")}}function sn(e,t){var n=t.structure,r={type:String,loc:!0},a={type:'"'+e+'"'};for(var i in n)if(!1!==nn.call(n,i)){for(var o=[],s=r[i]=Array.isArray(n[i])?n[i].slice():[n[i]],l=0;l<s.length;l++){var c=s[l];if(c===String||c===Boolean)o.push(c.name);else if(null===c)o.push("null");else if("string"==typeof c)o.push("<"+c+">");else{if(!Array.isArray(c))throw new Error("Wrong value `"+c+"` in `"+e+"."+i+"` structure definition");o.push("List")}}a[i]=o.join(" | ")}return{docs:a,check:on(e,r)}}var ln=function(e,t){const n=o("SyntaxReferenceError",e+(t?" `"+t+"`":""));return n.reference=t,n},cn=function(e,t,n,r){const a=o("SyntaxMatchError",e),{css:i,mismatchOffset:s,mismatchLength:l,start:c,end:u}=function(e,t){const n=e.tokens,r=e.longestMatch,a=r<n.length&&n[r].node||null,i=a!==t?a:null;let o,s,l=0,c=0,u=0,h="";for(let e=0;e<n.length;e++){const t=n[e].value;e===r&&(c=t.length,l=h.length),null!==i&&n[e].node===i&&(e<=r?u++:u=0),h+=t}return r===n.length||u>1?(o=$(i||t,"end")||Q(G,h),s=Q(o)):(o=$(i,"start")||Q($(t,"start")||G,h.slice(0,l)),s=$(i,"end")||Q(o,h.substr(l,c))),{css:h,mismatchOffset:l,mismatchLength:c,start:o,end:s}}(r,n);return a.rawMessage=e,a.syntax=t?K(t):"<generic>",a.css=i,a.mismatchOffset=s,a.mismatchLength=l,a.message=e+"\n  syntax: "+a.syntax+"\n   value: "+(i||"<empty string>")+"\n  --------"+new Array(a.mismatchOffset+1).join("-")+"^",Object.assign(a,c),a.loc={source:n&&n.loc&&n.loc.source||"<unknown>",start:c,end:u},a},un=function(e,t){return"string"==typeof e&&(e=Pt(e)),{type:"MatchGraph",match:qt(e),syntax:t||null,source:e}},hn=function(e,t,n){var r=function(e,t,n){function r(){do{b++,g=b<e.length?e[b]:null}while(null!==g&&(g.type===Gt.WhiteSpace||g.type===Gt.Comment))}function a(t){var n=b+t;return n<e.length?e[n]:null}function i(e,t){return{nextState:e,matchStack:v,syntaxStack:u,thenStack:h,tokenIndex:b,prev:t}}function o(e){h={nextState:e,matchStack:v,syntaxStack:u,prev:h}}function s(e){d=i(e,d)}function l(){v={type:1,syntax:t.syntax,token:g,prev:v},r(),p=null,b>y&&(y=b)}function c(){v=2===v.type?v.prev:{type:3,syntax:u.syntax,token:v.token,prev:v},u=u.prev}var u=null,h=null,d=null,p=null,m=0,f=null,g=null,b=-1,y=0,v={type:0,syntax:null,token:null,prev:null};for(r();null===f&&++m<15e3;)switch(t.type){case"Match":if(null===h){if(null!==g&&(b!==e.length-1||"\\0"!==g.value&&"\\9"!==g.value)){t=Vt;break}f="Match";break}if((t=h.nextState)===Kt){if(h.matchStack===v){t=Vt;break}t=Ht}for(;h.syntaxStack!==u;)c();h=h.prev;break;case"Mismatch":if(null!==p&&!1!==p)(null===d||b>d.tokenIndex)&&(d=p,p=!1);else if(null===d){f="Mismatch";break}t=d.nextState,h=d.thenStack,u=d.syntaxStack,v=d.matchStack,b=d.tokenIndex,g=b<e.length?e[b]:null,d=d.prev;break;case"MatchGraph":t=t.match;break;case"If":t.else!==Vt&&s(t.else),t.then!==Ht&&o(t.then),t=t.match;break;case"MatchOnce":t={type:"MatchOnceBuffer",syntax:t,index:0,mask:0};break;case"MatchOnceBuffer":var k=t.syntax.terms;if(t.index===k.length){if(0===t.mask||t.syntax.all){t=Vt;break}t=Ht;break}if(t.mask===(1<<k.length)-1){t=Ht;break}for(;t.index<k.length;t.index++){var w=1<<t.index;if(0==(t.mask&w)){s(t),o({type:"AddMatchOnce",syntax:t.syntax,mask:t.mask|w}),t=k[t.index++];break}}break;case"AddMatchOnce":t={type:"MatchOnceBuffer",syntax:t.syntax,index:0,mask:t.mask};break;case"Enum":if(null!==g&&(-1!==(z=g.value.toLowerCase()).indexOf("\\")&&(z=z.replace(/\\[09].*$/,"")),Yt.call(t.map,z))){t=t.map[z];break}t=Vt;break;case"Generic":var x=null!==u?u.opts:null,S=b+Math.floor(t.fn(g,a,x));if(!isNaN(S)&&S>b){for(;b<S;)l();t=Ht}else t=Vt;break;case"Type":case"Property":var C="Type"===t.type?"types":"properties",A=Yt.call(n,C)?n[C][t.name]:null;if(!A||!A.match)throw new Error("Bad syntax reference: "+("Type"===t.type?"<"+t.name+">":"<'"+t.name+"'>"));if(!1!==p&&null!==g&&"Type"===t.type&&("custom-ident"===t.name&&g.type===Gt.Ident||"length"===t.name&&"0"===g.value)){null===p&&(p=i(t,d)),t=Vt;break}u={syntax:t.syntax,opts:t.syntax.opts||null!==u&&u.opts||null,prev:u},v={type:2,syntax:t.syntax,token:v.token,prev:v},t=A.match;break;case"Keyword":var z=t.name;if(null!==g){var P=g.value;if(-1!==P.indexOf("\\")&&(P=P.replace(/\\[09].*$/,"")),$t(P,z)){l(),t=Ht;break}}t=Vt;break;case"AtKeyword":case"Function":if(null!==g&&$t(g.value,t.name)){l(),t=Ht;break}t=Vt;break;case"Token":if(null!==g&&g.value===t.value){l(),t=Ht;break}t=Vt;break;case"Comma":null!==g&&g.type===Gt.Comma?Qt(v.token)?t=Vt:(l(),t=Xt(g)?Vt:Ht):t=Qt(v.token)||Xt(g)?Ht:Vt;break;case"String":var L="";for(S=b;S<e.length&&L.length<t.value.length;S++)L+=e[S].value;if($t(L,t.value)){for(;b<S;)l();t=Ht}else t=Vt;break;default:throw new Error("Unknown node type: "+t.type)}switch(f){case null:console.warn("[csstree-match] BREAK after 15000 iterations"),f="Maximum iteration number exceeded (please fill an issue on https://github.com/csstree/csstree/issues)",v=null;break;case"Match":for(;null!==u;)c();break;default:v=null}return{tokens:e,reason:f,iterations:m,match:v,longestMatch:y}}(e,t,n||{});if(null===r.match)return r;var a=r.match,i=r.match={syntax:t.syntax||null,match:[]},o=[i];for(a=function(e){for(var t=null,n=null,r=e;null!==r;)n=r.prev,r.prev=t,t=r,r=n;return t}(a).prev;null!==a;){switch(a.type){case 2:i.match.push(i={syntax:a.syntax,match:[]}),o.push(i);break;case 3:o.pop(),i=o[o.length-1];break;default:i.match.push({syntax:a.syntax||null,token:a.token.value,node:a.token.node})}a=a.prev}return r},dn=un("inherit | initial | unset"),pn=un("inherit | initial | unset | <-ms-legacy-expression>");function mn(e,t,n){var r={};for(var a in e)e[a].syntax&&(r[a]=n?e[a].syntax:K(e[a].syntax,{compact:t}));return r}function fn(e,t,n){const r={};for(const[a,i]of Object.entries(e))r[a]={prelude:i.prelude&&(n?i.prelude.syntax:K(i.prelude.syntax,{compact:t})),descriptors:i.descriptors&&mn(i.descriptors,t,n)};return r}function gn(e,t,n){return{matched:e,iterations:n,error:t,getTrace:en.getTrace,isType:en.isType,isProperty:en.isProperty,isKeyword:en.isKeyword}}function bn(e,t,n,r){var a,i=function(e,t){return"string"==typeof e?It(e,null):t.generate(e,Dt)}(n,e.syntax);return function(e){for(var t=0;t<e.length;t++)if("var("===e[t].value.toLowerCase())return!0;return!1}(i)?gn(null,new Error("Matching for a tree with var() is not supported")):(r&&(a=hn(i,e.valueCommonSyntax,e)),r&&a.match||(a=hn(i,t.match,e)).match?gn(a.match,null,a.iterations):gn(null,new cn(a.reason,t.syntax,n,a),a.iterations))}var yn=function(e,t,n){if(this.valueCommonSyntax=dn,this.syntax=t,this.generic=!1,this.atrules={},this.properties={},this.types={},this.structure=n||function(e){var t={};if(e.node)for(var n in e.node)if(nn.call(e.node,n)){var r=e.node[n];if(!r.structure)throw new Error("Missed `structure` field in `"+n+"` node type definition");t[n]=sn(n,r)}return t}(e),e){if(e.types)for(var r in e.types)this.addType_(r,e.types[r]);if(e.generic)for(var r in this.generic=!0,ut)this.addType_(r,ut[r]);if(e.atrules)for(var r in e.atrules)this.addAtrule_(r,e.atrules[r]);if(e.properties)for(var r in e.properties)this.addProperty_(r,e.properties[r])}};yn.prototype={structure:{},checkStructure:function(e){function t(e,t){r.push({node:e,message:t})}var n=this.structure,r=[];return this.syntax.walk(e,(function(e){n.hasOwnProperty(e.type)?n[e.type].check(e,t):t(e,"Unknown node type `"+e.type+"`")})),!!r.length&&r},createDescriptor:function(e,t,n,r=null){var a={type:t,name:n},i={type:t,name:n,parent:r,syntax:null,match:null};return"function"==typeof e?i.match=un(e,a):("string"==typeof e?Object.defineProperty(i,"syntax",{get:function(){return Object.defineProperty(i,"syntax",{value:Pt(e)}),i.syntax}}):i.syntax=e,Object.defineProperty(i,"match",{get:function(){return Object.defineProperty(i,"match",{value:un(i.syntax,a)}),i.match}})),i},addAtrule_:function(e,t){t&&(this.atrules[e]={type:"Atrule",name:e,prelude:t.prelude?this.createDescriptor(t.prelude,"AtrulePrelude",e):null,descriptors:t.descriptors?Object.keys(t.descriptors).reduce((n,r)=>(n[r]=this.createDescriptor(t.descriptors[r],"AtruleDescriptor",r,e),n),{}):null})},addProperty_:function(e,t){t&&(this.properties[e]=this.createDescriptor(t,"Property",e))},addType_:function(e,t){t&&(this.types[e]=this.createDescriptor(t,"Type",e),t===ut["-ms-legacy-expression"]&&(this.valueCommonSyntax=pn))},checkAtruleName:function(e){if(!this.getAtrule(e))return new ln("Unknown at-rule","@"+e)},checkAtrulePrelude:function(e,t){let n=this.checkAtruleName(e);if(n)return n;var r=this.getAtrule(e);return!r.prelude&&t?new SyntaxError("At-rule `@"+e+"` should not contain a prelude"):r.prelude&&!t?new SyntaxError("At-rule `@"+e+"` should contain a prelude"):void 0},checkAtruleDescriptorName:function(e,t){let n=this.checkAtruleName(e);if(n)return n;var r=this.getAtrule(e),a=ne.keyword(t);return r.descriptors?r.descriptors[a.name]||r.descriptors[a.basename]?void 0:new ln("Unknown at-rule descriptor",t):new SyntaxError("At-rule `@"+e+"` has no known descriptors")},checkPropertyName:function(e){return ne.property(e).custom?new Error("Lexer matching doesn't applicable for custom properties"):this.getProperty(e)?void 0:new ln("Unknown property",e)},matchAtrulePrelude:function(e,t){var n=this.checkAtrulePrelude(e,t);return n?gn(null,n):t?bn(this,this.getAtrule(e).prelude,t,!0):gn(null,null)},matchAtruleDescriptor:function(e,t,n){var r=this.checkAtruleDescriptorName(e,t);if(r)return gn(null,r);var a=this.getAtrule(e),i=ne.keyword(t);return bn(this,a.descriptors[i.name]||a.descriptors[i.basename],n,!0)},matchDeclaration:function(e){return"Declaration"!==e.type?gn(null,new Error("Not a Declaration node")):this.matchProperty(e.property,e.value)},matchProperty:function(e,t){var n=this.checkPropertyName(e);return n?gn(null,n):bn(this,this.getProperty(e),t,!0)},matchType:function(e,t){var n=this.getType(e);return n?bn(this,n,t,!1):gn(null,new ln("Unknown type",e))},match:function(e,t){return"string"==typeof e||e&&e.type?("string"!=typeof e&&e.match||(e=this.createDescriptor(e,"Type","anonymous")),bn(this,e,t,!1)):gn(null,new ln("Bad syntax"))},findValueFragments:function(e,t,n,r){return tn(this,t,this.matchProperty(e,t),n,r)},findDeclarationValueFragments:function(e,t,n){return tn(this,e.value,this.matchDeclaration(e),t,n)},findAllFragments:function(e,t,n){var r=[];return this.syntax.walk(e,{visit:"Declaration",enter:function(e){r.push.apply(r,this.findDeclarationValueFragments(e,t,n))}.bind(this)}),r},getAtrule:function(e,t=!0){var n=ne.keyword(e);return(n.vendor&&t?this.atrules[n.name]||this.atrules[n.basename]:this.atrules[n.name])||null},getAtrulePrelude:function(e,t=!0){const n=this.getAtrule(e,t);return n&&n.prelude||null},getAtruleDescriptor:function(e,t){return this.atrules.hasOwnProperty(e)&&this.atrules.declarators&&this.atrules[e].declarators[t]||null},getProperty:function(e,t=!0){var n=ne.property(e);return(n.vendor&&t?this.properties[n.name]||this.properties[n.basename]:this.properties[n.name])||null},getType:function(e){return this.types.hasOwnProperty(e)?this.types[e]:null},validate:function(){function e(r,a,i,o){if(i.hasOwnProperty(a))return i[a];i[a]=!1,null!==o.syntax&&Et(o.syntax,(function(o){if("Type"===o.type||"Property"===o.type){var s="Type"===o.type?r.types:r.properties,l="Type"===o.type?t:n;s.hasOwnProperty(o.name)&&!e(r,o.name,l,s[o.name])||(i[a]=!0)}}),this)}var t={},n={};for(var r in this.types)e(this,r,t,this.types[r]);for(var r in this.properties)e(this,r,n,this.properties[r]);return t=Object.keys(t).filter((function(e){return t[e]})),n=Object.keys(n).filter((function(e){return n[e]})),t.length||n.length?{types:t,properties:n}:null},dump:function(e,t){return{generic:this.generic,types:mn(this.types,!t,e),properties:mn(this.properties,!t,e),atrules:fn(this.atrules,!t,e)}},toString:function(){return JSON.stringify(this.dump())}};var vn=yn,kn={SyntaxError:ht,parse:Pt,generate:K,walk:Et},wn=we.isBOM,xn=function(){this.lines=null,this.columns=null,this.linesAndColumnsComputed=!1};xn.prototype={setSource:function(e,t,n,r){this.source=e,this.startOffset=void 0===t?0:t,this.startLine=void 0===n?1:n,this.startColumn=void 0===r?1:r,this.linesAndColumnsComputed=!1},ensureLinesAndColumnsComputed:function(){this.linesAndColumnsComputed||(function(e,t){for(var n=t.length,r=ae(e.lines,n),a=e.startLine,i=ae(e.columns,n),o=e.startColumn,s=t.length>0?wn(t.charCodeAt(0)):0;s<n;s++){var l=t.charCodeAt(s);r[s]=a,i[s]=o++,10!==l&&13!==l&&12!==l||(13===l&&s+1<n&&10===t.charCodeAt(s+1)&&(r[++s]=a,i[s]=o),a++,o=1)}r[s]=a,i[s]=o,e.lines=r,e.columns=i}(this,this.source),this.linesAndColumnsComputed=!0)},getLocation:function(e,t){return this.ensureLinesAndColumnsComputed(),{source:t,offset:this.startOffset+e,line:this.lines[e],column:this.columns[e]}},getLocationRange:function(e,t,n){return this.ensureLinesAndColumnsComputed(),{source:n,start:{offset:this.startOffset+e,line:this.lines[e],column:this.columns[e]},end:{offset:this.startOffset+t,line:this.lines[t],column:this.columns[t]}}}};var Sn=xn,Cn=we.TYPE,An=Cn.WhiteSpace,zn=Cn.Comment,Pn=function(e){var t=this.createList(),n=null,r={recognizer:e,space:null,ignoreWS:!1,ignoreWSAfter:!1};for(this.scanner.skipSC();!this.scanner.eof;){switch(this.scanner.tokenType){case zn:this.scanner.next();continue;case An:r.ignoreWS?this.scanner.next():r.space=this.WhiteSpace();continue}if(void 0===(n=e.getNode.call(this,r)))break;null!==r.space&&(t.push(r.space),r.space=null),t.push(n),r.ignoreWSAfter?(r.ignoreWSAfter=!1,r.ignoreWS=!0):r.ignoreWS=!1}return t},{findWhiteSpaceStart:Ln,cmpStr:Tn}=M,En=function(){},On=h.TYPE,Dn=h.NAME,In=On.WhiteSpace,Rn=On.Comment,Nn=On.Ident,Bn=On.Function,Mn=On.Url,jn=On.Hash,_n=On.Percentage,qn=On.Number;function Wn(e){return function(){return this[e]()}}var Fn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),Un=function(e){if(0<=e&&e<Fn.length)return Fn[e];throw new TypeError("Must be between 0 and 63: "+e)},Yn=function(e){var t,n="",r=function(e){return e<0?1+(-e<<1):0+(e<<1)}(e);do{t=31&r,(r>>>=5)>0&&(t|=32),n+=Un(t)}while(r>0);return n},Hn=function(e,t){return function(e,t){t.getArg=function(e,t,n){if(t in e)return e[t];if(3===arguments.length)return n;throw new Error('"'+t+'" is a required argument.')};var n=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,r=/^data:.+\,.+$/;function a(e){var t=e.match(n);return t?{scheme:t[1],auth:t[2],host:t[3],port:t[4],path:t[5]}:null}function i(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}function o(e){var n=e,r=a(e);if(r){if(!r.path)return e;n=r.path}for(var o,s=t.isAbsolute(n),l=n.split(/\/+/),c=0,u=l.length-1;u>=0;u--)"."===(o=l[u])?l.splice(u,1):".."===o?c++:c>0&&(""===o?(l.splice(u+1,c),c=0):(l.splice(u,2),c--));return""===(n=l.join("/"))&&(n=s?"/":"."),r?(r.path=n,i(r)):n}function s(e,t){""===e&&(e="."),""===t&&(t=".");var n=a(t),s=a(e);if(s&&(e=s.path||"/"),n&&!n.scheme)return s&&(n.scheme=s.scheme),i(n);if(n||t.match(r))return t;if(s&&!s.host&&!s.path)return s.host=t,i(s);var l="/"===t.charAt(0)?t:o(e.replace(/\/+$/,"")+"/"+t);return s?(s.path=l,i(s)):l}t.urlParse=a,t.urlGenerate=i,t.normalize=o,t.join=s,t.isAbsolute=function(e){return"/"===e.charAt(0)||n.test(e)},t.relative=function(e,t){""===e&&(e="."),e=e.replace(/\/$/,"");for(var n=0;0!==t.indexOf(e+"/");){var r=e.lastIndexOf("/");if(r<0)return t;if((e=e.slice(0,r)).match(/^([^\/]+:\/)?\/*$/))return t;++n}return Array(n+1).join("../")+t.substr(e.length+1)};var l=!("__proto__"in Object.create(null));function c(e){return e}function u(e){if(!e)return!1;var t=e.length;if(t<9)return!1;if(95!==e.charCodeAt(t-1)||95!==e.charCodeAt(t-2)||111!==e.charCodeAt(t-3)||116!==e.charCodeAt(t-4)||111!==e.charCodeAt(t-5)||114!==e.charCodeAt(t-6)||112!==e.charCodeAt(t-7)||95!==e.charCodeAt(t-8)||95!==e.charCodeAt(t-9))return!1;for(var n=t-10;n>=0;n--)if(36!==e.charCodeAt(n))return!1;return!0}function h(e,t){return e===t?0:null===e?1:null===t?-1:e>t?1:-1}t.toSetString=l?c:function(e){return u(e)?"$"+e:e},t.fromSetString=l?c:function(e){return u(e)?e.slice(1):e},t.compareByOriginalPositions=function(e,t,n){var r=h(e.source,t.source);return 0!==r||0!=(r=e.originalLine-t.originalLine)||0!=(r=e.originalColumn-t.originalColumn)||n||0!=(r=e.generatedColumn-t.generatedColumn)||0!=(r=e.generatedLine-t.generatedLine)?r:h(e.name,t.name)},t.compareByGeneratedPositionsDeflated=function(e,t,n){var r=e.generatedLine-t.generatedLine;return 0!==r||0!=(r=e.generatedColumn-t.generatedColumn)||n||0!==(r=h(e.source,t.source))||0!=(r=e.originalLine-t.originalLine)||0!=(r=e.originalColumn-t.originalColumn)?r:h(e.name,t.name)},t.compareByGeneratedPositionsInflated=function(e,t){var n=e.generatedLine-t.generatedLine;return 0!==n||0!=(n=e.generatedColumn-t.generatedColumn)||0!==(n=h(e.source,t.source))||0!=(n=e.originalLine-t.originalLine)||0!=(n=e.originalColumn-t.originalColumn)?n:h(e.name,t.name)},t.parseSourceMapInput=function(e){return JSON.parse(e.replace(/^\)]}'[^\n]*\n/,""))},t.computeSourceURL=function(e,t,n){if(t=t||"",e&&("/"!==e[e.length-1]&&"/"!==t[0]&&(e+="/"),t=e+t),n){var r=a(n);if(!r)throw new Error("sourceMapURL could not be parsed");if(r.path){var l=r.path.lastIndexOf("/");l>=0&&(r.path=r.path.substring(0,l+1))}t=s(i(r),t)}return o(t)}}(t={exports:{}},t.exports),t.exports}(),Vn=(Hn.getArg,Hn.urlParse,Hn.urlGenerate,Hn.normalize,Hn.join,Hn.isAbsolute,Hn.relative,Hn.toSetString,Hn.fromSetString,Hn.compareByOriginalPositions,Hn.compareByGeneratedPositionsDeflated,Hn.compareByGeneratedPositionsInflated,Hn.parseSourceMapInput,Hn.computeSourceURL,Object.prototype.hasOwnProperty),Kn="undefined"!=typeof Map;function Gn(){this._array=[],this._set=Kn?new Map:Object.create(null)}Gn.fromArray=function(e,t){for(var n=new Gn,r=0,a=e.length;r<a;r++)n.add(e[r],t);return n},Gn.prototype.size=function(){return Kn?this._set.size:Object.getOwnPropertyNames(this._set).length},Gn.prototype.add=function(e,t){var n=Kn?e:Hn.toSetString(e),r=Kn?this.has(e):Vn.call(this._set,n),a=this._array.length;r&&!t||this._array.push(e),r||(Kn?this._set.set(e,a):this._set[n]=a)},Gn.prototype.has=function(e){if(Kn)return this._set.has(e);var t=Hn.toSetString(e);return Vn.call(this._set,t)},Gn.prototype.indexOf=function(e){if(Kn){var t=this._set.get(e);if(t>=0)return t}else{var n=Hn.toSetString(e);if(Vn.call(this._set,n))return this._set[n]}throw new Error('"'+e+'" is not in the set.')},Gn.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)},Gn.prototype.toArray=function(){return this._array.slice()};var $n=Gn;function Qn(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}Qn.prototype.unsortedForEach=function(e,t){this._array.forEach(e,t)},Qn.prototype.add=function(e){var t,n,r,a,i,o;n=e,r=(t=this._last).generatedLine,a=n.generatedLine,i=t.generatedColumn,o=n.generatedColumn,a>r||a==r&&o>=i||Hn.compareByGeneratedPositionsInflated(t,n)<=0?(this._last=e,this._array.push(e)):(this._sorted=!1,this._array.push(e))},Qn.prototype.toArray=function(){return this._sorted||(this._array.sort(Hn.compareByGeneratedPositionsInflated),this._sorted=!0),this._array};var Xn=$n,Zn={MappingList:Qn}.MappingList;function Jn(e){e||(e={}),this._file=Hn.getArg(e,"file",null),this._sourceRoot=Hn.getArg(e,"sourceRoot",null),this._skipValidation=Hn.getArg(e,"skipValidation",!1),this._sources=new Xn,this._names=new Xn,this._mappings=new Zn,this._sourcesContents=null}Jn.prototype._version=3,Jn.fromSourceMap=function(e){var t=e.sourceRoot,n=new Jn({file:e.file,sourceRoot:t});return e.eachMapping((function(e){var r={generated:{line:e.generatedLine,column:e.generatedColumn}};null!=e.source&&(r.source=e.source,null!=t&&(r.source=Hn.relative(t,r.source)),r.original={line:e.originalLine,column:e.originalColumn},null!=e.name&&(r.name=e.name)),n.addMapping(r)})),e.sources.forEach((function(r){var a=r;null!==t&&(a=Hn.relative(t,r)),n._sources.has(a)||n._sources.add(a);var i=e.sourceContentFor(r);null!=i&&n.setSourceContent(r,i)})),n},Jn.prototype.addMapping=function(e){var t=Hn.getArg(e,"generated"),n=Hn.getArg(e,"original",null),r=Hn.getArg(e,"source",null),a=Hn.getArg(e,"name",null);this._skipValidation||this._validateMapping(t,n,r,a),null!=r&&(r=String(r),this._sources.has(r)||this._sources.add(r)),null!=a&&(a=String(a),this._names.has(a)||this._names.add(a)),this._mappings.add({generatedLine:t.line,generatedColumn:t.column,originalLine:null!=n&&n.line,originalColumn:null!=n&&n.column,source:r,name:a})},Jn.prototype.setSourceContent=function(e,t){var n=e;null!=this._sourceRoot&&(n=Hn.relative(this._sourceRoot,n)),null!=t?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[Hn.toSetString(n)]=t):this._sourcesContents&&(delete this._sourcesContents[Hn.toSetString(n)],0===Object.keys(this._sourcesContents).length&&(this._sourcesContents=null))},Jn.prototype.applySourceMap=function(e,t,n){var r=t;if(null==t){if(null==e.file)throw new Error('SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map\'s "file" property. Both were omitted.');r=e.file}var a=this._sourceRoot;null!=a&&(r=Hn.relative(a,r));var i=new Xn,o=new Xn;this._mappings.unsortedForEach((function(t){if(t.source===r&&null!=t.originalLine){var s=e.originalPositionFor({line:t.originalLine,column:t.originalColumn});null!=s.source&&(t.source=s.source,null!=n&&(t.source=Hn.join(n,t.source)),null!=a&&(t.source=Hn.relative(a,t.source)),t.originalLine=s.line,t.originalColumn=s.column,null!=s.name&&(t.name=s.name))}var l=t.source;null==l||i.has(l)||i.add(l);var c=t.name;null==c||o.has(c)||o.add(c)}),this),this._sources=i,this._names=o,e.sources.forEach((function(t){var r=e.sourceContentFor(t);null!=r&&(null!=n&&(t=Hn.join(n,t)),null!=a&&(t=Hn.relative(a,t)),this.setSourceContent(t,r))}),this)},Jn.prototype._validateMapping=function(e,t,n,r){if(t&&"number"!=typeof t.line&&"number"!=typeof t.column)throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!(e&&"line"in e&&"column"in e&&e.line>0&&e.column>=0)||t||n||r)&&!(e&&"line"in e&&"column"in e&&t&&"line"in t&&"column"in t&&e.line>0&&e.column>=0&&t.line>0&&t.column>=0&&n))throw new Error("Invalid mapping: "+JSON.stringify({generated:e,source:n,original:t,name:r}))},Jn.prototype._serializeMappings=function(){for(var e,t,n,r,a=0,i=1,o=0,s=0,l=0,c=0,u="",h=this._mappings.toArray(),d=0,p=h.length;d<p;d++){if(e="",(t=h[d]).generatedLine!==i)for(a=0;t.generatedLine!==i;)e+=";",i++;else if(d>0){if(!Hn.compareByGeneratedPositionsInflated(t,h[d-1]))continue;e+=","}e+=Yn(t.generatedColumn-a),a=t.generatedColumn,null!=t.source&&(r=this._sources.indexOf(t.source),e+=Yn(r-c),c=r,e+=Yn(t.originalLine-1-s),s=t.originalLine-1,e+=Yn(t.originalColumn-o),o=t.originalColumn,null!=t.name&&(n=this._names.indexOf(t.name),e+=Yn(n-l),l=n)),u+=e}return u},Jn.prototype._generateSourcesContent=function(e,t){return e.map((function(e){if(!this._sourcesContents)return null;null!=t&&(e=Hn.relative(t,e));var n=Hn.toSetString(e);return Object.prototype.hasOwnProperty.call(this._sourcesContents,n)?this._sourcesContents[n]:null}),this)},Jn.prototype.toJSON=function(){var e={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return null!=this._file&&(e.file=this._file),null!=this._sourceRoot&&(e.sourceRoot=this._sourceRoot),this._sourcesContents&&(e.sourcesContent=this._generateSourcesContent(e.sources,e.sourceRoot)),e},Jn.prototype.toString=function(){return JSON.stringify(this.toJSON())};var er={SourceMapGenerator:Jn}.SourceMapGenerator,tr={Atrule:!0,Selector:!0,Declaration:!0},nr=Object.prototype.hasOwnProperty;function rr(e,t){var n=e.children,r=null;"function"!=typeof t?n.forEach(this.node,this):n.forEach((function(e){null!==r&&t.call(this,r),this.node(e),r=e}),this)}var ar=Object.prototype.hasOwnProperty,ir=function(){};function or(e){return"function"==typeof e?e:ir}function sr(e,t){return function(n,r,a){n.type===t&&e.call(this,n,r,a)}}function lr(e,t){var n=t.structure,r=[];for(var a in n)if(!1!==ar.call(n,a)){var i=n[a],o={name:a,type:!1,nullable:!1};Array.isArray(n[a])||(i=[n[a]]);for(var s=0;s<i.length;s++){var l=i[s];null===l?o.nullable=!0:"string"==typeof l?o.type="node":Array.isArray(l)&&(o.type="list")}o.type&&r.push(o)}return r.length?{context:t.walkContext,fields:r}:null}function cr(e,t){var n=e.fields.slice(),r=e.context,a="string"==typeof r;return t&&n.reverse(),function(e,i,o,s){var l;a&&(l=i[r],i[r]=e);for(var c=0;c<n.length;c++){var u=n[c],h=e[u.name];if(!u.nullable||h)if("list"===u.type){if(t?h.reduceRight(s,!1):h.reduce(s,!1))return!0}else if(o(h))return!0}a&&(i[r]=l)}}function ur(e){return{Atrule:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block},Rule:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block},Declaration:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block,DeclarationList:e.DeclarationList}}}var hr=function e(t){var n={};for(var r in t){var a=t[r];a&&(Array.isArray(a)||a instanceof i?a=a.map(e):a.constructor===Object&&(a=e(a))),n[r]=a}return n};const dr=Object.prototype.hasOwnProperty,pr={generic:!0,types:br,atrules:{prelude:yr,descriptors:yr},properties:br,parseContext:function(e,t){return Object.assign(e,t)},scope:function e(t,n){for(const r in n)dr.call(n,r)&&(mr(t[r])?e(t[r],fr(n[r])):t[r]=fr(n[r]));return t},atrule:["parse"],pseudo:["parse"],node:["name","structure","parse","generate","walkContext"]};function mr(e){return e&&e.constructor===Object}function fr(e){return mr(e)?Object.assign({},e):e}function gr(e,t){return"string"==typeof t&&/^\s*\|/.test(t)?"string"==typeof e?e+t:t.replace(/^\s*\|\s*/,""):t||null}function br(e,t){if("string"==typeof t)return gr(e,t);const n=Object.assign({},e);for(let r in t)dr.call(t,r)&&(n[r]=gr(dr.call(e,r)?e[r]:void 0,t[r]));return n}function yr(e,t){const n=br(e,t);return!mr(n)||Object.keys(n).length?n:null}var vr=(e,t)=>function e(t,n,r){for(const a in r)if(!1!==dr.call(r,a))if(!0===r[a])a in n&&dr.call(n,a)&&(t[a]=fr(n[a]));else if(r[a])if("function"==typeof r[a]){const e=r[a];t[a]=e({},t[a]),t[a]=e(t[a]||{},n[a])}else if(mr(r[a])){const i={};for(let n in t[a])i[n]=e({},t[a][n],r[a]);for(let t in n[a])i[t]=e(i[t]||{},n[a][t],r[a]);t[a]=i}else if(Array.isArray(r[a])){const i={},o=r[a].reduce((function(e,t){return e[t]=!0,e}),{});for(const[n,r]of Object.entries(t[a]||{}))i[n]={},r&&e(i[n],r,o);for(const t in n[a])dr.call(n[a],t)&&(i[t]||(i[t]={}),n[a]&&n[a][t]&&e(i[t],n[a][t],o));t[a]=i}return t}(e,t,pr);function kr(e){var t=function(e){var t={scanner:new H,locationMap:new Sn,filename:"<unknown>",needPositions:!1,onParseError:En,onParseErrorThrow:!1,parseAtrulePrelude:!0,parseRulePrelude:!0,parseValue:!0,parseCustomProperty:!1,readSequence:Pn,createList:function(){return new i},createSingleNodeList:function(e){return(new i).appendData(e)},getFirstListNode:function(e){return e&&e.first()},getLastListNode:function(e){return e.last()},parseWithFallback:function(e,t){var n=this.scanner.tokenIndex;try{return e.call(this)}catch(e){if(this.onParseErrorThrow)throw e;var r=t.call(this,n);return this.onParseErrorThrow=!0,this.onParseError(e,r),this.onParseErrorThrow=!1,r}},lookupNonWSType:function(e){do{var t=this.scanner.lookupType(e++);if(t!==In)return t}while(0!==t);return 0},eat:function(e){if(this.scanner.tokenType!==e){var t=this.scanner.tokenStart,n=Dn[e]+" is expected";switch(e){case Nn:this.scanner.tokenType===Bn||this.scanner.tokenType===Mn?(t=this.scanner.tokenEnd-1,n="Identifier is expected but function found"):n="Identifier is expected";break;case jn:this.scanner.isDelim(35)&&(this.scanner.next(),t++,n="Name is expected");break;case _n:this.scanner.tokenType===qn&&(t=this.scanner.tokenEnd,n="Percent sign is expected");break;default:this.scanner.source.charCodeAt(this.scanner.tokenStart)===e&&(t+=1)}this.error(n,t)}this.scanner.next()},consume:function(e){var t=this.scanner.getTokenValue();return this.eat(e),t},consumeFunctionName:function(){var e=this.scanner.source.substring(this.scanner.tokenStart,this.scanner.tokenEnd-1);return this.eat(Bn),e},getLocation:function(e,t){return this.needPositions?this.locationMap.getLocationRange(e,t,this.filename):null},getLocationFromList:function(e){if(this.needPositions){var t=this.getFirstListNode(e),n=this.getLastListNode(e);return this.locationMap.getLocationRange(null!==t?t.loc.start.offset-this.locationMap.startOffset:this.scanner.tokenStart,null!==n?n.loc.end.offset-this.locationMap.startOffset:this.scanner.tokenStart,this.filename)}return null},error:function(e,t){var n=void 0!==t&&t<this.scanner.source.length?this.locationMap.getLocation(t):this.scanner.eof?this.locationMap.getLocation(Ln(this.scanner.source,this.scanner.source.length-1)):this.locationMap.getLocation(this.scanner.tokenStart);throw new l(e||"Unexpected input",this.scanner.source,n.offset,n.line,n.column)}};for(var n in e=function(e){var t={context:{},scope:{},atrule:{},pseudo:{}};if(e.parseContext)for(var n in e.parseContext)switch(typeof e.parseContext[n]){case"function":t.context[n]=e.parseContext[n];break;case"string":t.context[n]=Wn(e.parseContext[n])}if(e.scope)for(var n in e.scope)t.scope[n]=e.scope[n];if(e.atrule)for(var n in e.atrule){var r=e.atrule[n];r.parse&&(t.atrule[n]=r.parse)}if(e.pseudo)for(var n in e.pseudo){var a=e.pseudo[n];a.parse&&(t.pseudo[n]=a.parse)}if(e.node)for(var n in e.node)t[n]=e.node[n].parse;return t}(e||{}))t[n]=e[n];return function(e,n){var r,a=(n=n||{}).context||"default",i=n.onComment;if(we(e,t.scanner),t.locationMap.setSource(e,n.offset,n.line,n.column),t.filename=n.filename||"<unknown>",t.needPositions=Boolean(n.positions),t.onParseError="function"==typeof n.onParseError?n.onParseError:En,t.onParseErrorThrow=!1,t.parseAtrulePrelude=!("parseAtrulePrelude"in n)||Boolean(n.parseAtrulePrelude),t.parseRulePrelude=!("parseRulePrelude"in n)||Boolean(n.parseRulePrelude),t.parseValue=!("parseValue"in n)||Boolean(n.parseValue),t.parseCustomProperty="parseCustomProperty"in n&&Boolean(n.parseCustomProperty),!t.context.hasOwnProperty(a))throw new Error("Unknown context `"+a+"`");return"function"==typeof i&&t.scanner.forEachToken((n,r,a)=>{if(n===Rn){const n=t.getLocation(r,a),o=Tn(e,a-2,a,"*/")?e.slice(r+2,a-2):e.slice(r+2,a);i(o,n)}}),r=t.context[a].call(t,n),t.scanner.eof||t.error(),r}}(e),n=function(e){var t=function(e){var t={};for(var n in e.node)if(ar.call(e.node,n)){var r=e.node[n];if(!r.structure)throw new Error("Missed `structure` field in `"+n+"` node type definition");t[n]=lr(0,r)}return t}(e),n={},r={},a=Symbol("break-walk"),i=Symbol("skip-node");for(var o in t)ar.call(t,o)&&null!==t[o]&&(n[o]=cr(t[o],!1),r[o]=cr(t[o],!0));var s=ur(n),l=ur(r),c=function(e,o){function c(e,t,n){var r=h.call(m,e,t,n);return r===a||r!==i&&(!(!p.hasOwnProperty(e.type)||!p[e.type](e,m,c,u))||d.call(m,e,t,n)===a)}var u=(e,t,n,r)=>e||c(t,n,r),h=ir,d=ir,p=n,m={break:a,skip:i,root:e,stylesheet:null,atrule:null,atrulePrelude:null,rule:null,selector:null,block:null,declaration:null,function:null};if("function"==typeof o)h=o;else if(o&&(h=or(o.enter),d=or(o.leave),o.reverse&&(p=r),o.visit)){if(s.hasOwnProperty(o.visit))p=o.reverse?l[o.visit]:s[o.visit];else if(!t.hasOwnProperty(o.visit))throw new Error("Bad value `"+o.visit+"` for `visit` option (should be: "+Object.keys(t).join(", ")+")");h=sr(h,o.visit),d=sr(d,o.visit)}if(h===ir&&d===ir)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");c(e)};return c.break=a,c.skip=i,c.find=function(e,t){var n=null;return c(e,(function(e,r,i){if(t.call(this,e,r,i))return n=e,a})),n},c.findLast=function(e,t){var n=null;return c(e,{reverse:!0,enter:function(e,r,i){if(t.call(this,e,r,i))return n=e,a}}),n},c.findAll=function(e,t){var n=[];return c(e,(function(e,r,a){t.call(this,e,r,a)&&n.push(e)})),n},c}(e),r=function(e){function t(e){if(!nr.call(n,e.type))throw new Error("Unknown node type: "+e.type);n[e.type].call(this,e)}var n={};if(e.node)for(var r in e.node)n[r]=e.node[r].generate;return function(e,n){var r="",a={children:rr,node:t,chunk:function(e){r+=e},result:function(){return r}};return n&&("function"==typeof n.decorator&&(a=n.decorator(a)),n.sourceMap&&(a=function(e){var t=new er,n=1,r=0,a={line:1,column:0},i={line:0,column:0},o=!1,s={line:1,column:0},l={generated:s},c=e.node;e.node=function(e){if(e.loc&&e.loc.start&&tr.hasOwnProperty(e.type)){var u=e.loc.start.line,h=e.loc.start.column-1;i.line===u&&i.column===h||(i.line=u,i.column=h,a.line=n,a.column=r,o&&(o=!1,a.line===s.line&&a.column===s.column||t.addMapping(l)),o=!0,t.addMapping({source:e.loc.source,original:i,generated:a}))}c.call(this,e),o&&tr.hasOwnProperty(e.type)&&(s.line=n,s.column=r)};var u=e.chunk;e.chunk=function(e){for(var t=0;t<e.length;t++)10===e.charCodeAt(t)?(n++,r=0):r++;u(e)};var h=e.result;return e.result=function(){return o&&t.addMapping(l),{css:h(),map:t}},e}(a))),a.node(e),a.result()}}(e),a=function(e){return{fromPlainObject:function(t){return e(t,{enter:function(e){e.children&&e.children instanceof i==0&&(e.children=(new i).fromArray(e.children))}}),t},toPlainObject:function(t){return e(t,{leave:function(e){e.children&&e.children instanceof i&&(e.children=e.children.toArray())}}),t}}}(n),o={List:i,SyntaxError:l,TokenStream:H,Lexer:vn,vendorPrefix:ne.vendorPrefix,keyword:ne.keyword,property:ne.property,isCustomProperty:ne.isCustomProperty,definitionSyntax:kn,lexer:null,createLexer:function(e){return new vn(e,o,o.lexer.structure)},tokenize:we,parse:t,walk:n,generate:r,find:n.find,findLast:n.findLast,findAll:n.findAll,clone:hr,fromPlainObject:a.fromPlainObject,toPlainObject:a.toPlainObject,createSyntax:function(e){return kr(vr({},e))},fork:function(t){var n=vr({},e);return kr("function"==typeof t?t(n,Object.assign):vr(n,t))}};return o.lexer=new vn({generic:!0,types:e.types,atrules:e.atrules,properties:e.properties,node:e.node},o),o}var wr={generic:!0,types:{"absolute-size":"xx-small|x-small|small|medium|large|x-large|xx-large|xxx-large","alpha-value":"<number>|<percentage>","angle-percentage":"<angle>|<percentage>","angular-color-hint":"<angle-percentage>","angular-color-stop":"<color>&&<color-stop-angle>?","angular-color-stop-list":"[<angular-color-stop> [, <angular-color-hint>]?]# , <angular-color-stop>","animateable-feature":"scroll-position|contents|<custom-ident>",attachment:"scroll|fixed|local","attr()":"attr( <attr-name> <type-or-unit>? [, <attr-fallback>]? )","attr-matcher":"['~'|'|'|'^'|'$'|'*']? '='","attr-modifier":"i|s","attribute-selector":"'[' <wq-name> ']'|'[' <wq-name> <attr-matcher> [<string-token>|<ident-token>] <attr-modifier>? ']'","auto-repeat":"repeat( [auto-fill|auto-fit] , [<line-names>? <fixed-size>]+ <line-names>? )","auto-track-list":"[<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>? <auto-repeat> [<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>?","baseline-position":"[first|last]? baseline","basic-shape":"<inset()>|<circle()>|<ellipse()>|<polygon()>|<path()>","bg-image":"none|<image>","bg-layer":"<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","bg-position":"[[left|center|right|top|bottom|<length-percentage>]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]|[center|[left|right] <length-percentage>?]&&[center|[top|bottom] <length-percentage>?]]","bg-size":"[<length-percentage>|auto]{1,2}|cover|contain","blur()":"blur( <length> )","blend-mode":"normal|multiply|screen|overlay|darken|lighten|color-dodge|color-burn|hard-light|soft-light|difference|exclusion|hue|saturation|color|luminosity",box:"border-box|padding-box|content-box","brightness()":"brightness( <number-percentage> )","calc()":"calc( <calc-sum> )","calc-sum":"<calc-product> [['+'|'-'] <calc-product>]*","calc-product":"<calc-value> ['*' <calc-value>|'/' <number>]*","calc-value":"<number>|<dimension>|<percentage>|( <calc-sum> )","cf-final-image":"<image>|<color>","cf-mixing-image":"<percentage>?&&<image>","circle()":"circle( [<shape-radius>]? [at <position>]? )","clamp()":"clamp( <calc-sum>#{3} )","class-selector":"'.' <ident-token>","clip-source":"<url>",color:"<rgb()>|<rgba()>|<hsl()>|<hsla()>|<hex-color>|<named-color>|currentcolor|<deprecated-system-color>","color-stop":"<color-stop-length>|<color-stop-angle>","color-stop-angle":"<angle-percentage>{1,2}","color-stop-length":"<length-percentage>{1,2}","color-stop-list":"[<linear-color-stop> [, <linear-color-hint>]?]# , <linear-color-stop>",combinator:"'>'|'+'|'~'|['||']","common-lig-values":"[common-ligatures|no-common-ligatures]","compat-auto":"searchfield|textarea|push-button|slider-horizontal|checkbox|radio|square-button|menulist|listbox|meter|progress-bar|button","composite-style":"clear|copy|source-over|source-in|source-out|source-atop|destination-over|destination-in|destination-out|destination-atop|xor","compositing-operator":"add|subtract|intersect|exclude","compound-selector":"[<type-selector>? <subclass-selector>* [<pseudo-element-selector> <pseudo-class-selector>*]*]!","compound-selector-list":"<compound-selector>#","complex-selector":"<compound-selector> [<combinator>? <compound-selector>]*","complex-selector-list":"<complex-selector>#","conic-gradient()":"conic-gradient( [from <angle>]? [at <position>]? , <angular-color-stop-list> )","contextual-alt-values":"[contextual|no-contextual]","content-distribution":"space-between|space-around|space-evenly|stretch","content-list":"[<string>|contents|<image>|<quote>|<target>|<leader()>|<attr()>|counter( <ident> , <'list-style-type'>? )]+","content-position":"center|start|end|flex-start|flex-end","content-replacement":"<image>","contrast()":"contrast( [<number-percentage>] )","counter()":"counter( <custom-ident> , <counter-style>? )","counter-style":"<counter-style-name>|symbols( )","counter-style-name":"<custom-ident>","counters()":"counters( <custom-ident> , <string> , <counter-style>? )","cross-fade()":"cross-fade( <cf-mixing-image> , <cf-final-image>? )","cubic-bezier-timing-function":"ease|ease-in|ease-out|ease-in-out|cubic-bezier( <number [0,1]> , <number> , <number [0,1]> , <number> )","deprecated-system-color":"ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText","discretionary-lig-values":"[discretionary-ligatures|no-discretionary-ligatures]","display-box":"contents|none","display-inside":"flow|flow-root|table|flex|grid|ruby","display-internal":"table-row-group|table-header-group|table-footer-group|table-row|table-cell|table-column-group|table-column|table-caption|ruby-base|ruby-text|ruby-base-container|ruby-text-container","display-legacy":"inline-block|inline-list-item|inline-table|inline-flex|inline-grid","display-listitem":"<display-outside>?&&[flow|flow-root]?&&list-item","display-outside":"block|inline|run-in","drop-shadow()":"drop-shadow( <length>{2,3} <color>? )","east-asian-variant-values":"[jis78|jis83|jis90|jis04|simplified|traditional]","east-asian-width-values":"[full-width|proportional-width]","element()":"element( <custom-ident> , [first|start|last|first-except]? )|element( <id-selector> )","ellipse()":"ellipse( [<shape-radius>{2}]? [at <position>]? )","ending-shape":"circle|ellipse","env()":"env( <custom-ident> , <declaration-value>? )","explicit-track-list":"[<line-names>? <track-size>]+ <line-names>?","family-name":"<string>|<custom-ident>+","feature-tag-value":"<string> [<integer>|on|off]?","feature-type":"@stylistic|@historical-forms|@styleset|@character-variant|@swash|@ornaments|@annotation","feature-value-block":"<feature-type> '{' <feature-value-declaration-list> '}'","feature-value-block-list":"<feature-value-block>+","feature-value-declaration":"<custom-ident> : <integer>+ ;","feature-value-declaration-list":"<feature-value-declaration>","feature-value-name":"<custom-ident>","fill-rule":"nonzero|evenodd","filter-function":"<blur()>|<brightness()>|<contrast()>|<drop-shadow()>|<grayscale()>|<hue-rotate()>|<invert()>|<opacity()>|<saturate()>|<sepia()>","filter-function-list":"[<filter-function>|<url>]+","final-bg-layer":"<'background-color'>||<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","fit-content()":"fit-content( [<length>|<percentage>] )","fixed-breadth":"<length-percentage>","fixed-repeat":"repeat( [<positive-integer>] , [<line-names>? <fixed-size>]+ <line-names>? )","fixed-size":"<fixed-breadth>|minmax( <fixed-breadth> , <track-breadth> )|minmax( <inflexible-breadth> , <fixed-breadth> )","font-stretch-absolute":"normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded|<percentage>","font-variant-css21":"[normal|small-caps]","font-weight-absolute":"normal|bold|<number [1,1000]>","frequency-percentage":"<frequency>|<percentage>","general-enclosed":"[<function-token> <any-value> )]|( <ident> <any-value> )","generic-family":"serif|sans-serif|cursive|fantasy|monospace|-apple-system","generic-name":"serif|sans-serif|cursive|fantasy|monospace","geometry-box":"<shape-box>|fill-box|stroke-box|view-box",gradient:"<linear-gradient()>|<repeating-linear-gradient()>|<radial-gradient()>|<repeating-radial-gradient()>|<conic-gradient()>|<-legacy-gradient>","grayscale()":"grayscale( <number-percentage> )","grid-line":"auto|<custom-ident>|[<integer>&&<custom-ident>?]|[span&&[<integer>||<custom-ident>]]","historical-lig-values":"[historical-ligatures|no-historical-ligatures]","hsl()":"hsl( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsl( <hue> , <percentage> , <percentage> , <alpha-value>? )","hsla()":"hsla( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsla( <hue> , <percentage> , <percentage> , <alpha-value>? )",hue:"<number>|<angle>","hue-rotate()":"hue-rotate( <angle> )",image:"<url>|<image()>|<image-set()>|<element()>|<paint()>|<cross-fade()>|<gradient>","image()":"image( <image-tags>? [<image-src>? , <color>?]! )","image-set()":"image-set( <image-set-option># )","image-set-option":"[<image>|<string>] <resolution>","image-src":"<url>|<string>","image-tags":"ltr|rtl","inflexible-breadth":"<length>|<percentage>|min-content|max-content|auto","inset()":"inset( <length-percentage>{1,4} [round <'border-radius'>]? )","invert()":"invert( <number-percentage> )","keyframes-name":"<custom-ident>|<string>","keyframe-block":"<keyframe-selector># { <declaration-list> }","keyframe-block-list":"<keyframe-block>+","keyframe-selector":"from|to|<percentage>","leader()":"leader( <leader-type> )","leader-type":"dotted|solid|space|<string>","length-percentage":"<length>|<percentage>","line-names":"'[' <custom-ident>* ']'","line-name-list":"[<line-names>|<name-repeat>]+","line-style":"none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset","line-width":"<length>|thin|medium|thick","linear-color-hint":"<length-percentage>","linear-color-stop":"<color> <color-stop-length>?","linear-gradient()":"linear-gradient( [<angle>|to <side-or-corner>]? , <color-stop-list> )","mask-layer":"<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||<geometry-box>||[<geometry-box>|no-clip]||<compositing-operator>||<masking-mode>","mask-position":"[<length-percentage>|left|center|right] [<length-percentage>|top|center|bottom]?","mask-reference":"none|<image>|<mask-source>","mask-source":"<url>","masking-mode":"alpha|luminance|match-source","matrix()":"matrix( <number>#{6} )","matrix3d()":"matrix3d( <number>#{16} )","max()":"max( <calc-sum># )","media-and":"<media-in-parens> [and <media-in-parens>]+","media-condition":"<media-not>|<media-and>|<media-or>|<media-in-parens>","media-condition-without-or":"<media-not>|<media-and>|<media-in-parens>","media-feature":"( [<mf-plain>|<mf-boolean>|<mf-range>] )","media-in-parens":"( <media-condition> )|<media-feature>|<general-enclosed>","media-not":"not <media-in-parens>","media-or":"<media-in-parens> [or <media-in-parens>]+","media-query":"<media-condition>|[not|only]? <media-type> [and <media-condition-without-or>]?","media-query-list":"<media-query>#","media-type":"<ident>","mf-boolean":"<mf-name>","mf-name":"<ident>","mf-plain":"<mf-name> : <mf-value>","mf-range":"<mf-name> ['<'|'>']? '='? <mf-value>|<mf-value> ['<'|'>']? '='? <mf-name>|<mf-value> '<' '='? <mf-name> '<' '='? <mf-value>|<mf-value> '>' '='? <mf-name> '>' '='? <mf-value>","mf-value":"<number>|<dimension>|<ident>|<ratio>","min()":"min( <calc-sum># )","minmax()":"minmax( [<length>|<percentage>|min-content|max-content|auto] , [<length>|<percentage>|<flex>|min-content|max-content|auto] )","named-color":"transparent|aliceblue|antiquewhite|aqua|aquamarine|azure|beige|bisque|black|blanchedalmond|blue|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|fuchsia|gainsboro|ghostwhite|gold|goldenrod|gray|green|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|lime|limegreen|linen|magenta|maroon|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|navy|oldlace|olive|olivedrab|orange|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|purple|rebeccapurple|red|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|silver|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|teal|thistle|tomato|turquoise|violet|wheat|white|whitesmoke|yellow|yellowgreen|<-non-standard-color>","namespace-prefix":"<ident>","ns-prefix":"[<ident-token>|'*']? '|'","number-percentage":"<number>|<percentage>","numeric-figure-values":"[lining-nums|oldstyle-nums]","numeric-fraction-values":"[diagonal-fractions|stacked-fractions]","numeric-spacing-values":"[proportional-nums|tabular-nums]",nth:"<an-plus-b>|even|odd","opacity()":"opacity( [<number-percentage>] )","overflow-position":"unsafe|safe","outline-radius":"<length>|<percentage>","page-body":"<declaration>? [; <page-body>]?|<page-margin-box> <page-body>","page-margin-box":"<page-margin-box-type> '{' <declaration-list> '}'","page-margin-box-type":"@top-left-corner|@top-left|@top-center|@top-right|@top-right-corner|@bottom-left-corner|@bottom-left|@bottom-center|@bottom-right|@bottom-right-corner|@left-top|@left-middle|@left-bottom|@right-top|@right-middle|@right-bottom","page-selector-list":"[<page-selector>#]?","page-selector":"<pseudo-page>+|<ident> <pseudo-page>*","path()":"path( [<fill-rule> ,]? <string> )","paint()":"paint( <ident> , <declaration-value>? )","perspective()":"perspective( <length> )","polygon()":"polygon( <fill-rule>? , [<length-percentage> <length-percentage>]# )",position:"[[left|center|right]||[top|center|bottom]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]?|[[left|right] <length-percentage>]&&[[top|bottom] <length-percentage>]]","pseudo-class-selector":"':' <ident-token>|':' <function-token> <any-value> ')'","pseudo-element-selector":"':' <pseudo-class-selector>","pseudo-page":": [left|right|first|blank]",quote:"open-quote|close-quote|no-open-quote|no-close-quote","radial-gradient()":"radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )","relative-selector":"<combinator>? <complex-selector>","relative-selector-list":"<relative-selector>#","relative-size":"larger|smaller","repeat-style":"repeat-x|repeat-y|[repeat|space|round|no-repeat]{1,2}","repeating-linear-gradient()":"repeating-linear-gradient( [<angle>|to <side-or-corner>]? , <color-stop-list> )","repeating-radial-gradient()":"repeating-radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )","rgb()":"rgb( <percentage>{3} [/ <alpha-value>]? )|rgb( <number>{3} [/ <alpha-value>]? )|rgb( <percentage>#{3} , <alpha-value>? )|rgb( <number>#{3} , <alpha-value>? )","rgba()":"rgba( <percentage>{3} [/ <alpha-value>]? )|rgba( <number>{3} [/ <alpha-value>]? )|rgba( <percentage>#{3} , <alpha-value>? )|rgba( <number>#{3} , <alpha-value>? )","rotate()":"rotate( [<angle>|<zero>] )","rotate3d()":"rotate3d( <number> , <number> , <number> , [<angle>|<zero>] )","rotateX()":"rotateX( [<angle>|<zero>] )","rotateY()":"rotateY( [<angle>|<zero>] )","rotateZ()":"rotateZ( [<angle>|<zero>] )","saturate()":"saturate( <number-percentage> )","scale()":"scale( <number> , <number>? )","scale3d()":"scale3d( <number> , <number> , <number> )","scaleX()":"scaleX( <number> )","scaleY()":"scaleY( <number> )","scaleZ()":"scaleZ( <number> )","self-position":"center|start|end|self-start|self-end|flex-start|flex-end","shape-radius":"<length-percentage>|closest-side|farthest-side","skew()":"skew( [<angle>|<zero>] , [<angle>|<zero>]? )","skewX()":"skewX( [<angle>|<zero>] )","skewY()":"skewY( [<angle>|<zero>] )","sepia()":"sepia( <number-percentage> )",shadow:"inset?&&<length>{2,4}&&<color>?","shadow-t":"[<length>{2,3}&&<color>?]",shape:"rect( <top> , <right> , <bottom> , <left> )|rect( <top> <right> <bottom> <left> )","shape-box":"<box>|margin-box","side-or-corner":"[left|right]||[top|bottom]","single-animation":"<time>||<timing-function>||<time>||<single-animation-iteration-count>||<single-animation-direction>||<single-animation-fill-mode>||<single-animation-play-state>||[none|<keyframes-name>]","single-animation-direction":"normal|reverse|alternate|alternate-reverse","single-animation-fill-mode":"none|forwards|backwards|both","single-animation-iteration-count":"infinite|<number>","single-animation-play-state":"running|paused","single-transition":"[none|<single-transition-property>]||<time>||<timing-function>||<time>","single-transition-property":"all|<custom-ident>",size:"closest-side|farthest-side|closest-corner|farthest-corner|<length>|<length-percentage>{2}","step-position":"jump-start|jump-end|jump-none|jump-both|start|end","step-timing-function":"step-start|step-end|steps( <integer> [, <step-position>]? )","subclass-selector":"<id-selector>|<class-selector>|<attribute-selector>|<pseudo-class-selector>","supports-condition":"not <supports-in-parens>|<supports-in-parens> [and <supports-in-parens>]*|<supports-in-parens> [or <supports-in-parens>]*","supports-in-parens":"( <supports-condition> )|<supports-feature>|<general-enclosed>","supports-feature":"<supports-decl>|<supports-selector-fn>","supports-decl":"( <declaration> )","supports-selector-fn":"selector( <complex-selector> )",symbol:"<string>|<image>|<custom-ident>",target:"<target-counter()>|<target-counters()>|<target-text()>","target-counter()":"target-counter( [<string>|<url>] , <custom-ident> , <counter-style>? )","target-counters()":"target-counters( [<string>|<url>] , <custom-ident> , <string> , <counter-style>? )","target-text()":"target-text( [<string>|<url>] , [content|before|after|first-letter]? )","time-percentage":"<time>|<percentage>","timing-function":"linear|<cubic-bezier-timing-function>|<step-timing-function>","track-breadth":"<length-percentage>|<flex>|min-content|max-content|auto","track-list":"[<line-names>? [<track-size>|<track-repeat>]]+ <line-names>?","track-repeat":"repeat( [<positive-integer>] , [<line-names>? <track-size>]+ <line-names>? )","track-size":"<track-breadth>|minmax( <inflexible-breadth> , <track-breadth> )|fit-content( [<length>|<percentage>] )","transform-function":"<matrix()>|<translate()>|<translateX()>|<translateY()>|<scale()>|<scaleX()>|<scaleY()>|<rotate()>|<skew()>|<skewX()>|<skewY()>|<matrix3d()>|<translate3d()>|<translateZ()>|<scale3d()>|<scaleZ()>|<rotate3d()>|<rotateX()>|<rotateY()>|<rotateZ()>|<perspective()>","transform-list":"<transform-function>+","translate()":"translate( <length-percentage> , <length-percentage>? )","translate3d()":"translate3d( <length-percentage> , <length-percentage> , <length> )","translateX()":"translateX( <length-percentage> )","translateY()":"translateY( <length-percentage> )","translateZ()":"translateZ( <length> )","type-or-unit":"string|color|url|integer|number|length|angle|time|frequency|cap|ch|em|ex|ic|lh|rlh|rem|vb|vi|vw|vh|vmin|vmax|mm|Q|cm|in|pt|pc|px|deg|grad|rad|turn|ms|s|Hz|kHz|%","type-selector":"<wq-name>|<ns-prefix>? '*'","var()":"var( <custom-property-name> , <declaration-value>? )","viewport-length":"auto|<length-percentage>","wq-name":"<ns-prefix>? <ident-token>","-legacy-gradient":"<-webkit-gradient()>|<-legacy-linear-gradient>|<-legacy-repeating-linear-gradient>|<-legacy-radial-gradient>|<-legacy-repeating-radial-gradient>","-legacy-linear-gradient":"-moz-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-repeating-linear-gradient":"-moz-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-linear-gradient-arguments":"[<angle>|<side-or-corner>]? , <color-stop-list>","-legacy-radial-gradient":"-moz-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-repeating-radial-gradient":"-moz-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-radial-gradient-arguments":"[<position> ,]? [[[<-legacy-radial-gradient-shape>||<-legacy-radial-gradient-size>]|[<length>|<percentage>]{2}] ,]? <color-stop-list>","-legacy-radial-gradient-size":"closest-side|closest-corner|farthest-side|farthest-corner|contain|cover","-legacy-radial-gradient-shape":"circle|ellipse","-non-standard-font":"-apple-system-body|-apple-system-headline|-apple-system-subheadline|-apple-system-caption1|-apple-system-caption2|-apple-system-footnote|-apple-system-short-body|-apple-system-short-headline|-apple-system-short-subheadline|-apple-system-short-caption1|-apple-system-short-footnote|-apple-system-tall-body","-non-standard-color":"-moz-ButtonDefault|-moz-ButtonHoverFace|-moz-ButtonHoverText|-moz-CellHighlight|-moz-CellHighlightText|-moz-Combobox|-moz-ComboboxText|-moz-Dialog|-moz-DialogText|-moz-dragtargetzone|-moz-EvenTreeRow|-moz-Field|-moz-FieldText|-moz-html-CellHighlight|-moz-html-CellHighlightText|-moz-mac-accentdarkestshadow|-moz-mac-accentdarkshadow|-moz-mac-accentface|-moz-mac-accentlightesthighlight|-moz-mac-accentlightshadow|-moz-mac-accentregularhighlight|-moz-mac-accentregularshadow|-moz-mac-chrome-active|-moz-mac-chrome-inactive|-moz-mac-focusring|-moz-mac-menuselect|-moz-mac-menushadow|-moz-mac-menutextselect|-moz-MenuHover|-moz-MenuHoverText|-moz-MenuBarText|-moz-MenuBarHoverText|-moz-nativehyperlinktext|-moz-OddTreeRow|-moz-win-communicationstext|-moz-win-mediatext|-moz-activehyperlinktext|-moz-default-background-color|-moz-default-color|-moz-hyperlinktext|-moz-visitedhyperlinktext|-webkit-activelink|-webkit-focus-ring-color|-webkit-link|-webkit-text","-non-standard-image-rendering":"optimize-contrast|-moz-crisp-edges|-o-crisp-edges|-webkit-optimize-contrast","-non-standard-overflow":"-moz-scrollbars-none|-moz-scrollbars-horizontal|-moz-scrollbars-vertical|-moz-hidden-unscrollable","-non-standard-width":"fill-available|min-intrinsic|intrinsic|-moz-available|-moz-fit-content|-moz-min-content|-moz-max-content|-webkit-min-content|-webkit-max-content","-webkit-gradient()":"-webkit-gradient( <-webkit-gradient-type> , <-webkit-gradient-point> [, <-webkit-gradient-point>|, <-webkit-gradient-radius> , <-webkit-gradient-point>] [, <-webkit-gradient-radius>]? [, <-webkit-gradient-color-stop>]* )","-webkit-gradient-color-stop":"from( <color> )|color-stop( [<number-zero-one>|<percentage>] , <color> )|to( <color> )","-webkit-gradient-point":"[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]","-webkit-gradient-radius":"<length>|<percentage>","-webkit-gradient-type":"linear|radial","-webkit-mask-box-repeat":"repeat|stretch|round","-webkit-mask-clip-style":"border|border-box|padding|padding-box|content|content-box|text","-ms-filter-function-list":"<-ms-filter-function>+","-ms-filter-function":"<-ms-filter-function-progid>|<-ms-filter-function-legacy>","-ms-filter-function-progid":"'progid:' [<ident-token> '.']* [<ident-token>|<function-token> <any-value>? )]","-ms-filter-function-legacy":"<ident-token>|<function-token> <any-value>? )","-ms-filter":"<string>",age:"child|young|old","attr-name":"<wq-name>","attr-fallback":"<any-value>","border-radius":"<length-percentage>{1,2}",bottom:"<length>|auto","generic-voice":"[<age>? <gender> <integer>?]",gender:"male|female|neutral",left:"<length>|auto","mask-image":"<mask-reference>#","name-repeat":"repeat( [<positive-integer>|auto-fill] , <line-names>+ )",paint:"none|<color>|<url> [none|<color>]?|context-fill|context-stroke","page-size":"A5|A4|A3|B5|B4|JIS-B5|JIS-B4|letter|legal|ledger",ratio:"<integer> / <integer>",right:"<length>|auto","svg-length":"<percentage>|<length>|<number>","svg-writing-mode":"lr-tb|rl-tb|tb-rl|lr|rl|tb",top:"<length>|auto","track-group":"'(' [<string>* <track-minmax> <string>*]+ ')' ['[' <positive-integer> ']']?|<track-minmax>","track-list-v0":"[<string>* <track-group> <string>*]+|none","track-minmax":"minmax( <track-breadth> , <track-breadth> )|auto|<track-breadth>|fit-content",x:"<number>",y:"<number>",declaration:"<ident-token> : <declaration-value>? ['!' important]?","declaration-list":"[<declaration>? ';']* <declaration>?",url:"url( <string> <url-modifier>* )|<url-token>","url-modifier":"<ident>|<function-token> <any-value> )","number-zero-one":"<number [0,1]>","number-one-or-greater":"<number [1,∞]>","positive-integer":"<integer [0,∞]>","-non-standard-display":"-ms-inline-flexbox|-ms-grid|-ms-inline-grid|-webkit-flex|-webkit-inline-flex|-webkit-box|-webkit-inline-box|-moz-inline-stack|-moz-box|-moz-inline-box"},properties:{"--*":"<declaration-value>","-ms-accelerator":"false|true","-ms-block-progression":"tb|rl|bt|lr","-ms-content-zoom-chaining":"none|chained","-ms-content-zooming":"none|zoom","-ms-content-zoom-limit":"<'-ms-content-zoom-limit-min'> <'-ms-content-zoom-limit-max'>","-ms-content-zoom-limit-max":"<percentage>","-ms-content-zoom-limit-min":"<percentage>","-ms-content-zoom-snap":"<'-ms-content-zoom-snap-type'>||<'-ms-content-zoom-snap-points'>","-ms-content-zoom-snap-points":"snapInterval( <percentage> , <percentage> )|snapList( <percentage># )","-ms-content-zoom-snap-type":"none|proximity|mandatory","-ms-filter":"<string>","-ms-flow-from":"[none|<custom-ident>]#","-ms-flow-into":"[none|<custom-ident>]#","-ms-grid-columns":"none|<track-list>|<auto-track-list>","-ms-grid-rows":"none|<track-list>|<auto-track-list>","-ms-high-contrast-adjust":"auto|none","-ms-hyphenate-limit-chars":"auto|<integer>{1,3}","-ms-hyphenate-limit-lines":"no-limit|<integer>","-ms-hyphenate-limit-zone":"<percentage>|<length>","-ms-ime-align":"auto|after","-ms-overflow-style":"auto|none|scrollbar|-ms-autohiding-scrollbar","-ms-scrollbar-3dlight-color":"<color>","-ms-scrollbar-arrow-color":"<color>","-ms-scrollbar-base-color":"<color>","-ms-scrollbar-darkshadow-color":"<color>","-ms-scrollbar-face-color":"<color>","-ms-scrollbar-highlight-color":"<color>","-ms-scrollbar-shadow-color":"<color>","-ms-scrollbar-track-color":"<color>","-ms-scroll-chaining":"chained|none","-ms-scroll-limit":"<'-ms-scroll-limit-x-min'> <'-ms-scroll-limit-y-min'> <'-ms-scroll-limit-x-max'> <'-ms-scroll-limit-y-max'>","-ms-scroll-limit-x-max":"auto|<length>","-ms-scroll-limit-x-min":"<length>","-ms-scroll-limit-y-max":"auto|<length>","-ms-scroll-limit-y-min":"<length>","-ms-scroll-rails":"none|railed","-ms-scroll-snap-points-x":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-points-y":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-type":"none|proximity|mandatory","-ms-scroll-snap-x":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-x'>","-ms-scroll-snap-y":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-y'>","-ms-scroll-translation":"none|vertical-to-horizontal","-ms-text-autospace":"none|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space","-ms-touch-select":"grippers|none","-ms-user-select":"none|element|text","-ms-wrap-flow":"auto|both|start|end|maximum|clear","-ms-wrap-margin":"<length>","-ms-wrap-through":"wrap|none","-moz-appearance":"none|button|button-arrow-down|button-arrow-next|button-arrow-previous|button-arrow-up|button-bevel|button-focus|caret|checkbox|checkbox-container|checkbox-label|checkmenuitem|dualbutton|groupbox|listbox|listitem|menuarrow|menubar|menucheckbox|menuimage|menuitem|menuitemtext|menulist|menulist-button|menulist-text|menulist-textfield|menupopup|menuradio|menuseparator|meterbar|meterchunk|progressbar|progressbar-vertical|progresschunk|progresschunk-vertical|radio|radio-container|radio-label|radiomenuitem|range|range-thumb|resizer|resizerpanel|scale-horizontal|scalethumbend|scalethumb-horizontal|scalethumbstart|scalethumbtick|scalethumb-vertical|scale-vertical|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|separator|sheet|spinner|spinner-downbutton|spinner-textfield|spinner-upbutton|splitter|statusbar|statusbarpanel|tab|tabpanel|tabpanels|tab-scroll-arrow-back|tab-scroll-arrow-forward|textfield|textfield-multiline|toolbar|toolbarbutton|toolbarbutton-dropdown|toolbargripper|toolbox|tooltip|treeheader|treeheadercell|treeheadersortarrow|treeitem|treeline|treetwisty|treetwistyopen|treeview|-moz-mac-unified-toolbar|-moz-win-borderless-glass|-moz-win-browsertabbar-toolbox|-moz-win-communicationstext|-moz-win-communications-toolbox|-moz-win-exclude-glass|-moz-win-glass|-moz-win-mediatext|-moz-win-media-toolbox|-moz-window-button-box|-moz-window-button-box-maximized|-moz-window-button-close|-moz-window-button-maximize|-moz-window-button-minimize|-moz-window-button-restore|-moz-window-frame-bottom|-moz-window-frame-left|-moz-window-frame-right|-moz-window-titlebar|-moz-window-titlebar-maximized","-moz-binding":"<url>|none","-moz-border-bottom-colors":"<color>+|none","-moz-border-left-colors":"<color>+|none","-moz-border-right-colors":"<color>+|none","-moz-border-top-colors":"<color>+|none","-moz-context-properties":"none|[fill|fill-opacity|stroke|stroke-opacity]#","-moz-float-edge":"border-box|content-box|margin-box|padding-box","-moz-force-broken-image-icon":"<integer [0,1]>","-moz-image-region":"<shape>|auto","-moz-orient":"inline|block|horizontal|vertical","-moz-outline-radius":"<outline-radius>{1,4} [/ <outline-radius>{1,4}]?","-moz-outline-radius-bottomleft":"<outline-radius>","-moz-outline-radius-bottomright":"<outline-radius>","-moz-outline-radius-topleft":"<outline-radius>","-moz-outline-radius-topright":"<outline-radius>","-moz-stack-sizing":"ignore|stretch-to-fit","-moz-text-blink":"none|blink","-moz-user-focus":"ignore|normal|select-after|select-before|select-menu|select-same|select-all|none","-moz-user-input":"auto|none|enabled|disabled","-moz-user-modify":"read-only|read-write|write-only","-moz-window-dragging":"drag|no-drag","-moz-window-shadow":"default|menu|tooltip|sheet|none","-webkit-appearance":"none|button|button-bevel|caps-lock-indicator|caret|checkbox|default-button|inner-spin-button|listbox|listitem|media-controls-background|media-controls-fullscreen-background|media-current-time-display|media-enter-fullscreen-button|media-exit-fullscreen-button|media-fullscreen-button|media-mute-button|media-overlay-play-button|media-play-button|media-seek-back-button|media-seek-forward-button|media-slider|media-sliderthumb|media-time-remaining-display|media-toggle-closed-captions-button|media-volume-slider|media-volume-slider-container|media-volume-sliderthumb|menulist|menulist-button|menulist-text|menulist-textfield|meter|progress-bar|progress-bar-value|push-button|radio|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbargripper-horizontal|scrollbargripper-vertical|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|searchfield-cancel-button|searchfield-decoration|searchfield-results-button|searchfield-results-decoration|slider-horizontal|slider-vertical|sliderthumb-horizontal|sliderthumb-vertical|square-button|textarea|textfield|-apple-pay-button","-webkit-border-before":"<'border-width'>||<'border-style'>||<'color'>","-webkit-border-before-color":"<'color'>","-webkit-border-before-style":"<'border-style'>","-webkit-border-before-width":"<'border-width'>","-webkit-box-reflect":"[above|below|right|left]? <length>? <image>?","-webkit-line-clamp":"none|<integer>","-webkit-mask":"[<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||[<box>|border|padding|content|text]||[<box>|border|padding|content]]#","-webkit-mask-attachment":"<attachment>#","-webkit-mask-clip":"[<box>|border|padding|content|text]#","-webkit-mask-composite":"<composite-style>#","-webkit-mask-image":"<mask-reference>#","-webkit-mask-origin":"[<box>|border|padding|content]#","-webkit-mask-position":"<position>#","-webkit-mask-position-x":"[<length-percentage>|left|center|right]#","-webkit-mask-position-y":"[<length-percentage>|top|center|bottom]#","-webkit-mask-repeat":"<repeat-style>#","-webkit-mask-repeat-x":"repeat|no-repeat|space|round","-webkit-mask-repeat-y":"repeat|no-repeat|space|round","-webkit-mask-size":"<bg-size>#","-webkit-overflow-scrolling":"auto|touch","-webkit-tap-highlight-color":"<color>","-webkit-text-fill-color":"<color>","-webkit-text-stroke":"<length>||<color>","-webkit-text-stroke-color":"<color>","-webkit-text-stroke-width":"<length>","-webkit-touch-callout":"default|none","-webkit-user-modify":"read-only|read-write|read-write-plaintext-only","align-content":"normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>","align-items":"normal|stretch|<baseline-position>|[<overflow-position>? <self-position>]","align-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? <self-position>","align-tracks":"[normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>]#",all:"initial|inherit|unset|revert",animation:"<single-animation>#","animation-delay":"<time>#","animation-direction":"<single-animation-direction>#","animation-duration":"<time>#","animation-fill-mode":"<single-animation-fill-mode>#","animation-iteration-count":"<single-animation-iteration-count>#","animation-name":"[none|<keyframes-name>]#","animation-play-state":"<single-animation-play-state>#","animation-timing-function":"<timing-function>#",appearance:"none|auto|textfield|menulist-button|<compat-auto>","aspect-ratio":"auto|<ratio>",azimuth:"<angle>|[[left-side|far-left|left|center-left|center|center-right|right|far-right|right-side]||behind]|leftwards|rightwards","backdrop-filter":"none|<filter-function-list>","backface-visibility":"visible|hidden",background:"[<bg-layer> ,]* <final-bg-layer>","background-attachment":"<attachment>#","background-blend-mode":"<blend-mode>#","background-clip":"<box>#","background-color":"<color>","background-image":"<bg-image>#","background-origin":"<box>#","background-position":"<bg-position>#","background-position-x":"[center|[[left|right|x-start|x-end]? <length-percentage>?]!]#","background-position-y":"[center|[[top|bottom|y-start|y-end]? <length-percentage>?]!]#","background-repeat":"<repeat-style>#","background-size":"<bg-size>#","block-overflow":"clip|ellipsis|<string>","block-size":"<'width'>",border:"<line-width>||<line-style>||<color>","border-block":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-block-color":"<'border-top-color'>{1,2}","border-block-style":"<'border-top-style'>","border-block-width":"<'border-top-width'>","border-block-end":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-block-end-color":"<'border-top-color'>","border-block-end-style":"<'border-top-style'>","border-block-end-width":"<'border-top-width'>","border-block-start":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-block-start-color":"<'border-top-color'>","border-block-start-style":"<'border-top-style'>","border-block-start-width":"<'border-top-width'>","border-bottom":"<line-width>||<line-style>||<color>","border-bottom-color":"<'border-top-color'>","border-bottom-left-radius":"<length-percentage>{1,2}","border-bottom-right-radius":"<length-percentage>{1,2}","border-bottom-style":"<line-style>","border-bottom-width":"<line-width>","border-collapse":"collapse|separate","border-color":"<color>{1,4}","border-end-end-radius":"<length-percentage>{1,2}","border-end-start-radius":"<length-percentage>{1,2}","border-image":"<'border-image-source'>||<'border-image-slice'> [/ <'border-image-width'>|/ <'border-image-width'>? / <'border-image-outset'>]?||<'border-image-repeat'>","border-image-outset":"[<length>|<number>]{1,4}","border-image-repeat":"[stretch|repeat|round|space]{1,2}","border-image-slice":"<number-percentage>{1,4}&&fill?","border-image-source":"none|<image>","border-image-width":"[<length-percentage>|<number>|auto]{1,4}","border-inline":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-inline-end":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-inline-color":"<'border-top-color'>{1,2}","border-inline-style":"<'border-top-style'>","border-inline-width":"<'border-top-width'>","border-inline-end-color":"<'border-top-color'>","border-inline-end-style":"<'border-top-style'>","border-inline-end-width":"<'border-top-width'>","border-inline-start":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-inline-start-color":"<'border-top-color'>","border-inline-start-style":"<'border-top-style'>","border-inline-start-width":"<'border-top-width'>","border-left":"<line-width>||<line-style>||<color>","border-left-color":"<color>","border-left-style":"<line-style>","border-left-width":"<line-width>","border-radius":"<length-percentage>{1,4} [/ <length-percentage>{1,4}]?","border-right":"<line-width>||<line-style>||<color>","border-right-color":"<color>","border-right-style":"<line-style>","border-right-width":"<line-width>","border-spacing":"<length> <length>?","border-start-end-radius":"<length-percentage>{1,2}","border-start-start-radius":"<length-percentage>{1,2}","border-style":"<line-style>{1,4}","border-top":"<line-width>||<line-style>||<color>","border-top-color":"<color>","border-top-left-radius":"<length-percentage>{1,2}","border-top-right-radius":"<length-percentage>{1,2}","border-top-style":"<line-style>","border-top-width":"<line-width>","border-width":"<line-width>{1,4}",bottom:"<length>|<percentage>|auto","box-align":"start|center|end|baseline|stretch","box-decoration-break":"slice|clone","box-direction":"normal|reverse|inherit","box-flex":"<number>","box-flex-group":"<integer>","box-lines":"single|multiple","box-ordinal-group":"<integer>","box-orient":"horizontal|vertical|inline-axis|block-axis|inherit","box-pack":"start|center|end|justify","box-shadow":"none|<shadow>#","box-sizing":"content-box|border-box","break-after":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-before":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-inside":"auto|avoid|avoid-page|avoid-column|avoid-region","caption-side":"top|bottom|block-start|block-end|inline-start|inline-end","caret-color":"auto|<color>",clear:"none|left|right|both|inline-start|inline-end",clip:"<shape>|auto","clip-path":"<clip-source>|[<basic-shape>||<geometry-box>]|none",color:"<color>","color-adjust":"economy|exact","column-count":"<integer>|auto","column-fill":"auto|balance|balance-all","column-gap":"normal|<length-percentage>","column-rule":"<'column-rule-width'>||<'column-rule-style'>||<'column-rule-color'>","column-rule-color":"<color>","column-rule-style":"<'border-style'>","column-rule-width":"<'border-width'>","column-span":"none|all","column-width":"<length>|auto",columns:"<'column-width'>||<'column-count'>",contain:"none|strict|content|[size||layout||style||paint]",content:"normal|none|[<content-replacement>|<content-list>] [/ <string>]?","counter-increment":"[<custom-ident> <integer>?]+|none","counter-reset":"[<custom-ident> <integer>?]+|none","counter-set":"[<custom-ident> <integer>?]+|none",cursor:"[[<url> [<x> <y>]? ,]* [auto|default|none|context-menu|help|pointer|progress|wait|cell|crosshair|text|vertical-text|alias|copy|move|no-drop|not-allowed|e-resize|n-resize|ne-resize|nw-resize|s-resize|se-resize|sw-resize|w-resize|ew-resize|ns-resize|nesw-resize|nwse-resize|col-resize|row-resize|all-scroll|zoom-in|zoom-out|grab|grabbing|hand|-webkit-grab|-webkit-grabbing|-webkit-zoom-in|-webkit-zoom-out|-moz-grab|-moz-grabbing|-moz-zoom-in|-moz-zoom-out]]",direction:"ltr|rtl",display:"[<display-outside>||<display-inside>]|<display-listitem>|<display-internal>|<display-box>|<display-legacy>|<-non-standard-display>","empty-cells":"show|hide",filter:"none|<filter-function-list>|<-ms-filter-function-list>",flex:"none|[<'flex-grow'> <'flex-shrink'>?||<'flex-basis'>]","flex-basis":"content|<'width'>","flex-direction":"row|row-reverse|column|column-reverse","flex-flow":"<'flex-direction'>||<'flex-wrap'>","flex-grow":"<number>","flex-shrink":"<number>","flex-wrap":"nowrap|wrap|wrap-reverse",float:"left|right|none|inline-start|inline-end",font:"[[<'font-style'>||<font-variant-css21>||<'font-weight'>||<'font-stretch'>]? <'font-size'> [/ <'line-height'>]? <'font-family'>]|caption|icon|menu|message-box|small-caption|status-bar","font-family":"[<family-name>|<generic-family>]#","font-feature-settings":"normal|<feature-tag-value>#","font-kerning":"auto|normal|none","font-language-override":"normal|<string>","font-optical-sizing":"auto|none","font-variation-settings":"normal|[<string> <number>]#","font-size":"<absolute-size>|<relative-size>|<length-percentage>","font-size-adjust":"none|<number>","font-smooth":"auto|never|always|<absolute-size>|<length>","font-stretch":"<font-stretch-absolute>","font-style":"normal|italic|oblique <angle>?","font-synthesis":"none|[weight||style]","font-variant":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>||stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )||[small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps]||<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero||<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-alternates":"normal|[stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )]","font-variant-caps":"normal|small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps","font-variant-east-asian":"normal|[<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-ligatures":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>]","font-variant-numeric":"normal|[<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero]","font-variant-position":"normal|sub|super","font-weight":"<font-weight-absolute>|bolder|lighter",gap:"<'row-gap'> <'column-gap'>?",grid:"<'grid-template'>|<'grid-template-rows'> / [auto-flow&&dense?] <'grid-auto-columns'>?|[auto-flow&&dense?] <'grid-auto-rows'>? / <'grid-template-columns'>","grid-area":"<grid-line> [/ <grid-line>]{0,3}","grid-auto-columns":"<track-size>+","grid-auto-flow":"[row|column]||dense","grid-auto-rows":"<track-size>+","grid-column":"<grid-line> [/ <grid-line>]?","grid-column-end":"<grid-line>","grid-column-gap":"<length-percentage>","grid-column-start":"<grid-line>","grid-gap":"<'grid-row-gap'> <'grid-column-gap'>?","grid-row":"<grid-line> [/ <grid-line>]?","grid-row-end":"<grid-line>","grid-row-gap":"<length-percentage>","grid-row-start":"<grid-line>","grid-template":"none|[<'grid-template-rows'> / <'grid-template-columns'>]|[<line-names>? <string> <track-size>? <line-names>?]+ [/ <explicit-track-list>]?","grid-template-areas":"none|<string>+","grid-template-columns":"none|<track-list>|<auto-track-list>|subgrid <line-name-list>?","grid-template-rows":"none|<track-list>|<auto-track-list>|subgrid <line-name-list>?","hanging-punctuation":"none|[first||[force-end|allow-end]||last]",height:"auto|<length>|<percentage>|min-content|max-content|fit-content( <length-percentage> )",hyphens:"none|manual|auto","image-orientation":"from-image|<angle>|[<angle>? flip]","image-rendering":"auto|crisp-edges|pixelated|optimizeSpeed|optimizeQuality|<-non-standard-image-rendering>","image-resolution":"[from-image||<resolution>]&&snap?","ime-mode":"auto|normal|active|inactive|disabled","initial-letter":"normal|[<number> <integer>?]","initial-letter-align":"[auto|alphabetic|hanging|ideographic]","inline-size":"<'width'>",inset:"<'top'>{1,4}","inset-block":"<'top'>{1,2}","inset-block-end":"<'top'>","inset-block-start":"<'top'>","inset-inline":"<'top'>{1,2}","inset-inline-end":"<'top'>","inset-inline-start":"<'top'>",isolation:"auto|isolate","justify-content":"normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]","justify-items":"normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]|legacy|legacy&&[left|right|center]","justify-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]","justify-tracks":"[normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]]#",left:"<length>|<percentage>|auto","letter-spacing":"normal|<length-percentage>","line-break":"auto|loose|normal|strict|anywhere","line-clamp":"none|<integer>","line-height":"normal|<number>|<length>|<percentage>","line-height-step":"<length>","list-style":"<'list-style-type'>||<'list-style-position'>||<'list-style-image'>","list-style-image":"<url>|none","list-style-position":"inside|outside","list-style-type":"<counter-style>|<string>|none",margin:"[<length>|<percentage>|auto]{1,4}","margin-block":"<'margin-left'>{1,2}","margin-block-end":"<'margin-left'>","margin-block-start":"<'margin-left'>","margin-bottom":"<length>|<percentage>|auto","margin-inline":"<'margin-left'>{1,2}","margin-inline-end":"<'margin-left'>","margin-inline-start":"<'margin-left'>","margin-left":"<length>|<percentage>|auto","margin-right":"<length>|<percentage>|auto","margin-top":"<length>|<percentage>|auto","margin-trim":"none|in-flow|all",mask:"<mask-layer>#","mask-border":"<'mask-border-source'>||<'mask-border-slice'> [/ <'mask-border-width'>? [/ <'mask-border-outset'>]?]?||<'mask-border-repeat'>||<'mask-border-mode'>","mask-border-mode":"luminance|alpha","mask-border-outset":"[<length>|<number>]{1,4}","mask-border-repeat":"[stretch|repeat|round|space]{1,2}","mask-border-slice":"<number-percentage>{1,4} fill?","mask-border-source":"none|<image>","mask-border-width":"[<length-percentage>|<number>|auto]{1,4}","mask-clip":"[<geometry-box>|no-clip]#","mask-composite":"<compositing-operator>#","mask-image":"<mask-reference>#","mask-mode":"<masking-mode>#","mask-origin":"<geometry-box>#","mask-position":"<position>#","mask-repeat":"<repeat-style>#","mask-size":"<bg-size>#","mask-type":"luminance|alpha","masonry-auto-flow":"[pack|next]||[definite-first|ordered]","math-style":"normal|compact","max-block-size":"<'max-width'>","max-height":"none|<length-percentage>|min-content|max-content|fit-content( <length-percentage> )","max-inline-size":"<'max-width'>","max-lines":"none|<integer>","max-width":"none|<length-percentage>|min-content|max-content|fit-content( <length-percentage> )|<-non-standard-width>","min-block-size":"<'min-width'>","min-height":"auto|<length>|<percentage>|min-content|max-content|fit-content( <length-percentage> )","min-inline-size":"<'min-width'>","min-width":"auto|<length-percentage>|min-content|max-content|fit-content( <length-percentage> )|<-non-standard-width>","mix-blend-mode":"<blend-mode>","object-fit":"fill|contain|cover|none|scale-down","object-position":"<position>",offset:"[<'offset-position'>? [<'offset-path'> [<'offset-distance'>||<'offset-rotate'>]?]?]! [/ <'offset-anchor'>]?","offset-anchor":"auto|<position>","offset-distance":"<length-percentage>","offset-path":"none|ray( [<angle>&&<size>&&contain?] )|<path()>|<url>|[<basic-shape>||<geometry-box>]","offset-position":"auto|<position>","offset-rotate":"[auto|reverse]||<angle>",opacity:"<alpha-value>",order:"<integer>",orphans:"<integer>",outline:"[<'outline-color'>||<'outline-style'>||<'outline-width'>]","outline-color":"<color>|invert","outline-offset":"<length>","outline-style":"auto|<'border-style'>","outline-width":"<line-width>",overflow:"[visible|hidden|clip|scroll|auto]{1,2}|<-non-standard-overflow>","overflow-anchor":"auto|none","overflow-block":"visible|hidden|clip|scroll|auto","overflow-clip-box":"padding-box|content-box","overflow-inline":"visible|hidden|clip|scroll|auto","overflow-wrap":"normal|break-word|anywhere","overflow-x":"visible|hidden|clip|scroll|auto","overflow-y":"visible|hidden|clip|scroll|auto","overscroll-behavior":"[contain|none|auto]{1,2}","overscroll-behavior-block":"contain|none|auto","overscroll-behavior-inline":"contain|none|auto","overscroll-behavior-x":"contain|none|auto","overscroll-behavior-y":"contain|none|auto",padding:"[<length>|<percentage>]{1,4}","padding-block":"<'padding-left'>{1,2}","padding-block-end":"<'padding-left'>","padding-block-start":"<'padding-left'>","padding-bottom":"<length>|<percentage>","padding-inline":"<'padding-left'>{1,2}","padding-inline-end":"<'padding-left'>","padding-inline-start":"<'padding-left'>","padding-left":"<length>|<percentage>","padding-right":"<length>|<percentage>","padding-top":"<length>|<percentage>","page-break-after":"auto|always|avoid|left|right|recto|verso","page-break-before":"auto|always|avoid|left|right|recto|verso","page-break-inside":"auto|avoid","paint-order":"normal|[fill||stroke||markers]",perspective:"none|<length>","perspective-origin":"<position>","place-content":"<'align-content'> <'justify-content'>?","place-items":"<'align-items'> <'justify-items'>?","place-self":"<'align-self'> <'justify-self'>?","pointer-events":"auto|none|visiblePainted|visibleFill|visibleStroke|visible|painted|fill|stroke|all|inherit",position:"static|relative|absolute|sticky|fixed|-webkit-sticky",quotes:"none|auto|[<string> <string>]+",resize:"none|both|horizontal|vertical|block|inline",right:"<length>|<percentage>|auto",rotate:"none|<angle>|[x|y|z|<number>{3}]&&<angle>","row-gap":"normal|<length-percentage>","ruby-align":"start|center|space-between|space-around","ruby-merge":"separate|collapse|auto","ruby-position":"over|under|inter-character",scale:"none|<number>{1,3}","scrollbar-color":"auto|dark|light|<color>{2}","scrollbar-gutter":"auto|[stable|always]&&both?&&force?","scrollbar-width":"auto|thin|none","scroll-behavior":"auto|smooth","scroll-margin":"<length>{1,4}","scroll-margin-block":"<length>{1,2}","scroll-margin-block-start":"<length>","scroll-margin-block-end":"<length>","scroll-margin-bottom":"<length>","scroll-margin-inline":"<length>{1,2}","scroll-margin-inline-start":"<length>","scroll-margin-inline-end":"<length>","scroll-margin-left":"<length>","scroll-margin-right":"<length>","scroll-margin-top":"<length>","scroll-padding":"[auto|<length-percentage>]{1,4}","scroll-padding-block":"[auto|<length-percentage>]{1,2}","scroll-padding-block-start":"auto|<length-percentage>","scroll-padding-block-end":"auto|<length-percentage>","scroll-padding-bottom":"auto|<length-percentage>","scroll-padding-inline":"[auto|<length-percentage>]{1,2}","scroll-padding-inline-start":"auto|<length-percentage>","scroll-padding-inline-end":"auto|<length-percentage>","scroll-padding-left":"auto|<length-percentage>","scroll-padding-right":"auto|<length-percentage>","scroll-padding-top":"auto|<length-percentage>","scroll-snap-align":"[none|start|end|center]{1,2}","scroll-snap-coordinate":"none|<position>#","scroll-snap-destination":"<position>","scroll-snap-points-x":"none|repeat( <length-percentage> )","scroll-snap-points-y":"none|repeat( <length-percentage> )","scroll-snap-stop":"normal|always","scroll-snap-type":"none|[x|y|block|inline|both] [mandatory|proximity]?","scroll-snap-type-x":"none|mandatory|proximity","scroll-snap-type-y":"none|mandatory|proximity","shape-image-threshold":"<alpha-value>","shape-margin":"<length-percentage>","shape-outside":"none|<shape-box>||<basic-shape>|<image>","tab-size":"<integer>|<length>","table-layout":"auto|fixed","text-align":"start|end|left|right|center|justify|match-parent","text-align-last":"auto|start|end|left|right|center|justify","text-combine-upright":"none|all|[digits <integer>?]","text-decoration":"<'text-decoration-line'>||<'text-decoration-style'>||<'text-decoration-color'>||<'text-decoration-thickness'>","text-decoration-color":"<color>","text-decoration-line":"none|[underline||overline||line-through||blink]|spelling-error|grammar-error","text-decoration-skip":"none|[objects||[spaces|[leading-spaces||trailing-spaces]]||edges||box-decoration]","text-decoration-skip-ink":"auto|all|none","text-decoration-style":"solid|double|dotted|dashed|wavy","text-decoration-thickness":"auto|from-font|<length>|<percentage>","text-emphasis":"<'text-emphasis-style'>||<'text-emphasis-color'>","text-emphasis-color":"<color>","text-emphasis-position":"[over|under]&&[right|left]","text-emphasis-style":"none|[[filled|open]||[dot|circle|double-circle|triangle|sesame]]|<string>","text-indent":"<length-percentage>&&hanging?&&each-line?","text-justify":"auto|inter-character|inter-word|none","text-orientation":"mixed|upright|sideways","text-overflow":"[clip|ellipsis|<string>]{1,2}","text-rendering":"auto|optimizeSpeed|optimizeLegibility|geometricPrecision","text-shadow":"none|<shadow-t>#","text-size-adjust":"none|auto|<percentage>","text-transform":"none|capitalize|uppercase|lowercase|full-width|full-size-kana","text-underline-offset":"auto|<length>|<percentage>","text-underline-position":"auto|from-font|[under||[left|right]]",top:"<length>|<percentage>|auto","touch-action":"auto|none|[[pan-x|pan-left|pan-right]||[pan-y|pan-up|pan-down]||pinch-zoom]|manipulation",transform:"none|<transform-list>","transform-box":"content-box|border-box|fill-box|stroke-box|view-box","transform-origin":"[<length-percentage>|left|center|right|top|bottom]|[[<length-percentage>|left|center|right]&&[<length-percentage>|top|center|bottom]] <length>?","transform-style":"flat|preserve-3d",transition:"<single-transition>#","transition-delay":"<time>#","transition-duration":"<time>#","transition-property":"none|<single-transition-property>#","transition-timing-function":"<timing-function>#",translate:"none|<length-percentage> [<length-percentage> <length>?]?","unicode-bidi":"normal|embed|isolate|bidi-override|isolate-override|plaintext|-moz-isolate|-moz-isolate-override|-moz-plaintext|-webkit-isolate|-webkit-isolate-override|-webkit-plaintext","user-select":"auto|text|none|contain|all","vertical-align":"baseline|sub|super|text-top|text-bottom|middle|top|bottom|<percentage>|<length>",visibility:"visible|hidden|collapse","white-space":"normal|pre|nowrap|pre-wrap|pre-line|break-spaces",widows:"<integer>",width:"auto|<length>|<percentage>|min-content|max-content|fit-content( <length-percentage> )","will-change":"auto|<animateable-feature>#","word-break":"normal|break-all|keep-all|break-word","word-spacing":"normal|<length-percentage>","word-wrap":"normal|break-word","writing-mode":"horizontal-tb|vertical-rl|vertical-lr|sideways-rl|sideways-lr|<svg-writing-mode>","z-index":"auto|<integer>",zoom:"normal|reset|<number>|<percentage>","-moz-background-clip":"padding|border","-moz-border-radius-bottomleft":"<'border-bottom-left-radius'>","-moz-border-radius-bottomright":"<'border-bottom-right-radius'>","-moz-border-radius-topleft":"<'border-top-left-radius'>","-moz-border-radius-topright":"<'border-bottom-right-radius'>","-moz-control-character-visibility":"visible|hidden","-moz-osx-font-smoothing":"auto|grayscale","-moz-user-select":"none|text|all|-moz-none","-ms-flex-align":"start|end|center|baseline|stretch","-ms-flex-item-align":"auto|start|end|center|baseline|stretch","-ms-flex-line-pack":"start|end|center|justify|distribute|stretch","-ms-flex-negative":"<'flex-shrink'>","-ms-flex-pack":"start|end|center|justify|distribute","-ms-flex-order":"<integer>","-ms-flex-positive":"<'flex-grow'>","-ms-flex-preferred-size":"<'flex-basis'>","-ms-interpolation-mode":"nearest-neighbor|bicubic","-ms-grid-column-align":"start|end|center|stretch","-ms-grid-row-align":"start|end|center|stretch","-ms-hyphenate-limit-last":"none|always|column|page|spread","-webkit-background-clip":"[<box>|border|padding|content|text]#","-webkit-column-break-after":"always|auto|avoid","-webkit-column-break-before":"always|auto|avoid","-webkit-column-break-inside":"always|auto|avoid","-webkit-font-smoothing":"auto|none|antialiased|subpixel-antialiased","-webkit-mask-box-image":"[<url>|<gradient>|none] [<length-percentage>{4} <-webkit-mask-box-repeat>{2}]?","-webkit-print-color-adjust":"economy|exact","-webkit-text-security":"none|circle|disc|square","-webkit-user-drag":"none|element|auto","-webkit-user-select":"auto|none|text|all","alignment-baseline":"auto|baseline|before-edge|text-before-edge|middle|central|after-edge|text-after-edge|ideographic|alphabetic|hanging|mathematical","baseline-shift":"baseline|sub|super|<svg-length>",behavior:"<url>+","clip-rule":"nonzero|evenodd",cue:"<'cue-before'> <'cue-after'>?","cue-after":"<url> <decibel>?|none","cue-before":"<url> <decibel>?|none","dominant-baseline":"auto|use-script|no-change|reset-size|ideographic|alphabetic|hanging|mathematical|central|middle|text-after-edge|text-before-edge",fill:"<paint>","fill-opacity":"<number-zero-one>","fill-rule":"nonzero|evenodd","glyph-orientation-horizontal":"<angle>","glyph-orientation-vertical":"<angle>",kerning:"auto|<svg-length>",marker:"none|<url>","marker-end":"none|<url>","marker-mid":"none|<url>","marker-start":"none|<url>",pause:"<'pause-before'> <'pause-after'>?","pause-after":"<time>|none|x-weak|weak|medium|strong|x-strong","pause-before":"<time>|none|x-weak|weak|medium|strong|x-strong",rest:"<'rest-before'> <'rest-after'>?","rest-after":"<time>|none|x-weak|weak|medium|strong|x-strong","rest-before":"<time>|none|x-weak|weak|medium|strong|x-strong","shape-rendering":"auto|optimizeSpeed|crispEdges|geometricPrecision",src:"[<url> [format( <string># )]?|local( <family-name> )]#",speak:"auto|none|normal","speak-as":"normal|spell-out||digits||[literal-punctuation|no-punctuation]",stroke:"<paint>","stroke-dasharray":"none|[<svg-length>+]#","stroke-dashoffset":"<svg-length>","stroke-linecap":"butt|round|square","stroke-linejoin":"miter|round|bevel","stroke-miterlimit":"<number-one-or-greater>","stroke-opacity":"<number-zero-one>","stroke-width":"<svg-length>","text-anchor":"start|middle|end","unicode-range":"<urange>#","voice-balance":"<number>|left|center|right|leftwards|rightwards","voice-duration":"auto|<time>","voice-family":"[[<family-name>|<generic-voice>] ,]* [<family-name>|<generic-voice>]|preserve","voice-pitch":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-range":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-rate":"[normal|x-slow|slow|medium|fast|x-fast]||<percentage>","voice-stress":"normal|strong|moderate|none|reduced","voice-volume":"silent|[[x-soft|soft|medium|loud|x-loud]||<decibel>]"},atrules:{charset:{prelude:"<string>",descriptors:null},"counter-style":{prelude:"<counter-style-name>",descriptors:{"additive-symbols":"[<integer>&&<symbol>]#",fallback:"<counter-style-name>",negative:"<symbol> <symbol>?",pad:"<integer>&&<symbol>",prefix:"<symbol>",range:"[[<integer>|infinite]{2}]#|auto","speak-as":"auto|bullets|numbers|words|spell-out|<counter-style-name>",suffix:"<symbol>",symbols:"<symbol>+",system:"cyclic|numeric|alphabetic|symbolic|additive|[fixed <integer>?]|[extends <counter-style-name>]"}},document:{prelude:"[<url>|url-prefix( <string> )|domain( <string> )|media-document( <string> )|regexp( <string> )]#",descriptors:null},"font-face":{prelude:null,descriptors:{"font-display":"[auto|block|swap|fallback|optional]","font-family":"<family-name>","font-feature-settings":"normal|<feature-tag-value>#","font-variation-settings":"normal|[<string> <number>]#","font-stretch":"<font-stretch-absolute>{1,2}","font-style":"normal|italic|oblique <angle>{0,2}","font-weight":"<font-weight-absolute>{1,2}","font-variant":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>||stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )||[small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps]||<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero||<east-asian-variant-values>||<east-asian-width-values>||ruby]",src:"[<url> [format( <string># )]?|local( <family-name> )]#","unicode-range":"<urange>#"}},"font-feature-values":{prelude:"<family-name>#",descriptors:null},import:{prelude:"[<string>|<url>] [<media-query-list>]?",descriptors:null},keyframes:{prelude:"<keyframes-name>",descriptors:null},media:{prelude:"<media-query-list>",descriptors:null},namespace:{prelude:"<namespace-prefix>? [<string>|<url>]",descriptors:null},page:{prelude:"<page-selector-list>",descriptors:{bleed:"auto|<length>",marks:"none|[crop||cross]",size:"<length>{1,2}|auto|[<page-size>||[portrait|landscape]]"}},property:{prelude:"<custom-property-name>",descriptors:{syntax:"<string>",inherits:"true|false","initial-value":"<string>"}},supports:{prelude:"<supports-condition>",descriptors:null},viewport:{prelude:null,descriptors:{height:"<viewport-length>{1,2}","max-height":"<viewport-length>","max-width":"<viewport-length>","max-zoom":"auto|<number>|<percentage>","min-height":"<viewport-length>","min-width":"<viewport-length>","min-zoom":"auto|<number>|<percentage>",orientation:"auto|portrait|landscape","user-zoom":"zoom|fixed","viewport-fit":"auto|contain|cover",width:"<viewport-length>{1,2}",zoom:"auto|<number>|<percentage>"}}}},xr=we.cmpChar,Sr=we.isDigit,Cr=we.TYPE,Ar=Cr.WhiteSpace,zr=Cr.Comment,Pr=Cr.Ident,Lr=Cr.Number,Tr=Cr.Dimension;function Er(e,t){var n=this.scanner.tokenStart+e,r=this.scanner.source.charCodeAt(n);for(43!==r&&45!==r||(t&&this.error("Number sign is not allowed"),n++);n<this.scanner.tokenEnd;n++)Sr(this.scanner.source.charCodeAt(n))||this.error("Integer is expected",n)}function Or(e){return Er.call(this,0,e)}function Dr(e,t){if(!xr(this.scanner.source,this.scanner.tokenStart+e,t)){var n="";switch(t){case 110:n="N is expected";break;case 45:n="HyphenMinus is expected"}this.error(n,this.scanner.tokenStart+e)}}function Ir(){for(var e=0,t=0,n=this.scanner.tokenType;n===Ar||n===zr;)n=this.scanner.lookupType(++e);if(n!==Lr){if(!this.scanner.isDelim(43,e)&&!this.scanner.isDelim(45,e))return null;t=this.scanner.isDelim(43,e)?43:45;do{n=this.scanner.lookupType(++e)}while(n===Ar||n===zr);n!==Lr&&(this.scanner.skip(e),Or.call(this,!0))}return e>0&&this.scanner.skip(e),0===t&&43!==(n=this.scanner.source.charCodeAt(this.scanner.tokenStart))&&45!==n&&this.error("Number sign is expected"),Or.call(this,0!==t),45===t?"-"+this.consume(Lr):this.consume(Lr)}var Rr={name:"AnPlusB",structure:{a:[String,null],b:[String,null]},parse:function(){var e=this.scanner.tokenStart,t=null,n=null;if(this.scanner.tokenType===Lr)Or.call(this,!1),n=this.consume(Lr);else if(this.scanner.tokenType===Pr&&xr(this.scanner.source,this.scanner.tokenStart,45))switch(t="-1",Dr.call(this,1,110),this.scanner.getTokenLength()){case 2:this.scanner.next(),n=Ir.call(this);break;case 3:Dr.call(this,2,45),this.scanner.next(),this.scanner.skipSC(),Or.call(this,!0),n="-"+this.consume(Lr);break;default:Dr.call(this,2,45),Er.call(this,3,!0),this.scanner.next(),n=this.scanner.substrToCursor(e+2)}else if(this.scanner.tokenType===Pr||this.scanner.isDelim(43)&&this.scanner.lookupType(1)===Pr){var r=0;switch(t="1",this.scanner.isDelim(43)&&(r=1,this.scanner.next()),Dr.call(this,0,110),this.scanner.getTokenLength()){case 1:this.scanner.next(),n=Ir.call(this);break;case 2:Dr.call(this,1,45),this.scanner.next(),this.scanner.skipSC(),Or.call(this,!0),n="-"+this.consume(Lr);break;default:Dr.call(this,1,45),Er.call(this,2,!0),this.scanner.next(),n=this.scanner.substrToCursor(e+r+1)}}else if(this.scanner.tokenType===Tr){for(var a=this.scanner.source.charCodeAt(this.scanner.tokenStart),i=(r=43===a||45===a,this.scanner.tokenStart+r);i<this.scanner.tokenEnd&&Sr(this.scanner.source.charCodeAt(i));i++);i===this.scanner.tokenStart+r&&this.error("Integer is expected",this.scanner.tokenStart+r),Dr.call(this,i-this.scanner.tokenStart,110),t=this.scanner.source.substring(e,i),i+1===this.scanner.tokenEnd?(this.scanner.next(),n=Ir.call(this)):(Dr.call(this,i-this.scanner.tokenStart+1,45),i+2===this.scanner.tokenEnd?(this.scanner.next(),this.scanner.skipSC(),Or.call(this,!0),n="-"+this.consume(Lr)):(Er.call(this,i-this.scanner.tokenStart+2,!0),this.scanner.next(),n=this.scanner.substrToCursor(i+1)))}else this.error();return null!==t&&43===t.charCodeAt(0)&&(t=t.substr(1)),null!==n&&43===n.charCodeAt(0)&&(n=n.substr(1)),{type:"AnPlusB",loc:this.getLocation(e,this.scanner.tokenStart),a:t,b:n}},generate:function(e){var t=null!==e.a&&void 0!==e.a,n=null!==e.b&&void 0!==e.b;t?(this.chunk("+1"===e.a?"+n":"1"===e.a?"n":"-1"===e.a?"-n":e.a+"n"),n&&("-"===(n=String(e.b)).charAt(0)||"+"===n.charAt(0)?(this.chunk(n.charAt(0)),this.chunk(n.substr(1))):(this.chunk("+"),this.chunk(n)))):this.chunk(String(e.b))}},Nr=we.TYPE,Br=Nr.WhiteSpace,Mr=Nr.Semicolon,jr=Nr.LeftCurlyBracket,_r=Nr.Delim;function qr(){return this.scanner.tokenIndex>0&&this.scanner.lookupType(-1)===Br?this.scanner.tokenIndex>1?this.scanner.getTokenStart(this.scanner.tokenIndex-1):this.scanner.firstCharOffset:this.scanner.tokenStart}function Wr(){return 0}var Fr={name:"Raw",structure:{value:String},parse:function(e,t,n){var r,a=this.scanner.getTokenStart(e);return this.scanner.skip(this.scanner.getRawLength(e,t||Wr)),r=n&&this.scanner.tokenStart>a?qr.call(this):this.scanner.tokenStart,{type:"Raw",loc:this.getLocation(a,r),value:this.scanner.source.substring(a,r)}},generate:function(e){this.chunk(e.value)},mode:{default:Wr,leftCurlyBracket:function(e){return e===jr?1:0},leftCurlyBracketOrSemicolon:function(e){return e===jr||e===Mr?1:0},exclamationMarkOrSemicolon:function(e,t,n){return e===_r&&33===t.charCodeAt(n)||e===Mr?1:0},semicolonIncluded:function(e){return e===Mr?2:0}}},Ur=we.TYPE,Yr=Fr.mode,Hr=Ur.AtKeyword,Vr=Ur.Semicolon,Kr=Ur.LeftCurlyBracket,Gr=Ur.RightCurlyBracket;function $r(e){return this.Raw(e,Yr.leftCurlyBracketOrSemicolon,!0)}function Qr(){for(var e,t=1;e=this.scanner.lookupType(t);t++){if(e===Gr)return!0;if(e===Kr||e===Hr)return!1}return!1}var Xr={name:"Atrule",structure:{name:String,prelude:["AtrulePrelude","Raw",null],block:["Block",null]},parse:function(){var e,t,n=this.scanner.tokenStart,r=null,a=null;switch(this.eat(Hr),t=(e=this.scanner.substrToCursor(n+1)).toLowerCase(),this.scanner.skipSC(),!1===this.scanner.eof&&this.scanner.tokenType!==Kr&&this.scanner.tokenType!==Vr&&(this.parseAtrulePrelude?"AtrulePrelude"===(r=this.parseWithFallback(this.AtrulePrelude.bind(this,e),$r)).type&&null===r.children.head&&(r=null):r=$r.call(this,this.scanner.tokenIndex),this.scanner.skipSC()),this.scanner.tokenType){case Vr:this.scanner.next();break;case Kr:a=this.atrule.hasOwnProperty(t)&&"function"==typeof this.atrule[t].block?this.atrule[t].block.call(this):this.Block(Qr.call(this))}return{type:"Atrule",loc:this.getLocation(n,this.scanner.tokenStart),name:e,prelude:r,block:a}},generate:function(e){this.chunk("@"),this.chunk(e.name),null!==e.prelude&&(this.chunk(" "),this.node(e.prelude)),e.block?this.node(e.block):this.chunk(";")},walkContext:"atrule"},Zr=we.TYPE,Jr=Zr.Semicolon,ea=Zr.LeftCurlyBracket,ta={name:"AtrulePrelude",structure:{children:[[]]},parse:function(e){var t=null;return null!==e&&(e=e.toLowerCase()),this.scanner.skipSC(),t=this.atrule.hasOwnProperty(e)&&"function"==typeof this.atrule[e].prelude?this.atrule[e].prelude.call(this):this.readSequence(this.scope.AtrulePrelude),this.scanner.skipSC(),!0!==this.scanner.eof&&this.scanner.tokenType!==ea&&this.scanner.tokenType!==Jr&&this.error("Semicolon or block is expected"),null===t&&(t=this.createList()),{type:"AtrulePrelude",loc:this.getLocationFromList(t),children:t}},generate:function(e){this.children(e)},walkContext:"atrulePrelude"},na=we.TYPE,ra=na.Ident,aa=na.String,ia=na.Colon,oa=na.LeftSquareBracket,sa=na.RightSquareBracket;function la(){this.scanner.eof&&this.error("Unexpected end of input");var e=this.scanner.tokenStart,t=!1,n=!0;return this.scanner.isDelim(42)?(t=!0,n=!1,this.scanner.next()):this.scanner.isDelim(124)||this.eat(ra),this.scanner.isDelim(124)?61!==this.scanner.source.charCodeAt(this.scanner.tokenStart+1)?(this.scanner.next(),this.eat(ra)):t&&this.error("Identifier is expected",this.scanner.tokenEnd):t&&this.error("Vertical line is expected"),n&&this.scanner.tokenType===ia&&(this.scanner.next(),this.eat(ra)),{type:"Identifier",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}}function ca(){var e=this.scanner.tokenStart,t=this.scanner.source.charCodeAt(e);return 61!==t&&126!==t&&94!==t&&36!==t&&42!==t&&124!==t&&this.error("Attribute selector (=, ~=, ^=, $=, *=, |=) is expected"),this.scanner.next(),61!==t&&(this.scanner.isDelim(61)||this.error("Equal sign is expected"),this.scanner.next()),this.scanner.substrToCursor(e)}var ua={name:"AttributeSelector",structure:{name:"Identifier",matcher:[String,null],value:["String","Identifier",null],flags:[String,null]},parse:function(){var e,t=this.scanner.tokenStart,n=null,r=null,a=null;return this.eat(oa),this.scanner.skipSC(),e=la.call(this),this.scanner.skipSC(),this.scanner.tokenType!==sa&&(this.scanner.tokenType!==ra&&(n=ca.call(this),this.scanner.skipSC(),r=this.scanner.tokenType===aa?this.String():this.Identifier(),this.scanner.skipSC()),this.scanner.tokenType===ra&&(a=this.scanner.getTokenValue(),this.scanner.next(),this.scanner.skipSC())),this.eat(sa),{type:"AttributeSelector",loc:this.getLocation(t,this.scanner.tokenStart),name:e,matcher:n,value:r,flags:a}},generate:function(e){var t=" ";this.chunk("["),this.node(e.name),null!==e.matcher&&(this.chunk(e.matcher),null!==e.value&&(this.node(e.value),"String"===e.value.type&&(t=""))),null!==e.flags&&(this.chunk(t),this.chunk(e.flags)),this.chunk("]")}},ha=we.TYPE,da=Fr.mode,pa=ha.WhiteSpace,ma=ha.Comment,fa=ha.Semicolon,ga=ha.AtKeyword,ba=ha.LeftCurlyBracket,ya=ha.RightCurlyBracket;function va(e){return this.Raw(e,null,!0)}function ka(){return this.parseWithFallback(this.Rule,va)}function wa(e){return this.Raw(e,da.semicolonIncluded,!0)}function xa(){if(this.scanner.tokenType===fa)return wa.call(this,this.scanner.tokenIndex);var e=this.parseWithFallback(this.Declaration,wa);return this.scanner.tokenType===fa&&this.scanner.next(),e}var Sa={name:"Block",structure:{children:[["Atrule","Rule","Declaration"]]},parse:function(e){var t=e?xa:ka,n=this.scanner.tokenStart,r=this.createList();this.eat(ba);e:for(;!this.scanner.eof;)switch(this.scanner.tokenType){case ya:break e;case pa:case ma:this.scanner.next();break;case ga:r.push(this.parseWithFallback(this.Atrule,va));break;default:r.push(t.call(this))}return this.scanner.eof||this.eat(ya),{type:"Block",loc:this.getLocation(n,this.scanner.tokenStart),children:r}},generate:function(e){this.chunk("{"),this.children(e,(function(e){"Declaration"===e.type&&this.chunk(";")})),this.chunk("}")},walkContext:"block"},Ca=we.TYPE,Aa=Ca.LeftSquareBracket,za=Ca.RightSquareBracket,Pa={name:"Brackets",structure:{children:[[]]},parse:function(e,t){var n,r=this.scanner.tokenStart;return this.eat(Aa),n=e.call(this,t),this.scanner.eof||this.eat(za),{type:"Brackets",loc:this.getLocation(r,this.scanner.tokenStart),children:n}},generate:function(e){this.chunk("["),this.children(e),this.chunk("]")}},La=we.TYPE.CDC,Ta={name:"CDC",structure:[],parse:function(){var e=this.scanner.tokenStart;return this.eat(La),{type:"CDC",loc:this.getLocation(e,this.scanner.tokenStart)}},generate:function(){this.chunk("--\x3e")}},Ea=we.TYPE.CDO,Oa={name:"CDO",structure:[],parse:function(){var e=this.scanner.tokenStart;return this.eat(Ea),{type:"CDO",loc:this.getLocation(e,this.scanner.tokenStart)}},generate:function(){this.chunk("\x3c!--")}},Da=we.TYPE.Ident,Ia={name:"ClassSelector",structure:{name:String},parse:function(){return this.scanner.isDelim(46)||this.error("Full stop is expected"),this.scanner.next(),{type:"ClassSelector",loc:this.getLocation(this.scanner.tokenStart-1,this.scanner.tokenEnd),name:this.consume(Da)}},generate:function(e){this.chunk("."),this.chunk(e.name)}},Ra=we.TYPE.Ident,Na={name:"Combinator",structure:{name:String},parse:function(){var e=this.scanner.tokenStart;switch(this.scanner.source.charCodeAt(this.scanner.tokenStart)){case 62:case 43:case 126:this.scanner.next();break;case 47:this.scanner.next(),this.scanner.tokenType===Ra&&!1!==this.scanner.lookupValue(0,"deep")||this.error("Identifier `deep` is expected"),this.scanner.next(),this.scanner.isDelim(47)||this.error("Solidus is expected"),this.scanner.next();break;default:this.error("Combinator is expected")}return{type:"Combinator",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.name)}},Ba=we.TYPE.Comment,Ma={name:"Comment",structure:{value:String},parse:function(){var e=this.scanner.tokenStart,t=this.scanner.tokenEnd;return this.eat(Ba),t-e+2>=2&&42===this.scanner.source.charCodeAt(t-2)&&47===this.scanner.source.charCodeAt(t-1)&&(t-=2),{type:"Comment",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.source.substring(e+2,t)}},generate:function(e){this.chunk("/*"),this.chunk(e.value),this.chunk("*/")}},ja=ne.isCustomProperty,_a=we.TYPE,qa=Fr.mode,Wa=_a.Ident,Fa=_a.Hash,Ua=_a.Colon,Ya=_a.Semicolon,Ha=_a.Delim,Va=_a.WhiteSpace;function Ka(e){return this.Raw(e,qa.exclamationMarkOrSemicolon,!0)}function Ga(e){return this.Raw(e,qa.exclamationMarkOrSemicolon,!1)}function $a(){var e=this.scanner.tokenIndex,t=this.Value();return"Raw"!==t.type&&!1===this.scanner.eof&&this.scanner.tokenType!==Ya&&!1===this.scanner.isDelim(33)&&!1===this.scanner.isBalanceEdge(e)&&this.error(),t}var Qa={name:"Declaration",structure:{important:[Boolean,String],property:String,value:["Value","Raw"]},parse:function(){var e,t=this.scanner.tokenStart,n=this.scanner.tokenIndex,r=Xa.call(this),a=ja(r),i=a?this.parseCustomProperty:this.parseValue,o=a?Ga:Ka,s=!1;this.scanner.skipSC(),this.eat(Ua);const l=this.scanner.tokenIndex;if(a||this.scanner.skipSC(),e=i?this.parseWithFallback($a,o):o.call(this,this.scanner.tokenIndex),a&&"Value"===e.type&&e.children.isEmpty())for(let t=l-this.scanner.tokenIndex;t<=0;t++)if(this.scanner.lookupType(t)===Va){e.children.appendData({type:"WhiteSpace",loc:null,value:" "});break}return this.scanner.isDelim(33)&&(s=Za.call(this),this.scanner.skipSC()),!1===this.scanner.eof&&this.scanner.tokenType!==Ya&&!1===this.scanner.isBalanceEdge(n)&&this.error(),{type:"Declaration",loc:this.getLocation(t,this.scanner.tokenStart),important:s,property:r,value:e}},generate:function(e){this.chunk(e.property),this.chunk(":"),this.node(e.value),e.important&&this.chunk(!0===e.important?"!important":"!"+e.important)},walkContext:"declaration"};function Xa(){var e=this.scanner.tokenStart;if(this.scanner.tokenType===Ha)switch(this.scanner.source.charCodeAt(this.scanner.tokenStart)){case 42:case 36:case 43:case 35:case 38:this.scanner.next();break;case 47:this.scanner.next(),this.scanner.isDelim(47)&&this.scanner.next()}return this.scanner.tokenType===Fa?this.eat(Fa):this.eat(Wa),this.scanner.substrToCursor(e)}function Za(){this.eat(Ha),this.scanner.skipSC();var e=this.consume(Wa);return"important"===e||e}var Ja=we.TYPE,ei=Fr.mode,ti=Ja.WhiteSpace,ni=Ja.Comment,ri=Ja.Semicolon;function ai(e){return this.Raw(e,ei.semicolonIncluded,!0)}var ii={name:"DeclarationList",structure:{children:[["Declaration"]]},parse:function(){for(var e=this.createList();!this.scanner.eof;)switch(this.scanner.tokenType){case ti:case ni:case ri:this.scanner.next();break;default:e.push(this.parseWithFallback(this.Declaration,ai))}return{type:"DeclarationList",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e,(function(e){"Declaration"===e.type&&this.chunk(";")}))}},oi=M.consumeNumber,si=we.TYPE.Dimension,li={name:"Dimension",structure:{value:String,unit:String},parse:function(){var e=this.scanner.tokenStart,t=oi(this.scanner.source,e);return this.eat(si),{type:"Dimension",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.source.substring(e,t),unit:this.scanner.source.substring(t,this.scanner.tokenStart)}},generate:function(e){this.chunk(e.value),this.chunk(e.unit)}},ci=we.TYPE.RightParenthesis,ui={name:"Function",structure:{name:String,children:[[]]},parse:function(e,t){var n,r=this.scanner.tokenStart,a=this.consumeFunctionName(),i=a.toLowerCase();return n=t.hasOwnProperty(i)?t[i].call(this,t):e.call(this,t),this.scanner.eof||this.eat(ci),{type:"Function",loc:this.getLocation(r,this.scanner.tokenStart),name:a,children:n}},generate:function(e){this.chunk(e.name),this.chunk("("),this.children(e),this.chunk(")")},walkContext:"function"},hi=we.TYPE.Hash,di={name:"Hash",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;return this.eat(hi),{type:"Hash",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e+1)}},generate:function(e){this.chunk("#"),this.chunk(e.value)}},pi=we.TYPE.Ident,mi={name:"Identifier",structure:{name:String},parse:function(){return{type:"Identifier",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),name:this.consume(pi)}},generate:function(e){this.chunk(e.name)}},fi=we.TYPE.Hash,gi={name:"IdSelector",structure:{name:String},parse:function(){var e=this.scanner.tokenStart;return this.eat(fi),{type:"IdSelector",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e+1)}},generate:function(e){this.chunk("#"),this.chunk(e.name)}},bi=we.TYPE,yi=bi.Ident,vi=bi.Number,ki=bi.Dimension,wi=bi.LeftParenthesis,xi=bi.RightParenthesis,Si=bi.Colon,Ci=bi.Delim,Ai={name:"MediaFeature",structure:{name:String,value:["Identifier","Number","Dimension","Ratio",null]},parse:function(){var e,t=this.scanner.tokenStart,n=null;if(this.eat(wi),this.scanner.skipSC(),e=this.consume(yi),this.scanner.skipSC(),this.scanner.tokenType!==xi){switch(this.eat(Si),this.scanner.skipSC(),this.scanner.tokenType){case vi:n=this.lookupNonWSType(1)===Ci?this.Ratio():this.Number();break;case ki:n=this.Dimension();break;case yi:n=this.Identifier();break;default:this.error("Number, dimension, ratio or identifier is expected")}this.scanner.skipSC()}return this.eat(xi),{type:"MediaFeature",loc:this.getLocation(t,this.scanner.tokenStart),name:e,value:n}},generate:function(e){this.chunk("("),this.chunk(e.name),null!==e.value&&(this.chunk(":"),this.node(e.value)),this.chunk(")")}},zi=we.TYPE,Pi=zi.WhiteSpace,Li=zi.Comment,Ti=zi.Ident,Ei=zi.LeftParenthesis,Oi={name:"MediaQuery",structure:{children:[["Identifier","MediaFeature","WhiteSpace"]]},parse:function(){this.scanner.skipSC();var e=this.createList(),t=null,n=null;e:for(;!this.scanner.eof;){switch(this.scanner.tokenType){case Li:this.scanner.next();continue;case Pi:n=this.WhiteSpace();continue;case Ti:t=this.Identifier();break;case Ei:t=this.MediaFeature();break;default:break e}null!==n&&(e.push(n),n=null),e.push(t)}return null===t&&this.error("Identifier or parenthesis is expected"),{type:"MediaQuery",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e)}},Di=we.TYPE.Comma,Ii={name:"MediaQueryList",structure:{children:[["MediaQuery"]]},parse:function(e){var t=this.createList();for(this.scanner.skipSC();!this.scanner.eof&&(t.push(this.MediaQuery(e)),this.scanner.tokenType===Di);)this.scanner.next();return{type:"MediaQueryList",loc:this.getLocationFromList(t),children:t}},generate:function(e){this.children(e,(function(){this.chunk(",")}))}},Ri=we.TYPE.Number,Ni={name:"Number",structure:{value:String},parse:function(){return{type:"Number",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),value:this.consume(Ri)}},generate:function(e){this.chunk(e.value)}},Bi={name:"Operator",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;return this.scanner.next(),{type:"Operator",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.value)}},Mi=we.TYPE,ji=Mi.LeftParenthesis,_i=Mi.RightParenthesis,qi={name:"Parentheses",structure:{children:[[]]},parse:function(e,t){var n,r=this.scanner.tokenStart;return this.eat(ji),n=e.call(this,t),this.scanner.eof||this.eat(_i),{type:"Parentheses",loc:this.getLocation(r,this.scanner.tokenStart),children:n}},generate:function(e){this.chunk("("),this.children(e),this.chunk(")")}},Wi=M.consumeNumber,Fi=we.TYPE.Percentage,Ui={name:"Percentage",structure:{value:String},parse:function(){var e=this.scanner.tokenStart,t=Wi(this.scanner.source,e);return this.eat(Fi),{type:"Percentage",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.source.substring(e,t)}},generate:function(e){this.chunk(e.value),this.chunk("%")}},Yi=we.TYPE,Hi=Yi.Ident,Vi=Yi.Function,Ki=Yi.Colon,Gi=Yi.RightParenthesis,$i={name:"PseudoClassSelector",structure:{name:String,children:[["Raw"],null]},parse:function(){var e,t,n=this.scanner.tokenStart,r=null;return this.eat(Ki),this.scanner.tokenType===Vi?(t=(e=this.consumeFunctionName()).toLowerCase(),this.pseudo.hasOwnProperty(t)?(this.scanner.skipSC(),r=this.pseudo[t].call(this),this.scanner.skipSC()):(r=this.createList()).push(this.Raw(this.scanner.tokenIndex,null,!1)),this.eat(Gi)):e=this.consume(Hi),{type:"PseudoClassSelector",loc:this.getLocation(n,this.scanner.tokenStart),name:e,children:r}},generate:function(e){this.chunk(":"),this.chunk(e.name),null!==e.children&&(this.chunk("("),this.children(e),this.chunk(")"))},walkContext:"function"},Qi=we.TYPE,Xi=Qi.Ident,Zi=Qi.Function,Ji=Qi.Colon,eo=Qi.RightParenthesis,to={name:"PseudoElementSelector",structure:{name:String,children:[["Raw"],null]},parse:function(){var e,t,n=this.scanner.tokenStart,r=null;return this.eat(Ji),this.eat(Ji),this.scanner.tokenType===Zi?(t=(e=this.consumeFunctionName()).toLowerCase(),this.pseudo.hasOwnProperty(t)?(this.scanner.skipSC(),r=this.pseudo[t].call(this),this.scanner.skipSC()):(r=this.createList()).push(this.Raw(this.scanner.tokenIndex,null,!1)),this.eat(eo)):e=this.consume(Xi),{type:"PseudoElementSelector",loc:this.getLocation(n,this.scanner.tokenStart),name:e,children:r}},generate:function(e){this.chunk("::"),this.chunk(e.name),null!==e.children&&(this.chunk("("),this.children(e),this.chunk(")"))},walkContext:"function"},no=we.isDigit,ro=we.TYPE,ao=ro.Number,io=ro.Delim;function oo(){this.scanner.skipWS();for(var e=this.consume(ao),t=0;t<e.length;t++){var n=e.charCodeAt(t);no(n)||46===n||this.error("Unsigned number is expected",this.scanner.tokenStart-e.length+t)}return 0===Number(e)&&this.error("Zero number is not allowed",this.scanner.tokenStart-e.length),e}var so={name:"Ratio",structure:{left:String,right:String},parse:function(){var e,t=this.scanner.tokenStart,n=oo.call(this);return this.scanner.skipWS(),this.scanner.isDelim(47)||this.error("Solidus is expected"),this.eat(io),e=oo.call(this),{type:"Ratio",loc:this.getLocation(t,this.scanner.tokenStart),left:n,right:e}},generate:function(e){this.chunk(e.left),this.chunk("/"),this.chunk(e.right)}},lo=we.TYPE,co=Fr.mode,uo=lo.LeftCurlyBracket;function ho(e){return this.Raw(e,co.leftCurlyBracket,!0)}function po(){var e=this.SelectorList();return"Raw"!==e.type&&!1===this.scanner.eof&&this.scanner.tokenType!==uo&&this.error(),e}var mo={name:"Rule",structure:{prelude:["SelectorList","Raw"],block:["Block"]},parse:function(){var e,t,n=this.scanner.tokenIndex,r=this.scanner.tokenStart;return e=this.parseRulePrelude?this.parseWithFallback(po,ho):ho.call(this,n),t=this.Block(!0),{type:"Rule",loc:this.getLocation(r,this.scanner.tokenStart),prelude:e,block:t}},generate:function(e){this.node(e.prelude),this.node(e.block)},walkContext:"rule"},fo=we.TYPE.Comma,go={name:"SelectorList",structure:{children:[["Selector","Raw"]]},parse:function(){for(var e=this.createList();!this.scanner.eof&&(e.push(this.Selector()),this.scanner.tokenType===fo);)this.scanner.next();return{type:"SelectorList",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e,(function(){this.chunk(",")}))},walkContext:"selector"},bo=we.TYPE.String,yo={name:"String",structure:{value:String},parse:function(){return{type:"String",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),value:this.consume(bo)}},generate:function(e){this.chunk(e.value)}},vo=we.TYPE,ko=vo.WhiteSpace,wo=vo.Comment,xo=vo.AtKeyword,So=vo.CDO,Co=vo.CDC;function Ao(e){return this.Raw(e,null,!1)}var zo={name:"StyleSheet",structure:{children:[["Comment","CDO","CDC","Atrule","Rule","Raw"]]},parse:function(){for(var e,t=this.scanner.tokenStart,n=this.createList();!this.scanner.eof;){switch(this.scanner.tokenType){case ko:this.scanner.next();continue;case wo:if(33!==this.scanner.source.charCodeAt(this.scanner.tokenStart+2)){this.scanner.next();continue}e=this.Comment();break;case So:e=this.CDO();break;case Co:e=this.CDC();break;case xo:e=this.parseWithFallback(this.Atrule,Ao);break;default:e=this.parseWithFallback(this.Rule,Ao)}n.push(e)}return{type:"StyleSheet",loc:this.getLocation(t,this.scanner.tokenStart),children:n}},generate:function(e){this.children(e)},walkContext:"stylesheet"},Po=we.TYPE.Ident;function Lo(){this.scanner.tokenType!==Po&&!1===this.scanner.isDelim(42)&&this.error("Identifier or asterisk is expected"),this.scanner.next()}var To={name:"TypeSelector",structure:{name:String},parse:function(){var e=this.scanner.tokenStart;return this.scanner.isDelim(124)?(this.scanner.next(),Lo.call(this)):(Lo.call(this),this.scanner.isDelim(124)&&(this.scanner.next(),Lo.call(this))),{type:"TypeSelector",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.name)}},Eo=we.isHexDigit,Oo=we.cmpChar,Do=we.TYPE,Io=we.NAME,Ro=Do.Ident,No=Do.Number,Bo=Do.Dimension;function Mo(e,t){for(var n=this.scanner.tokenStart+e,r=0;n<this.scanner.tokenEnd;n++){var a=this.scanner.source.charCodeAt(n);if(45===a&&t&&0!==r)return 0===Mo.call(this,e+r+1,!1)&&this.error(),-1;Eo(a)||this.error(t&&0!==r?"HyphenMinus"+(r<6?" or hex digit":"")+" is expected":r<6?"Hex digit is expected":"Unexpected input",n),++r>6&&this.error("Too many hex digits",n)}return this.scanner.next(),r}function jo(e){for(var t=0;this.scanner.isDelim(63);)++t>e&&this.error("Too many question marks"),this.scanner.next()}function _o(e){this.scanner.source.charCodeAt(this.scanner.tokenStart)!==e&&this.error(Io[e]+" is expected")}function qo(){var e=0;return this.scanner.isDelim(43)?(this.scanner.next(),this.scanner.tokenType===Ro?void((e=Mo.call(this,0,!0))>0&&jo.call(this,6-e)):this.scanner.isDelim(63)?(this.scanner.next(),void jo.call(this,5)):void this.error("Hex digit or question mark is expected")):this.scanner.tokenType===No?(_o.call(this,43),e=Mo.call(this,1,!0),this.scanner.isDelim(63)?void jo.call(this,6-e):this.scanner.tokenType===Bo||this.scanner.tokenType===No?(_o.call(this,45),void Mo.call(this,1,!1)):void 0):this.scanner.tokenType===Bo?(_o.call(this,43),void((e=Mo.call(this,1,!0))>0&&jo.call(this,6-e))):void this.error()}var Wo={name:"UnicodeRange",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;return Oo(this.scanner.source,e,117)||this.error("U is expected"),Oo(this.scanner.source,e+1,43)||this.error("Plus sign is expected"),this.scanner.next(),qo.call(this),{type:"UnicodeRange",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.value)}},Fo=we.isWhiteSpace,Uo=we.cmpStr,Yo=we.TYPE,Ho=Yo.Function,Vo=Yo.Url,Ko=Yo.RightParenthesis,Go={name:"Url",structure:{value:["String","Raw"]},parse:function(){var e,t=this.scanner.tokenStart;switch(this.scanner.tokenType){case Vo:for(var n=t+4,r=this.scanner.tokenEnd-1;n<r&&Fo(this.scanner.source.charCodeAt(n));)n++;for(;n<r&&Fo(this.scanner.source.charCodeAt(r-1));)r--;e={type:"Raw",loc:this.getLocation(n,r),value:this.scanner.source.substring(n,r)},this.eat(Vo);break;case Ho:Uo(this.scanner.source,this.scanner.tokenStart,this.scanner.tokenEnd,"url(")||this.error("Function name must be `url`"),this.eat(Ho),this.scanner.skipSC(),e=this.String(),this.scanner.skipSC(),this.eat(Ko);break;default:this.error("Url or Function is expected")}return{type:"Url",loc:this.getLocation(t,this.scanner.tokenStart),value:e}},generate:function(e){this.chunk("url"),this.chunk("("),this.node(e.value),this.chunk(")")}},$o=we.TYPE.WhiteSpace,Qo=Object.freeze({type:"WhiteSpace",loc:null,value:" "}),Xo={AnPlusB:Rr,Atrule:Xr,AtrulePrelude:ta,AttributeSelector:ua,Block:Sa,Brackets:Pa,CDC:Ta,CDO:Oa,ClassSelector:Ia,Combinator:Na,Comment:Ma,Declaration:Qa,DeclarationList:ii,Dimension:li,Function:ui,Hash:di,Identifier:mi,IdSelector:gi,MediaFeature:Ai,MediaQuery:Oi,MediaQueryList:Ii,Nth:{name:"Nth",structure:{nth:["AnPlusB","Identifier"],selector:["SelectorList",null]},parse:function(e){this.scanner.skipSC();var t,n=this.scanner.tokenStart,r=n,a=null;return t=this.scanner.lookupValue(0,"odd")||this.scanner.lookupValue(0,"even")?this.Identifier():this.AnPlusB(),this.scanner.skipSC(),e&&this.scanner.lookupValue(0,"of")?(this.scanner.next(),a=this.SelectorList(),this.needPositions&&(r=this.getLastListNode(a.children).loc.end.offset)):this.needPositions&&(r=t.loc.end.offset),{type:"Nth",loc:this.getLocation(n,r),nth:t,selector:a}},generate:function(e){this.node(e.nth),null!==e.selector&&(this.chunk(" of "),this.node(e.selector))}},Number:Ni,Operator:Bi,Parentheses:qi,Percentage:Ui,PseudoClassSelector:$i,PseudoElementSelector:to,Ratio:so,Raw:Fr,Rule:mo,Selector:{name:"Selector",structure:{children:[["TypeSelector","IdSelector","ClassSelector","AttributeSelector","PseudoClassSelector","PseudoElementSelector","Combinator","WhiteSpace"]]},parse:function(){var e=this.readSequence(this.scope.Selector);return null===this.getFirstListNode(e)&&this.error("Selector is expected"),{type:"Selector",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e)}},SelectorList:go,String:yo,StyleSheet:zo,TypeSelector:To,UnicodeRange:Wo,Url:Go,Value:{name:"Value",structure:{children:[[]]},parse:function(){var e=this.scanner.tokenStart,t=this.readSequence(this.scope.Value);return{type:"Value",loc:this.getLocation(e,this.scanner.tokenStart),children:t}},generate:function(e){this.children(e)}},WhiteSpace:{name:"WhiteSpace",structure:{value:String},parse:function(){return this.eat($o),Qo},generate:function(e){this.chunk(e.value)}}},Zo={generic:!0,types:wr.types,atrules:wr.atrules,properties:wr.properties,node:Xo},Jo=we.cmpChar,es=we.cmpStr,ts=we.TYPE,ns=ts.Ident,rs=ts.String,as=ts.Number,is=ts.Function,os=ts.Url,ss=ts.Hash,ls=ts.Dimension,cs=ts.Percentage,us=ts.LeftParenthesis,hs=ts.LeftSquareBracket,ds=ts.Comma,ps=ts.Delim,ms=function(e){switch(this.scanner.tokenType){case ss:return this.Hash();case ds:return e.space=null,e.ignoreWSAfter=!0,this.Operator();case us:return this.Parentheses(this.readSequence,e.recognizer);case hs:return this.Brackets(this.readSequence,e.recognizer);case rs:return this.String();case ls:return this.Dimension();case cs:return this.Percentage();case as:return this.Number();case is:return es(this.scanner.source,this.scanner.tokenStart,this.scanner.tokenEnd,"url(")?this.Url():this.Function(this.readSequence,e.recognizer);case os:return this.Url();case ns:return Jo(this.scanner.source,this.scanner.tokenStart,117)&&Jo(this.scanner.source,this.scanner.tokenStart+1,43)?this.UnicodeRange():this.Identifier();case ps:var t=this.scanner.source.charCodeAt(this.scanner.tokenStart);if(47===t||42===t||43===t||45===t)return this.Operator();35===t&&this.error("Hex or identifier is expected",this.scanner.tokenStart+1)}},fs={getNode:ms},gs=we.TYPE,bs=gs.Delim,ys=gs.Ident,vs=gs.Dimension,ks=gs.Percentage,ws=gs.Number,xs=gs.Hash,Ss=gs.Colon,Cs=gs.LeftSquareBracket,As={getNode:function(e){switch(this.scanner.tokenType){case Cs:return this.AttributeSelector();case xs:return this.IdSelector();case Ss:return this.scanner.lookupType(1)===Ss?this.PseudoElementSelector():this.PseudoClassSelector();case ys:return this.TypeSelector();case ws:case ks:return this.Percentage();case vs:46===this.scanner.source.charCodeAt(this.scanner.tokenStart)&&this.error("Identifier is expected",this.scanner.tokenStart+1);break;case bs:switch(this.scanner.source.charCodeAt(this.scanner.tokenStart)){case 43:case 62:case 126:return e.space=null,e.ignoreWSAfter=!0,this.Combinator();case 47:return this.Combinator();case 46:return this.ClassSelector();case 42:case 124:return this.TypeSelector();case 35:return this.IdSelector()}}}},zs=we.TYPE,Ps=Fr.mode,Ls=zs.Comma,Ts=zs.WhiteSpace,Es={AtrulePrelude:fs,Selector:As,Value:{getNode:ms,expression:function(){return this.createSingleNodeList(this.Raw(this.scanner.tokenIndex,null,!1))},var:function(){var e=this.createList();if(this.scanner.skipSC(),e.push(this.Identifier()),this.scanner.skipSC(),this.scanner.tokenType===Ls){e.push(this.Operator());const t=this.scanner.tokenIndex,n=this.parseCustomProperty?this.Value(null):this.Raw(this.scanner.tokenIndex,Ps.exclamationMarkOrSemicolon,!1);if("Value"===n.type&&n.children.isEmpty())for(let e=t-this.scanner.tokenIndex;e<=0;e++)if(this.scanner.lookupType(e)===Ts){n.children.appendData({type:"WhiteSpace",loc:null,value:" "});break}e.push(n)}return e}}},Os=we.TYPE,Ds=Os.String,Is=Os.Ident,Rs=Os.Url,Ns=Os.Function,Bs=Os.LeftParenthesis,Ms={parse:{prelude:function(){var e=this.createList();switch(this.scanner.skipSC(),this.scanner.tokenType){case Ds:e.push(this.String());break;case Rs:case Ns:e.push(this.Url());break;default:this.error("String or url() is expected")}return this.lookupNonWSType(0)!==Is&&this.lookupNonWSType(0)!==Bs||(e.push(this.WhiteSpace()),e.push(this.MediaQueryList())),e},block:null}},js=we.TYPE,_s=js.WhiteSpace,qs=js.Comment,Ws=js.Ident,Fs=js.Function,Us=js.Colon,Ys=js.LeftParenthesis;function Hs(){return this.createSingleNodeList(this.Raw(this.scanner.tokenIndex,null,!1))}function Vs(){return this.scanner.skipSC(),this.scanner.tokenType===Ws&&this.lookupNonWSType(1)===Us?this.createSingleNodeList(this.Declaration()):Ks.call(this)}function Ks(){var e,t=this.createList(),n=null;this.scanner.skipSC();e:for(;!this.scanner.eof;){switch(this.scanner.tokenType){case _s:n=this.WhiteSpace();continue;case qs:this.scanner.next();continue;case Fs:e=this.Function(Hs,this.scope.AtrulePrelude);break;case Ws:e=this.Identifier();break;case Ys:e=this.Parentheses(Vs,this.scope.AtrulePrelude);break;default:break e}null!==n&&(t.push(n),n=null),t.push(e)}return t}var Gs,$s={parse:function(){return this.createSingleNodeList(this.SelectorList())}},Qs={parse:function(){return this.createSingleNodeList(this.Nth(!0))}},Xs={parse:function(){return this.createSingleNodeList(this.Nth(!1))}},Zs={parseContext:{default:"StyleSheet",stylesheet:"StyleSheet",atrule:"Atrule",atrulePrelude:function(e){return this.AtrulePrelude(e.atrule?String(e.atrule):null)},mediaQueryList:"MediaQueryList",mediaQuery:"MediaQuery",rule:"Rule",selectorList:"SelectorList",selector:"Selector",block:function(){return this.Block(!0)},declarationList:"DeclarationList",declaration:"Declaration",value:"Value"},scope:Es,atrule:{"font-face":{parse:{prelude:null,block:function(){return this.Block(!0)}}},import:Ms,media:{parse:{prelude:function(){return this.createSingleNodeList(this.MediaQueryList())},block:function(){return this.Block(!1)}}},page:{parse:{prelude:function(){return this.createSingleNodeList(this.SelectorList())},block:function(){return this.Block(!0)}}},supports:{parse:{prelude:function(){var e=Ks.call(this);return null===this.getFirstListNode(e)&&this.error("Condition is expected"),e},block:function(){return this.Block(!1)}}}},pseudo:{dir:{parse:function(){return this.createSingleNodeList(this.Identifier())}},has:{parse:function(){return this.createSingleNodeList(this.SelectorList())}},lang:{parse:function(){return this.createSingleNodeList(this.Identifier())}},matches:$s,not:$s,"nth-child":Qs,"nth-last-child":Qs,"nth-last-of-type":Xs,"nth-of-type":Xs,slotted:{parse:function(){return this.createSingleNodeList(this.Selector())}}},node:Xo},Js={node:Xo},el=(Gs=Object.freeze({__proto__:null,version:"1.1.2",default:{version:"1.1.2"}}))&&Gs.default||Gs,tl=function(e){return kr(vr({},e))}(function(){for(var e={},t=0;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}(Zo,Zs,Js)),nl=el.version;return tl.version=nl,tl}()})),n=Object.prototype.hasOwnProperty;function r(e,t){var n=Object.create(null);if(!Array.isArray(e))return null;for(var r=0;r<e.length;r++){var a=e[r];t&&(a=a.toLowerCase()),n[a]=!0}return n}function a(e){if(!e)return null;var t=r(e.tags,!0),n=r(e.ids),a=r(e.classes);return null===t&&null===n&&null===a?null:{tags:t,ids:n,classes:a}}var i={buildIndex:function(e){var t=!1;if(e.scopes&&Array.isArray(e.scopes)){t=Object.create(null);for(var r=0;r<e.scopes.length;r++){var i=e.scopes[r];if(!i||!Array.isArray(i))throw new Error("Wrong usage format");for(var o=0;o<i.length;o++){var s=i[o];if(n.call(t,s))throw new Error("Class can't be used for several scopes: "+s);t[s]=r+1}}}return{whitelist:a(e),blacklist:a(e.blacklist),scopes:t}}},o={hasNoChildren:function(e){return!e||!e.children||e.children.isEmpty()},isNodeChildrenList:function(e,t){return null!==e&&e.children===t}},s=t.keyword,{hasNoChildren:l}=o,c=t.property,{isNodeChildrenList:u}=o,h=Object.prototype.hasOwnProperty,d=t.walk,{hasNoChildren:p}=o;var{isNodeChildrenList:m}=o;function f(e){return"Operator"===e.type&&"+"!==e.value&&"-"!==e.value}var g=t.walk,b={Atrule:function(e,t,n){if(e.block&&(null!==this.stylesheet&&(this.stylesheet.firstAtrulesAllowed=!1),l(e.block)))n.remove(t);else switch(e.name){case"charset":if(l(e.prelude))return void n.remove(t);if(t.prev)return void n.remove(t);break;case"import":if(null===this.stylesheet||!this.stylesheet.firstAtrulesAllowed)return void n.remove(t);n.prevUntil(t.prev,(function(e){if("Atrule"!==e.type||"import"!==e.name&&"charset"!==e.name)return this.root.firstAtrulesAllowed=!1,n.remove(t),!0}),this);break;default:var r=s(e.name).basename;"keyframes"!==r&&"media"!==r&&"supports"!==r||(l(e.prelude)||l(e.block))&&n.remove(t)}},Comment:function(e,t,n){n.remove(t)},Declaration:function(e,t,n){e.value.children&&e.value.children.isEmpty()?n.remove(t):c(e.property).custom&&/\S/.test(e.value.value)&&(e.value.value=e.value.value.trim())},Raw:function(e,t,n){(u(this.stylesheet,n)||u(this.block,n))&&n.remove(t)},Rule:function(e,t,n,r){if(p(e.prelude)||p(e.block))n.remove(t);else{var a=r.usage;!a||null===a.whitelist&&null===a.blacklist||(function e(t,n){return t.children.each((function(r,a,i){var o=!1;d(r,(function(r){if(null===this.selector||this.selector===t)switch(r.type){case"SelectorList":null!==this.function&&"not"===this.function.name.toLowerCase()||e(r,n)&&(o=!0);break;case"ClassSelector":null===n.whitelist||null===n.whitelist.classes||h.call(n.whitelist.classes,r.name)||(o=!0),null!==n.blacklist&&null!==n.blacklist.classes&&h.call(n.blacklist.classes,r.name)&&(o=!0);break;case"IdSelector":null===n.whitelist||null===n.whitelist.ids||h.call(n.whitelist.ids,r.name)||(o=!0),null!==n.blacklist&&null!==n.blacklist.ids&&h.call(n.blacklist.ids,r.name)&&(o=!0);break;case"TypeSelector":"*"!==r.name.charAt(r.name.length-1)&&(null===n.whitelist||null===n.whitelist.tags||h.call(n.whitelist.tags,r.name.toLowerCase())||(o=!0),null!==n.blacklist&&null!==n.blacklist.tags&&h.call(n.blacklist.tags,r.name.toLowerCase())&&(o=!0))}})),o&&i.remove(a)})),t.children.isEmpty()}(e.prelude,a),!p(e.prelude))||n.remove(t)}},TypeSelector:function(e,t,n){if("*"===t.data.name){var r=t.next&&t.next.data.type;"IdSelector"!==r&&"ClassSelector"!==r&&"AttributeSelector"!==r&&"PseudoClassSelector"!==r&&"PseudoElementSelector"!==r||n.remove(t)}},WhiteSpace:function(e,t,n){null!==t.next&&null!==t.prev?m(this.stylesheet,n)||m(this.block,n)?n.remove(t):"WhiteSpace"!==t.next.data.type?(f(t.prev.data)||f(t.next.data))&&n.remove(t):n.remove(t):n.remove(t)}},y=t.keyword,v=/\\([0-9A-Fa-f]{1,6})(\r\n|[ \t\n\f\r])?|\\./g,k=/^(-?\d|--)|[\u0000-\u002c\u002e\u002f\u003A-\u0040\u005B-\u005E\u0060\u007B-\u009f]/;var w=t.List;var x=function(e){e.children.each((function(e,t,n){"Identifier"===e.type&&"none"===e.name.toLowerCase()&&(n.head===n.tail?t.data={type:"Number",loc:e.loc,value:"0"}:function(e,t){var n=t.prev,r=t.next;null!==r?"WhiteSpace"!==r.data.type||null!==n&&"WhiteSpace"!==n.data.type||e.remove(r):null!==n&&"WhiteSpace"===n.data.type&&e.remove(n),e.remove(t)}(n,t))}))},S=t.property,C={font:function(e){var t=e.children;t.eachRight((function(e,t){if("Identifier"===e.type)if("bold"===e.name)t.data={type:"Number",loc:e.loc,value:"700"};else if("normal"===e.name){var n=t.prev;n&&"Operator"===n.data.type&&"/"===n.data.value&&this.remove(n),this.remove(t)}else if("medium"===e.name){var r=t.next;r&&"Operator"===r.data.type||this.remove(t)}})),t.each((function(e,t){"WhiteSpace"===e.type&&(t.prev&&t.next&&"WhiteSpace"!==t.next.data.type||this.remove(t))})),t.isEmpty()&&t.insert(t.createItem({type:"Identifier",name:"normal"}))},"font-weight":function(e){var t=e.children.head.data;if("Identifier"===t.type)switch(t.name){case"normal":e.children.head.data={type:"Number",loc:t.loc,value:"400"};break;case"bold":e.children.head.data={type:"Number",loc:t.loc,value:"700"}}},background:function(e){function t(){if(a.length)return a[a.length-1].type}function n(){"WhiteSpace"===t()&&a.pop(),a.length||a.unshift({type:"Number",loc:null,value:"0"},{type:"WhiteSpace",value:" "},{type:"Number",loc:null,value:"0"}),r.push.apply(r,a),a=[]}var r=[],a=[];e.children.each((function(e){if("Operator"===e.type&&","===e.value)return n(),void r.push(e);("Identifier"!==e.type||"transparent"!==e.name&&"none"!==e.name&&"repeat"!==e.name&&"scroll"!==e.name)&&("WhiteSpace"!==e.type||a.length&&"WhiteSpace"!==t())&&a.push(e)})),n(),e.children=(new w).fromArray(r)},border:x,outline:x},A=/^(?:\+|(-))?0*(\d*)(?:\.0*|(\.\d*?)0*)?$/,z=/^([\+\-])?0*(\d*)(?:\.0*|(\.\d*?)0*)?$/,P={Dimension:!0,Hash:!0,Identifier:!0,Number:!0,Raw:!0,UnicodeRange:!0};function L(e,t){var n=t&&null!==t.prev&&P.hasOwnProperty(t.prev.data.type)?z:A;return""!==(e=String(e).replace(n,"$1$2$3"))&&"-"!==e||(e="0"),e}var T=function(e,t){e.value=L(e.value,t)},E=T.pack=L,O={calc:!0,min:!0,max:!0,clamp:!0},D={px:!0,mm:!0,cm:!0,in:!0,pt:!0,pc:!0,em:!0,ex:!0,ch:!0,rem:!0,vh:!0,vw:!0,vmin:!0,vmax:!0,vm:!0},I=t.lexer,R=T.pack,N=new Set(["width","min-width","max-width","height","min-height","max-height","flex","-ms-flex"]),B=new RegExp("^((\\\\[0-9a-f]{1,6}(\\r\\n|[ \\n\\r\\t\\f])?|\\\\[^\\n\\r\\f0-9a-fA-F])|[^\"'\\(\\)\\\\\\s\0\b\v-])*$","i"),M=t.lexer,j=T.pack,_={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgrey:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",grey:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"639",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},q={8e5:"maroon",800080:"purple",808e3:"olive",808080:"gray","00ffff":"cyan",f0ffff:"azure",f5f5dc:"beige",ffe4c4:"bisque","000000":"black","0000ff":"blue",a52a2a:"brown",ff7f50:"coral",ffd700:"gold","008000":"green","4b0082":"indigo",fffff0:"ivory",f0e68c:"khaki","00ff00":"lime",faf0e6:"linen","000080":"navy",ffa500:"orange",da70d6:"orchid",cd853f:"peru",ffc0cb:"pink",dda0dd:"plum",f00:"red",ff0000:"red",fa8072:"salmon",a0522d:"sienna",c0c0c0:"silver",fffafa:"snow",d2b48c:"tan","008080":"teal",ff6347:"tomato",ee82ee:"violet",f5deb3:"wheat",ffffff:"white",ffff00:"yellow"};function W(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function F(e,t,n,r){var a,i,o;if(0===t)a=i=o=n;else{var s=n<.5?n*(1+t):n+t-n*t,l=2*n-s;a=W(l,s,e+1/3),i=W(l,s,e),o=W(l,s,e-1/3)}return[Math.round(255*a),Math.round(255*i),Math.round(255*o),r]}function U(e){return 1===(e=e.toString(16)).length?"0"+e:e}function Y(e,t,n){for(var r=e.head,a=[],i=!1;null!==r;){var o=r.data,s=o.type;switch(s){case"Number":case"Percentage":if(i)return;i=!0,a.push({type:s,value:Number(o.value)});break;case"Operator":if(","===o.value){if(!i)return;i=!1}else if(i||"+"!==o.value)return;break;default:return}r=r.next}if(a.length===t){if(4===a.length){if("Number"!==a[3].type)return;a[3].type="Alpha"}if(n){if(a[0].type!==a[1].type||a[0].type!==a[2].type)return}else{if("Number"!==a[0].type||"Percentage"!==a[1].type||"Percentage"!==a[2].type)return;a[0].type="Angle"}return a.map((function(e){var t=Math.max(0,e.value);switch(e.type){case"Number":t=Math.min(t,255);break;case"Percentage":if(t=Math.min(t,100)/100,!n)return t;t*=255;break;case"Angle":return(t%360+360)%360/360;case"Alpha":return Math.min(t,1)}return Math.round(t)}))}}function H(e,t){var n=e.value.toLowerCase();6===n.length&&n[0]===n[1]&&n[2]===n[3]&&n[4]===n[5]&&(n=n[0]+n[2]+n[4]),q[n]?t.data={type:"Identifier",loc:e.loc,name:q[n]}:e.value=n}var V={compressFunction:function(e,t,n){var r,a=e.name;if("rgba"===a||"hsla"===a){if(!(r=Y(e.children,4,"rgba"===a)))return;if("hsla"===a&&(r=F.apply(null,r),e.name="rgba"),0===r[3]){var i=this.function&&this.function.name;if(0===r[0]&&0===r[1]&&0===r[2]||!/^(?:to|from|color-stop)$|gradient$/i.test(i))return void(t.data={type:"Identifier",loc:e.loc,name:"transparent"})}if(1!==r[3])return void e.children.each((function(e,t,n){"Operator"!==e.type?t.data={type:"Number",loc:e.loc,value:j(r.shift(),null)}:","!==e.value&&n.remove(t)}));a="rgb"}if("hsl"===a){if(!(r=r||Y(e.children,3,!1)))return;r=F.apply(null,r),a="rgb"}if("rgb"===a){if(!(r=r||Y(e.children,3,!0)))return;var o=t.next;o&&"WhiteSpace"!==o.data.type&&n.insert(n.createItem({type:"WhiteSpace",value:" "}),o),t.data={type:"Hash",loc:e.loc,value:U(r[0])+U(r[1])+U(r[2])},H(t.data,t)}},compressIdent:function(e,t){if(null!==this.declaration){var n=e.name.toLowerCase();if(_.hasOwnProperty(n)&&M.matchDeclaration(this.declaration).isType(e,"color")){var r=_[n];r.length+1<=n.length?t.data={type:"Hash",loc:e.loc,value:r}:("grey"===n&&(n="gray"),e.name=n)}}},compressHex:H},K=t.walk,G={Atrule:function(e){"keyframes"===y(e.name).basename&&function(e){e.block.children.each((function(e){e.prelude.children.each((function(e){e.children.each((function(e,t){"Percentage"===e.type&&"100"===e.value?t.data={type:"TypeSelector",loc:e.loc,name:"to"}:"TypeSelector"===e.type&&"from"===e.name&&(t.data={type:"Percentage",loc:e.loc,value:"0"})}))}))}))}(e)},AttributeSelector:function(e){var t=e.value;if(t&&"String"===t.type){var n=t.value.replace(/^(.)(.*)\1$/,"$2");(function(e){if(""!==e&&"-"!==e)return e=e.replace(v,"a"),!k.test(e)})(n)&&(e.value={type:"Identifier",loc:t.loc,name:n})}},Value:function(e){if(this.declaration){var t=S(this.declaration.property);C.hasOwnProperty(t.basename)&&C[t.basename](e)}},Dimension:function(e,t){var n=E(e.value,t);if(e.value=n,"0"===n&&null!==this.declaration&&null===this.atrulePrelude){var r=e.unit.toLowerCase();if(!D.hasOwnProperty(r))return;if("-ms-flex"===this.declaration.property||"flex"===this.declaration.property)return;if(this.function&&O.hasOwnProperty(this.function.name))return;t.data={type:"Number",loc:e.loc,value:n}}},Percentage:function(e,t){e.value=R(e.value,t),"0"===e.value&&this.declaration&&!N.has(this.declaration.property)&&(t.data={type:"Number",loc:e.loc,value:e.value},I.matchDeclaration(this.declaration).isType(t.data,"length")||(t.data=e))},Number:T,String:function(e){var t=e.value;t=t.replace(/\\(\r\n|\r|\n|\f)/g,""),e.value=t},Url:function(e){var t=e.value;if("String"===t.type){var n=t.value[0],r=t.value.substr(1,t.value.length-2);r=r.replace(/\\\\/g,"/"),B.test(r)?e.value={type:"Raw",loc:e.value.loc,value:r}:e.value.value=-1===r.indexOf('"')?'"'+r+'"':n+r+n}},Hash:V.compressHex,Identifier:V.compressIdent,Function:V.compressFunction},$=t.generate;function Q(){this.seed=0,this.map=Object.create(null)}Q.prototype.resolve=function(e){var t=this.map[e];return t||(t=++this.seed,this.map[e]=t),t};var X=t.generate,Z={"first-letter":!0,"first-line":!0,after:!0,before:!0},J={link:!0,visited:!0,hover:!0,active:!0,"first-letter":!0,"first-line":!0,after:!0,before:!0},ee=t.keyword,te=t.walk,ne=t.generate,re=function(e,t){var n,r=(n=new Q,function(e){var t=$(e);return e.id=n.resolve(t),e.length=t.length,e.fingerprint=null,e});return te(e,{visit:"Rule",enter:function(e){e.block.children.each(r),function(e,t){var n=Object.create(null),r=!1;e.prelude.children.each((function(e){var a="*",i=0;e.children.each((function(o){switch(o.type){case"ClassSelector":if(t&&t.scopes){var s=t.scopes[o.name]||0;if(0!==i&&s!==i)throw new Error("Selector can't has classes from different scopes: "+X(e));i=s}break;case"PseudoClassSelector":var l=o.name.toLowerCase();J.hasOwnProperty(l)||(n[":"+l]=!0,r=!0);break;case"PseudoElementSelector":l=o.name.toLowerCase();Z.hasOwnProperty(l)||(n["::"+l]=!0,r=!0);break;case"TypeSelector":a=o.name.toLowerCase();break;case"AttributeSelector":o.flags&&(n["["+o.flags.toLowerCase()+"]"]=!0,r=!0);break;case"WhiteSpace":case"Combinator":a="*"}})),e.compareMarker=function(e){var t=0,n=0,r=0;return e.children.each((function e(a){switch(a.type){case"SelectorList":case"Selector":a.children.each(e);break;case"IdSelector":t++;break;case"ClassSelector":case"AttributeSelector":n++;break;case"PseudoClassSelector":switch(a.name.toLowerCase()){case"not":a.children.each(e);break;case"before":case"after":case"first-line":case"first-letter":r++;break;default:n++}break;case"PseudoElementSelector":r++;break;case"TypeSelector":"*"!==a.name.charAt(a.name.length-1)&&r++}})),[t,n,r]}(e).toString(),e.id=null,e.id=X(e),i&&(e.compareMarker+=":"+i),"*"!==a&&(e.compareMarker+=","+a)})),e.pseudoSignature=r&&Object.keys(n).sort().join(",")}(e,t.usage)}}),te(e,{visit:"Atrule",enter:function(e){e.prelude&&(e.prelude.id=null,e.prelude.id=ne(e.prelude)),"keyframes"===ee(e.name).basename&&(e.block.avoidRulesMerge=!0,e.block.children.each((function(e){e.prelude.children.each((function(e){e.compareMarker=e.id}))})))}}),{declaration:r}},ae=t.List,ie=t.keyword,oe=Object.prototype.hasOwnProperty,se=t.walk;function le(e,t,n,r){var a=t.data,i=ie(a.name).basename,o=a.name.toLowerCase()+"/"+(a.prelude?a.prelude.id:null);oe.call(e,i)||(e[i]=Object.create(null)),r&&delete e[i][o],oe.call(e[i],o)||(e[i][o]=new ae),e[i][o].append(n.remove(t))}function ce(e){return"Atrule"===e.type&&"media"===e.name}function ue(e,t,n){if(ce(e)){var r=t.prev&&t.prev.data;r&&ce(r)&&e.prelude&&r.prelude&&e.prelude.id===r.prelude.id&&(r.block.children.appendList(e.block.children),n.remove(t))}}var he=function(e,t){!function(e,t){var n=Object.create(null),r=null;for(var a in e.children.each((function(e,a,i){if("Atrule"===e.type){var o=ie(e.name).basename;switch(o){case"keyframes":return void le(n,a,i,!0);case"media":if(t.forceMediaMerge)return void le(n,a,i,!1)}null===r&&"charset"!==o&&"import"!==o&&(r=a)}else null===r&&(r=a)})),n)for(var i in n[a])e.children.insertList(n[a][i],"media"===a?null:r)}(e,t),se(e,{visit:"Atrule",reverse:!0,enter:ue})},de=Object.prototype.hasOwnProperty;function pe(e,t){for(var n=e.head;null!==n;){for(var r=t.head;null!==r;){if(n.data.compareMarker===r.data.compareMarker)return!0;r=r.next}n=n.next}return!1}var me={isEqualSelectors:function(e,t){for(var n=e.head,r=t.head;null!==n&&null!==r&&n.data.id===r.data.id;)n=n.next,r=r.next;return null===n&&null===r},isEqualDeclarations:function(e,t){for(var n=e.head,r=t.head;null!==n&&null!==r&&n.data.id===r.data.id;)n=n.next,r=r.next;return null===n&&null===r},compareDeclarations:function(e,t){for(var n={eq:[],ne1:[],ne2:[],ne2overrided:[]},r=Object.create(null),a=Object.create(null),i=t.head;i;i=i.next)a[i.data.id]=!0;for(i=e.head;i;i=i.next){(o=i.data).fingerprint&&(r[o.fingerprint]=o.important),a[o.id]?(a[o.id]=!1,n.eq.push(o)):n.ne1.push(o)}for(i=t.head;i;i=i.next){var o;a[(o=i.data).id]&&((!de.call(r,o.fingerprint)||!r[o.fingerprint]&&o.important)&&n.ne2.push(o),n.ne2overrided.push(o))}return n},addSelectors:function(e,t){return t.each((function(t){for(var n=t.id,r=e.head;r;){var a=r.data.id;if(a===n)return;if(a>n)break;r=r.next}e.insert(e.createItem(t),r)})),e},hasSimilarSelectors:pe,unsafeToSkipNode:function e(t){switch(t.type){case"Rule":return pe(t.prelude.children,this);case"Atrule":if(t.block)return t.block.children.some(e,this);break;case"Declaration":return!1}return!0}},fe=t.walk;function ge(e,t,n){var r=e.prelude.children,a=e.block.children;n.prevUntil(t.prev,(function(i){if("Rule"!==i.type)return me.unsafeToSkipNode.call(r,i);var o=i.prelude.children,s=i.block.children;if(e.pseudoSignature===i.pseudoSignature){if(me.isEqualSelectors(o,r))return s.appendList(a),n.remove(t),!0;if(me.isEqualDeclarations(a,s))return me.addSelectors(o,r),n.remove(t),!0}return me.hasSimilarSelectors(r,o)}))}var be=t.List,ye=t.walk;function ve(e,t,n){for(var r=e.prelude.children;r.head!==r.tail;){var a=new be;a.insert(r.remove(r.head)),n.insert(n.createItem({type:"Rule",loc:e.loc,prelude:{type:"SelectorList",loc:e.prelude.loc,children:a},block:{type:"Block",loc:e.block.loc,children:e.block.children.copy()},pseudoSignature:e.pseudoSignature}),t)}}var ke=t.List,we=t.generate,xe=t.walk,Se=["top","right","bottom","left"],Ce={"margin-top":"top","margin-right":"right","margin-bottom":"bottom","margin-left":"left","padding-top":"top","padding-right":"right","padding-bottom":"bottom","padding-left":"left","border-top-color":"top","border-right-color":"right","border-bottom-color":"bottom","border-left-color":"left","border-top-width":"top","border-right-width":"right","border-bottom-width":"bottom","border-left-width":"left","border-top-style":"top","border-right-style":"right","border-bottom-style":"bottom","border-left-style":"left"},Ae={margin:"margin","margin-top":"margin","margin-right":"margin","margin-bottom":"margin","margin-left":"margin",padding:"padding","padding-top":"padding","padding-right":"padding","padding-bottom":"padding","padding-left":"padding","border-color":"border-color","border-top-color":"border-color","border-right-color":"border-color","border-bottom-color":"border-color","border-left-color":"border-color","border-width":"border-width","border-top-width":"border-width","border-right-width":"border-width","border-bottom-width":"border-width","border-left-width":"border-width","border-style":"border-style","border-top-style":"border-style","border-right-style":"border-style","border-bottom-style":"border-style","border-left-style":"border-style"};function ze(e){this.name=e,this.loc=null,this.iehack=void 0,this.sides={top:null,right:null,bottom:null,left:null}}function Pe(e,t,n,r){var a=e.block.children,i=e.prelude.children.first().id;return e.block.children.eachRight((function(e,o){var s=e.property;if(Ae.hasOwnProperty(s)){var l,c,u=Ae[s];r&&i!==r||u in t&&(c=2,l=t[u]),l&&l.add(s,e)||(c=1,(l=new ze(u)).add(s,e))?(t[u]=l,n.push({operation:c,block:a,item:o,shorthand:l}),r=i):r=null}})),r}ze.prototype.getValueSequence=function(e,t){var n=[],r="";return!("Value"!==e.value.type||e.value.children.some((function(t){var a=!1;switch(t.type){case"Identifier":switch(t.name){case"\\0":case"\\9":return void(r=t.name);case"inherit":case"initial":case"unset":case"revert":a=t.name}break;case"Dimension":switch(t.unit){case"rem":case"vw":case"vh":case"vmin":case"vmax":case"vm":a=t.unit}break;case"Hash":case"Number":case"Percentage":break;case"Function":if("var"===t.name)return!0;a=t.name;break;case"WhiteSpace":return!1;default:return!0}n.push({node:t,special:a,important:e.important})}))||n.length>t)&&(("string"!=typeof this.iehack||this.iehack===r)&&(this.iehack=r,n))},ze.prototype.canOverride=function(e,t){var n=this.sides[e];return!n||t.important&&!n.important},ze.prototype.add=function(e,t){return!!function(){var n=this.sides,r=Ce[e];if(r){if(r in n==!1)return!1;if(!(i=this.getValueSequence(t,1))||!i.length)return!1;for(var a in n)if(null!==n[a]&&n[a].special!==i[0].special)return!1;return!this.canOverride(r,i[0])||(n[r]=i[0],!0)}if(e===this.name){var i;if(!(i=this.getValueSequence(t,4))||!i.length)return!1;switch(i.length){case 1:i[1]=i[0],i[2]=i[0],i[3]=i[0];break;case 2:i[2]=i[0],i[3]=i[1];break;case 3:i[3]=i[1]}for(var o=0;o<4;o++)for(var a in n)if(null!==n[a]&&n[a].special!==i[o].special)return!1;for(o=0;o<4;o++)this.canOverride(Se[o],i[o])&&(n[Se[o]]=i[o]);return!0}}.call(this)&&(this.loc||(this.loc=t.loc),!0)},ze.prototype.isOkToMinimize=function(){var e=this.sides.top,t=this.sides.right,n=this.sides.bottom,r=this.sides.left;if(e&&t&&n&&r){var a=e.important+t.important+n.important+r.important;return 0===a||4===a}return!1},ze.prototype.getValue=function(){var e=new ke,t=this.sides,n=[t.top,t.right,t.bottom,t.left],r=[we(t.top.node),we(t.right.node),we(t.bottom.node),we(t.left.node)];r[3]===r[1]&&(n.pop(),r[2]===r[0]&&(n.pop(),r[1]===r[0]&&n.pop()));for(var a=0;a<n.length;a++)a&&e.appendData({type:"WhiteSpace",value:" "}),e.appendData(n[a].node);return this.iehack&&(e.appendData({type:"WhiteSpace",value:" "}),e.appendData({type:"Identifier",loc:null,name:this.iehack})),{type:"Value",loc:null,children:e}},ze.prototype.getDeclaration=function(){return{type:"Declaration",loc:this.loc,important:this.sides.top.important,property:this.name,value:this.getValue()}};var Le=function(e,t){var n={},r=[];xe(e,{visit:"Rule",reverse:!0,enter:function(e){var t,a,i=this.block||this.stylesheet,o=(e.pseudoSignature||"")+"|"+e.prelude.children.first().id;n.hasOwnProperty(i.id)?t=n[i.id]:(t={lastShortSelector:null},n[i.id]=t),t.hasOwnProperty(o)?a=t[o]:(a={},t[o]=a),t.lastShortSelector=Pe.call(this,e,a,r,t.lastShortSelector)}}),function(e,t){e.forEach((function(e){var n=e.shorthand;n.isOkToMinimize()&&(1===e.operation?e.item.data=t(n.getDeclaration()):e.block.remove(e.item))}))}(r,t.declaration)},Te=t.property,Ee=t.keyword,Oe=t.walk,De=t.generate,Ie=1,Re={src:1},Ne={display:/table|ruby|flex|-(flex)?box$|grid|contents|run-in/i,"text-align":/^(start|end|match-parent|justify-all)$/i},Be={cursor:["auto","crosshair","default","move","text","wait","help","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","pointer","progress","not-allowed","no-drop","vertical-text","all-scroll","col-resize","row-resize"],overflow:["hidden","visible","scroll","auto"],position:["static","relative","absolute","fixed"]},Me={"border-width":["border"],"border-style":["border"],"border-color":["border"],"border-top":["border"],"border-right":["border"],"border-bottom":["border"],"border-left":["border"],"border-top-width":["border-top","border-width","border"],"border-right-width":["border-right","border-width","border"],"border-bottom-width":["border-bottom","border-width","border"],"border-left-width":["border-left","border-width","border"],"border-top-style":["border-top","border-style","border"],"border-right-style":["border-right","border-style","border"],"border-bottom-style":["border-bottom","border-style","border"],"border-left-style":["border-left","border-style","border"],"border-top-color":["border-top","border-color","border"],"border-right-color":["border-right","border-color","border"],"border-bottom-color":["border-bottom","border-color","border"],"border-left-color":["border-left","border-color","border"],"margin-top":["margin"],"margin-right":["margin"],"margin-bottom":["margin"],"margin-left":["margin"],"padding-top":["padding"],"padding-right":["padding"],"padding-bottom":["padding"],"padding-left":["padding"],"font-style":["font"],"font-variant":["font"],"font-weight":["font"],"font-size":["font"],"font-family":["font"],"list-style-type":["list-style"],"list-style-position":["list-style"],"list-style-image":["list-style"]};function je(e,t,n){var r=Te(e).basename;if("background"===r)return e+":"+De(t.value);var a=t.id,i=n[a];if(!i){switch(t.value.type){case"Value":var o="",s="",l={},c=!1;t.value.children.each((function e(t){switch(t.type){case"Value":case"Brackets":case"Parentheses":t.children.each(e);break;case"Raw":c=!0;break;case"Identifier":var n=t.name;o||(o=Ee(n).vendor),/\\[09]/.test(n)&&(s=RegExp.lastMatch),Be.hasOwnProperty(r)?-1===Be[r].indexOf(n)&&(l[n]=!0):Ne.hasOwnProperty(r)&&Ne[r].test(n)&&(l[n]=!0);break;case"Function":n=t.name;if(o||(o=Ee(n).vendor),"rect"===n)t.children.some((function(e){return"Operator"===e.type&&","===e.value}))||(n="rect-backward");l[n+"()"]=!0,t.children.each(e);break;case"Dimension":var a=t.unit;switch(/\\[09]/.test(a)&&(s=RegExp.lastMatch),a){case"rem":case"vw":case"vh":case"vmin":case"vmax":case"vm":l[a]=!0}}})),i=c?"!"+Ie++:"!"+Object.keys(l).sort()+"|"+s+o;break;case"Raw":i="!"+t.value.value;break;default:i=De(t.value)}n[a]=i}return e+i}function _e(e,t,n,r,a){var i=e.block.children;i.eachRight((function(e,t){var n,o=e.property,s=je(o,e,a);(n=r[s])&&!Re.hasOwnProperty(o)?e.important&&!n.item.data.important?(r[s]={block:i,item:t},n.block.remove(n.item)):i.remove(t):(n=function(e,t,n){var r=Te(t.property);if(Me.hasOwnProperty(r.basename))for(var a=Me[r.basename],i=0;i<a.length;i++){var o=je(r.prefix+a[i],t,n),s=e.hasOwnProperty(o)?e[o]:null;if(s&&(!t.important||s.item.data.important))return s}}(r,e,a))?i.remove(t):(e.fingerprint=s,r[s]={block:i,item:t})})),i.isEmpty()&&n.remove(t)}var qe=t.walk;function We(e,t,n){var r=e.prelude.children,a=e.block.children,i=r.first().compareMarker,o={};n.nextUntil(t.next,(function(t,s){if("Rule"!==t.type)return me.unsafeToSkipNode.call(r,t);if(e.pseudoSignature!==t.pseudoSignature)return!0;var l=t.prelude.children.head,c=t.block.children,u=l.data.compareMarker;if(u in o)return!0;if(r.head===r.tail&&r.first().id===l.data.id)return a.appendList(c),void n.remove(s);if(me.isEqualDeclarations(a,c)){var h=l.data.id;return r.some((function(e,t){var n=e.id;return h<n?(r.insert(l,t),!0):t.next?void 0:(r.insert(l),!0)})),void n.remove(s)}if(u===i)return!0;o[u]=!0}))}var Fe=t.List,Ue=t.walk;function Ye(e){var t=0;return e.each((function(e){t+=e.id.length+1})),t-1}function He(e){for(var t=0,n=0;n<e.length;n++)t+=e[n].length;return t+e.length-1}function Ve(e,t,n){var r=null!==this.block&&this.block.avoidRulesMerge,a=e.prelude.children,i=e.block,o=Object.create(null),s=!0,l=!0;n.prevUntil(t.prev,(function(c,u){var h=c.block,d=c.type;if("Rule"!==d){var p=me.unsafeToSkipNode.call(a,c);return!p&&"Atrule"===d&&h&&Ue(h,{visit:"Rule",enter:function(e){e.prelude.children.each((function(e){o[e.compareMarker]=!0}))}}),p}var m=c.prelude.children;if(e.pseudoSignature!==c.pseudoSignature)return!0;if(!(l=!m.some((function(e){return e.compareMarker in o})))&&!s)return!0;if(s&&me.isEqualSelectors(m,a))return h.children.appendList(i.children),n.remove(t),!0;var f=me.compareDeclarations(i.children,h.children);if(f.eq.length){if(!f.ne1.length&&!f.ne2.length)return l&&(me.addSelectors(a,m),n.remove(u)),!0;if(!r)if(f.ne1.length&&!f.ne2.length){var g=Ye(a),b=He(f.eq);s&&g<b&&(me.addSelectors(m,a),i.children=(new Fe).fromArray(f.ne1))}else if(!f.ne1.length&&f.ne2.length){g=Ye(m),b=He(f.eq);l&&g<b&&(me.addSelectors(a,m),h.children=(new Fe).fromArray(f.ne2))}else{var y={type:"SelectorList",loc:null,children:me.addSelectors(m.copy(),a)},v=Ye(y.children)+2;if((b=He(f.eq))>=v){var k=n.createItem({type:"Rule",loc:null,prelude:y,block:{type:"Block",loc:null,children:(new Fe).fromArray(f.eq)},pseudoSignature:e.pseudoSignature});return i.children=(new Fe).fromArray(f.ne1),h.children=(new Fe).fromArray(f.ne2overrided),s?n.insert(k,u):n.insert(k,t),!0}}}s&&(s=!m.some((function(e){return a.some((function(t){return t.compareMarker===e.compareMarker}))}))),m.each((function(e){o[e.compareMarker]=!0}))}))}var Ke=function(e,t){var n=re(e,t);t.logger("prepare",e),he(e,t),t.logger("mergeAtrule",e),function(e){fe(e,{visit:"Rule",enter:ge})}(e),t.logger("initialMergeRuleset",e),function(e){ye(e,{visit:"Rule",reverse:!0,enter:ve})}(e),t.logger("disjoinRuleset",e),Le(e,n),t.logger("restructShorthand",e),function(e){var t={},n=Object.create(null);Oe(e,{visit:"Rule",reverse:!0,enter:function(e,r,a){var i,o,s=this.block||this.stylesheet,l=(e.pseudoSignature||"")+"|"+e.prelude.children.first().id;t.hasOwnProperty(s.id)?i=t[s.id]:(i={},t[s.id]=i),i.hasOwnProperty(l)?o=i[l]:(o={},i[l]=o),_e.call(this,e,r,a,o,n)}})}(e),t.logger("restructBlock",e),function(e){qe(e,{visit:"Rule",enter:We})}(e),t.logger("mergeRuleset",e),function(e){Ue(e,{visit:"Rule",reverse:!0,enter:Ve})}(e),t.logger("restructRuleset",e)},Ge=t.List,$e=t.clone,Qe=t.walk;function Xe(e,t){var n,r=new Ge,a=!1;return e.nextUntil(e.head,(function(e,i,o){if("Comment"===e.type)return t&&"!"===e.value.charAt(0)?!(!a&&!n)||(o.remove(i),void(n=e)):void o.remove(i);"WhiteSpace"!==e.type&&(a=!0),r.insert(o.remove(i))})),{comment:n,stylesheet:{type:"StyleSheet",loc:null,children:r}}}function Ze(e,t,n,r){r.logger("Compress block #"+n,null,!0);var a=1;return"StyleSheet"===e.type&&(e.firstAtrulesAllowed=t,e.id=a++),Qe(e,{visit:"Atrule",enter:function(e){null!==e.block&&(e.block.id=a++)}}),r.logger("init",e),function(e,t){g(e,{leave:function(e,n,r){b.hasOwnProperty(e.type)&&b[e.type].call(this,e,n,r,t)}})}(e,r),r.logger("clean",e),function(e){K(e,{leave:function(e,t,n){G.hasOwnProperty(e.type)&&G[e.type].call(this,e,t,n)}})}(e),r.logger("replace",e),r.restructuring&&Ke(e,r),e}function Je(e){return"restructure"in e?e.restructure:!("restructuring"in e)||e.restructuring}var et,tt=function(e,t){e=e||{type:"StyleSheet",loc:null,children:new Ge};var n,r,a,o,s={logger:"function"==typeof(t=t||{}).logger?t.logger:function(){},restructuring:Je(t),forceMediaMerge:Boolean(t.forceMediaMerge),usage:!!t.usage&&i.buildIndex(t.usage)},l=function(e){var t="comments"in e?e.comments:"exclamation";return"boolean"==typeof t?t=!!t&&"exclamation":"exclamation"!==t&&"first-exclamation"!==t&&(t=!1),t}(t),c=!0,u=new Ge,h=1;t.clone&&(e=$e(e)),"StyleSheet"===e.type?(n=e.children,e.children=u):(o=e,n=(new Ge).appendData({type:"Rule",loc:null,prelude:{type:"SelectorList",loc:null,children:(new Ge).appendData({type:"Selector",loc:null,children:(new Ge).appendData({type:"TypeSelector",loc:null,name:"x"})})},block:o}));do{if(Ze((r=Xe(n,Boolean(l))).stylesheet,c,h++,s),a=r.stylesheet.children,r.comment&&(u.isEmpty()||u.insert(Ge.createItem({type:"Raw",value:"\n"})),u.insert(Ge.createItem(r.comment)),a.isEmpty()||u.insert(Ge.createItem({type:"Raw",value:"\n"}))),c&&!a.isEmpty()){var d=a.last();("Atrule"!==d.type||"import"!==d.name&&"charset"!==d.name)&&(c=!1)}"exclamation"!==l&&(l=!1),u.appendList(a)}while(!n.isEmpty());return{ast:e}},nt={version:"4.2.0"},rt=(et=Object.freeze({__proto__:null,version:"4.2.0",default:nt}))&&et.default||et,at=t.parse,it=t.generate;function ot(e,t,n,r){return t.debug&&console.error("## "+e+" done in %d ms\n",Date.now()-n),r}function st(e){var t,n;return"function"!=typeof(e=function(e){var t={};for(var n in e)t[n]=e[n];return t}(e)).logger&&e.debug&&(e.logger=(t=e.debug,function(e,r){var a=e;if(r&&(a="["+((Date.now()-n)/1e3).toFixed(3)+"s] "+a),t>1&&r){var i=it(r);2===t&&i.length>256&&(i=i.substr(0,256)+"..."),a+="\n  "+i+"\n"}console.error(a),n=Date.now()})),e}function lt(e,t,n){Array.isArray(n)||(n=[n]),n.forEach((function(n){n(e,t)}))}function ct(e,t,n){var r=(n=n||{}).filename||"<unknown>",a=ot("parsing",n,Date.now(),at(t,{context:e,filename:r,positions:Boolean(n.sourceMap)}));n.beforeCompress&&ot("beforeCompress",n,Date.now(),lt(a,n,n.beforeCompress));var i,o=ot("compress",n,Date.now(),tt(a,st(n)));return n.afterCompress&&ot("afterCompress",n,Date.now(),lt(o,n,n.afterCompress)),n.sourceMap?ot("generate(sourceMap: true)",n,Date.now(),((i=it(o.ast,{sourceMap:!0})).map._file=r,i.map.setSourceContent(r,t),i)):ot("generate",n,Date.now(),{css:it(o.ast),map:null})}var ut={version:rt.version,minify:function(e,t){return ct("stylesheet",e,t)},minifyBlock:function(e,t){return ct("declarationList",e,t)},syntax:Object.assign({compress:tt},t)},ht=ut.version,dt=ut.minify,pt=ut.minifyBlock,mt=ut.syntax;e.default=ut,e.minify=dt,e.minifyBlock=pt,e.syntax=mt,e.version=ht,Object.defineProperty(e,"__esModule",{value:!0})}));