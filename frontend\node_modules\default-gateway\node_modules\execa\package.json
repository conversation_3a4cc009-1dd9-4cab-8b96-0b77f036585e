{"name": "execa", "version": "5.1.1", "description": "Process execution for humans", "license": "MIT", "repository": "sindresorhus/execa", "funding": "https://github.com/sindresorhus/execa?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd"}, "files": ["index.js", "index.d.ts", "lib"], "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "devDependencies": {"@types/node": "^14.14.10", "ava": "^2.4.0", "get-node": "^11.0.1", "is-running": "^2.1.0", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempfile": "^3.0.0", "tsd": "^0.13.1", "xo": "^0.35.0"}, "nyc": {"reporter": ["text", "lcov"], "exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}}