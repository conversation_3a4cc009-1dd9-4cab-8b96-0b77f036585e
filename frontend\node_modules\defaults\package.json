{"name": "defaults", "version": "1.0.4", "description": "merge single level defaults over a config object", "main": "index.js", "funding": "https://github.com/sponsors/sindresorhus", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/sindresorhus/node-defaults.git"}, "keywords": ["config", "defaults", "options", "object", "merge", "assign", "properties", "deep"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "readmeFilename": "README.md", "dependencies": {"clone": "^1.0.2"}, "devDependencies": {"tap": "^2.0.0"}}