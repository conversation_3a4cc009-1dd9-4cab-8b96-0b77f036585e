(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('element/locale/ku', ['module', 'exports'], factory);
  } else if (typeof exports !== "undefined") {
    factory(module, exports);
  } else {
    var mod = {
      exports: {}
    };
    factory(mod, mod.exports);
    global.ELEMENT.lang = global.ELEMENT.lang || {}; 
    global.ELEMENT.lang.ku = mod.exports;
  }
})(this, function (module, exports) {
  'use strict';

  exports.__esModule = true;
  exports.default = {
    el: {
      colorpicker: {
        confirm: 'Temam',
        clear: 'Paqij bike'
      },
      datepicker: {
        now: 'Niha',
        today: 'Îro',
        cancel: 'Betal bike',
        clear: 'Paqij bike',
        confirm: 'Temam',
        selectDate: 'Dîrokê bibijêre',
        selectTime: 'Demê bibijêre',
        startDate: 'Dîroka Destpêkê',
        startTime: '<PERSON><PERSON>pêk<PERSON>',
        endDate: '<PERSON>îroka Dawî',
        endTime: 'Dema Dawî',
        prevYear: '<PERSON>a <PERSON>',
        nextYear: '<PERSON>a <PERSON>',
        prevMonth: '<PERSON><PERSON>',
        nextMonth: '<PERSON>ha <PERSON>ş',
        year: 'Sal',
        month1: 'Rêbendan',
        month2: 'Reşemeh',
        month3: 'Adar',
        month4: 'Avrêl',
        month5: 'Gulan',
        month6: 'Pûşber',
        month7: 'Tîrmeh',
        month8: 'Gilavêj',
        month9: 'Rezber',
        month10: 'Kewçêr',
        month11: 'Sarmawaz',
        month12: 'Berfanbar',
        // week: 'week',
        weeks: {
          sun: 'Yek',
          mon: 'Duş',
          tue: 'Sêş',
          wed: 'Çar',
          thu: 'Pên',
          fri: 'În',
          sat: 'Şem'
        },
        months: {
          jan: 'Rêb',
          feb: 'Reş',
          mar: 'Ada',
          apr: 'Avr',
          may: 'Gul',
          jun: 'Pûş',
          jul: 'Tîr',
          aug: 'Gil',
          sep: 'Rez',
          oct: 'Kew',
          nov: 'Sar',
          dec: 'Ber'
        }
      },
      select: {
        loading: 'Bardibe',
        noMatch: 'Li hembere ve agahî tune',
        noData: 'Agahî tune',
        placeholder: 'Bibijêre'
      },
      cascader: {
        noMatch: 'Li hembere ve agahî tune',
        loading: 'Bardibe',
        placeholder: 'Bibijêre',
        noData: 'Agahî tune'
      },
      pagination: {
        goto: 'Biçe',
        pagesize: '/rupel',
        total: 'Tevahî {total}',
        pageClassifier: ''
      },
      messagebox: {
        title: 'Peyam',
        confirm: 'Temam',
        cancel: 'Betal bike',
        error: 'Beyana çewt'
      },
      upload: {
        deleteTip: 'ji bo rake pêl "delete" bike',
        delete: 'Rake',
        preview: 'Pêşdîtin',
        continue: 'Berdewam'
      },
      table: {
        emptyText: 'Agahî tune',
        confirmFilter: 'Piştrast bike',
        resetFilter: 'Jê bibe',
        clearFilter: 'Hemû',
        sumText: 'Kom'
      },
      tree: {
        emptyText: 'Agahî tune'
      },
      transfer: {
        noMatch: 'Li hembere ve agahî tune',
        noData: 'Agahî tune',
        titles: ['Lîste 1', 'Lîste 2'],
        filterPlaceholder: 'Binivîse',
        noCheckedFormat: '{total} lib',
        hasCheckedFormat: '{checked}/{total} bijartin'
      },
      image: {
        error: 'FAILED' // to be translated
      },
      pageHeader: {
        title: 'Back' // to be translated
      },
      popconfirm: {
        confirmButtonText: 'Yes', // to be translated
        cancelButtonText: 'No' // to be translated
      },
      empty: {
        description: 'Agahî tune'
      }
    }
  };
  module.exports = exports['default'];
});