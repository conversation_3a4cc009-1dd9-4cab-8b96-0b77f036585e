@import 'mixins/mixins';
@import 'common/var';

@include b(result) {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  box-sizing: border-box;
  padding: $--result-padding;

  @include e(icon) {
    svg {
      width: $--result-icon-font-size;
      height: $--result-icon-font-size;
    }
  }

  @include e(title) {
    margin-top: $--result-title-margin-top;

    p {
      margin: 0;
      font-size: $--result-title-font-size;
      color: $--color-text-primary;
      line-height: 1.3;
    }
  }

  @include e(subtitle) {
    margin-top: $--result-subtitle-margin-top;

    p {
      margin: 0;
      font-size: $--font-size-base;
      color: $--color-text-regular;
      line-height: 1.3;
    }
  }

  @include e(extra) {
    margin-top: $--result-extra-margin-top;
  }

  .icon-success {
    fill: $--result-success-color;
  }

  .icon-error {
    fill: $--result-danger-color;
  }

  .icon-info {
    fill: $--result-info-color;
  }

  .icon-warning {
    fill: $--result-warning-color;
  }
}
