@import "mixins/mixins";
@import "common/var";

@include b(statistic) {
  $statistic-justify: center;
  $statistic-align: center;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: $--color-black;
  font-variant: tabular-nums;
  list-style: none;
  font-feature-settings: "tnum";
  text-align: center;
  .head {
    margin-bottom: 4px;
    color: $--color-text-regular;
    font-size: $--font-size-small;
  }

  .con {
    font-family: Sans-serif;
    display: flex;
    justify-content: $statistic-justify;
    align-items: $statistic-align;
    color: $--color-text-primary;
    .number {
      font-size: $--font-size-extra-large;
      padding: 0 4px;
    }
    span {
      display: inline-block;
      margin: 0;
      line-height: 100%;
    }
  }
}
