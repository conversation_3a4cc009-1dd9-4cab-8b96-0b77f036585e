@import "mixins/mixins";
@import "mixins/utils";
@import "common/var";
@import "input";
@import "button";
@import "checkbox";
@import "checkbox-group";

@include b(transfer) {
  font-size: $--font-size-base;

  @include e(buttons) {
    display: inline-block;
    vertical-align: middle;
    padding: 0 30px;
  }

  @include e(button) {
    display: block;
    margin: 0 auto;
    padding: 10px;
    border-radius: 50%;
    color: $--color-white;
    background-color: $--color-primary;
    font-size: 0;

    @include when(with-texts) {
      border-radius: $--border-radius-base;
    }

    @include when(disabled) {
      border: $--border-base;
      background-color: $--background-color-base;
      color: $--color-text-placeholder;

      &:hover {
        border: $--border-base;
        background-color: $--background-color-base;
        color: $--color-text-placeholder;
      }
    }

    &:first-child {
      margin-bottom: 10px;
    }

    &:nth-child(2) {
      margin: 0;
    }

    i, span {
      font-size: 14px;
    }

    & [class*="el-icon-"] + span {
      margin-left: 0;
    }
  }
}

@include b(transfer-panel) {
  border: 1px solid $--transfer-border-color;
  border-radius: $--transfer-border-radius;
  overflow: hidden;
  background: $--color-white;
  display: inline-block;
  vertical-align: middle;
  width: $--transfer-panel-width;
  max-height: 100%;
  box-sizing: border-box;
  position: relative;

  @include e(body) {
    height: $--transfer-panel-body-height;

    @include when(with-footer) {
      padding-bottom: $--transfer-panel-footer-height;
    }
  }

  @include e(list) {
    margin: 0;
    padding: 6px 0;
    list-style: none;
    height: $--transfer-panel-body-height;
    overflow: auto;
    box-sizing: border-box;

    @include when(filterable) {
      height: #{$--transfer-panel-body-height - $--transfer-filter-height - 20px};
      padding-top: 0;
    }
  }

  @include e(item) {
    height: $--transfer-item-height;
    line-height: $--transfer-item-height;
    padding-left: 15px;
    display: block !important;

    & + .el-transfer-panel__item {
      margin-left: 0;
    }

    &.el-checkbox {
      color: $--color-text-regular;
    }

    &:hover {
      color: $--color-primary;
    }

    &.el-checkbox .el-checkbox__label {
      width: 100%;
      @include utils-ellipsis;
      display: block;
      box-sizing: border-box;
      padding-left: 24px;
      line-height: $--transfer-item-height;
    }

    .el-checkbox__input {
      position: absolute;
      top: 8px;
    }
  }

  @include e(filter) {
    text-align: center;
    margin: 15px;
    box-sizing: border-box;
    display: block;
    width: auto;

    .el-input__inner {
      height: $--transfer-filter-height;
      width: 100%;
      font-size: 12px;
      display: inline-block;
      box-sizing: border-box;
      border-radius: #{$--transfer-filter-height / 2};
      padding-right: 10px;
      padding-left: 30px;
    }

    .el-input__icon {
      margin-left: 5px;
    }

    .el-icon-circle-close {
      cursor: pointer;
    }
  }

  .el-transfer-panel__header {
    height: $--transfer-panel-header-height;
    line-height: $--transfer-panel-header-height;
    background: $--transfer-panel-header-background-color;
    margin: 0;
    padding-left: 15px;
    border-bottom: 1px solid $--transfer-border-color;
    box-sizing: border-box;
    color: $--color-black;

    .el-checkbox {
      display: block;
      line-height: 40px;

      .el-checkbox__label {
        font-size: 16px;
        color: $--color-text-primary;
        font-weight: normal;

        span {
          position: absolute;
          right: 15px;
          color: $--color-text-secondary;
          font-size: 12px;
          font-weight: normal;
        }
      }
    }
  }

  .el-transfer-panel__footer {
    height: $--transfer-panel-footer-height;
    background: $--color-white;
    margin: 0;
    padding: 0;
    border-top: 1px solid $--transfer-border-color;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: $--index-normal;
    @include utils-vertical-center;

    .el-checkbox {
      padding-left: 20px;
      color: $--color-text-regular;
    }
  }

  .el-transfer-panel__empty {
    margin: 0;
    height: $--transfer-item-height;
    line-height: $--transfer-item-height;
    padding: 6px 15px 0;
    color: $--color-text-secondary;
    text-align: center;
  }

  .el-checkbox__label {
    padding-left: 8px;
  }

  .el-checkbox__inner {
    height: 14px;
    width: 14px;
    border-radius: 3px;
    &::after {
      height: 6px;
      width: 3px;
      left: 4px;
    }
  }
}
