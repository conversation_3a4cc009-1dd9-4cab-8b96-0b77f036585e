export default {
  el: {
    colorpicker: {
      confirm: 'OK',
      clear: '<PERSON><PERSON>'
    },
    datepicker: {
      now: '<PERSON>z<PERSON>',
      today: 'He<PERSON>',
      cancel: 'Abbrechen',
      clear: '<PERSON><PERSON>',
      confirm: 'OK',
      selectDate: '<PERSON><PERSON> wählen',
      selectTime: '<PERSON>rz<PERSON> wählen',
      startDate: 'Startdatum',
      startTime: 'Startzeit',
      endDate: 'Enddatum',
      endTime: 'Endzeit',
      prevYear: 'Letztes Jahr',
      nextYear: 'Nächt<PERSON> Jahr',
      prevMonth: 'Letzter Monat',
      nextMonth: 'N<PERSON>chster Monat',
      day: 'Tag',
      week: 'Woche',
      month: 'Monat',
      year: '',
      month1: 'Januar',
      month2: 'Februar',
      month3: 'M<PERSON><PERSON>',
      month4: 'April',
      month5: 'Mai',
      month6: 'Juni',
      month7: 'Juli',
      month8: 'August',
      month9: 'September',
      month10: 'Oktober',
      month11: 'November',
      month12: 'Dezember',
      weeks: {
        sun: 'So',
        mon: '<PERSON>',
        tue: 'Di',
        wed: 'Mi',
        thu: 'Do',
        fri: 'Fr',
        sat: 'Sa'
      },
      months: {
        jan: 'Jan',
        feb: 'Feb',
        mar: '<PERSON>är',
        apr: 'Apr',
        may: 'Mai',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Aug',
        sep: 'Sep',
        oct: 'Okt',
        nov: 'Nov',
        dec: 'Dez'
      }
    },
    select: {
      loading: 'Lädt.',
      noMatch: 'Nichts gefunden.',
      noData: 'Keine Daten',
      placeholder: 'Daten wählen'
    },
    cascader: {
      noMatch: 'Nichts gefunden.',
      loading: 'Lädt.',
      placeholder: 'Daten wählen',
      noData: 'Keine Daten'
    },
    pagination: {
      goto: 'Gehe zu',
      pagesize: ' pro Seite',
      total: 'Gesamt {total}',
      pageClassifier: ''
    },
    messagebox: {
      confirm: 'OK',
      cancel: 'Abbrechen',
      error: 'Fehler'
    },
    upload: {
      deleteTip: 'Klicke löschen zum entfernen',
      delete: 'Löschen',
      preview: 'Vorschau',
      continue: 'Fortsetzen'
    },
    table: {
      emptyText: 'Keine Daten',
      confirmFilter: 'Anwenden',
      resetFilter: 'Zurücksetzen',
      clearFilter: 'Alles ',
      sumText: 'Summe'
    },
    tree: {
      emptyText: 'Keine Einträge'
    },
    transfer: {
      noMatch: 'Nichts gefunden.',
      noData: 'Keine Einträge',
      titles: ['Liste 1', 'Liste 2'],
      filterPlaceholder: 'Einträge filtern',
      noCheckedFormat: '{total} Einträge',
      hasCheckedFormat: '{checked}/{total} ausgewählt'
    },
    image: {
      error: 'FAILED' // to be translated
    },
    pageHeader: {
      title: 'Back' // to be translated
    },
    popconfirm: {
      confirmButtonText: 'Yes', // to be translated
      cancelButtonText: 'No' // to be translated
    },
    empty: {
      description: 'Keine Daten'
    }
  }
};
