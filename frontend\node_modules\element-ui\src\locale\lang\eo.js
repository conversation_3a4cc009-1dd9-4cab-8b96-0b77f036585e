export default {
  el: {
    colorpicker: {
      confirm: '<PERSON>',
      clear: '<PERSON><PERSON><PERSON><PERSON>'
    },
    datepicker: {
      now: 'Nun',
      today: '<PERSON><PERSON><PERSON>',
      cancel: '<PERSON><PERSON><PERSON>',
      clear: '<PERSON><PERSON><PERSON><PERSON>',
      confirm: '<PERSON>',
      selectDate: '<PERSON><PERSON><PERSON> daton',
      selectTime: '<PERSON><PERSON><PERSON> horon',
      startDate: '<PERSON><PERSON><PERSON>',
      startTime: 'Komenca Horo',
      endDate: '<PERSON><PERSON> Dato',
      endTime: 'Fina Horo',
      prevYear: 'Antaŭa Jaro',
      nextYear: 'Sekva J<PERSON>',
      prevMonth: 'Anta<PERSON><PERSON> Mona<PERSON>',
      nextMonth: '<PERSON>k<PERSON>',
      year: 'J<PERSON>',
      month1: 'Januaro',
      month2: 'Februaro',
      month3: 'Marto',
      month4: 'Aprilo',
      month5: 'Majo',
      month6: 'Junio',
      month7: 'Julio',
      month8: 'Aŭgusto',
      month9: 'Septembro',
      month10: 'Oktobro',
      month11: 'Novembro',
      month12: 'Decembro',
      week: '<PERSON><PERSON><PERSON><PERSON>',
      weeks: {
        sun: 'Dim',
        mon: 'Lun',
        tue: 'Mar',
        wed: 'Mer',
        thu: 'Ĵaŭ',
        fri: 'Ven',
        sat: 'Sab'
      },
      months: {
        jan: 'Jan',
        feb: 'Feb',
        mar: 'Mar',
        apr: 'Apr',
        may: 'Maj',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Aŭg',
        sep: 'Sep',
        oct: 'Okt',
        nov: 'Nov',
        dec: 'Dec'
      }
    },
    select: {
      loading: 'Ŝarĝante',
      noMatch: 'Neniuj kongruaj datumoj',
      noData: 'Neniuj datumoj',
      placeholder: 'Bonvolu elekti'
    },
    cascader: {
      noMatch: 'Neniuj kongruaj datumoj',
      loading: 'Ŝarĝante',
      placeholder: 'Bonvolu elekti',
      noData: 'Neniuj datumoj'
    },
    pagination: {
      goto: 'Iru al',
      pagesize: '/ paĝo',
      total: 'Entute {total}',
      pageClassifier: ''
    },
    messagebox: {
      title: 'Mesaĝo',
      confirm: 'Bone',
      cancel: 'Nuligi',
      error: 'Nevalida Enigo!'
    },
    upload: {
      deleteTip: 'Premu "Delete" por forigi',
      delete: 'Forigi',
      preview: 'Antaŭrigardi',
      continue: 'Daŭrigi'
    },
    table: {
      emptyText: 'Neniuj datumoj',
      confirmFilter: 'Konfirmi',
      resetFilter: 'Restarigi',
      clearFilter: 'Ĉiuj',
      sumText: 'Sumo'
    },
    tree: {
      emptyText: 'Neniuj datumoj'
    },
    transfer: {
      noMatch: 'Neniuj kongruaj datumoj',
      noData: 'Neniuj datumoj',
      titles: ['Listo 1', 'Listo 2'],
      filterPlaceholder: 'Enigu ŝlosilvorton',
      noCheckedFormat: '{total} elementoj',
      hasCheckedFormat: '{checked}/{total} elektitaj'
    },
    image: {
      error: 'MALSUKCESIS'
    },
    pageHeader: {
      title: 'Reen'
    },
    popconfirm: {
      confirmButtonText: 'Yes', // to be translated
      cancelButtonText: 'No' // to be translated
    },
    empty: {
      description: 'Neniuj datumoj'
    }
  }
};
