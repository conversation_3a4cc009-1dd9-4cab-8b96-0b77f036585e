export default {
  el: {
    colorpicker: {
      confirm: '확인',
      clear: '초기화'
    },
    datepicker: {
      now: '지금',
      today: '오늘',
      cancel: '취소',
      clear: '초기화',
      confirm: '확인',
      selectDate: '날짜 선택',
      selectTime: '시간 선택',
      startDate: '시작 날짜',
      startTime: '시작 시간',
      endDate: '종료 날짜',
      endTime: '종료 시간',
      prevYear: '지난해',
      nextYear: '다음해',
      prevMonth: '지난달',
      nextMonth: '다음달',
      year: '년',
      month1: '1월',
      month2: '2월',
      month3: '3월',
      month4: '4월',
      month5: '5월',
      month6: '6월',
      month7: '7월',
      month8: '8월',
      month9: '9월',
      month10: '10월',
      month11: '11월',
      month12: '12월',
      // week: 'week',
      weeks: {
        sun: '일',
        mon: '월',
        tue: '화',
        wed: '수',
        thu: '목',
        fri: '금',
        sat: '토'
      },
      months: {
        jan: '1월',
        feb: '2월',
        mar: '3월',
        apr: '4월',
        may: '5월',
        jun: '6월',
        jul: '7월',
        aug: '8월',
        sep: '9월',
        oct: '10월',
        nov: '11월',
        dec: '12월'
      }
    },
    select: {
      loading: '불러오는 중',
      noMatch: '맞는 데이터가 없습니다',
      noData: '데이터 없음',
      placeholder: '선택'
    },
    cascader: {
      noMatch: '맞는 데이터가 없습니다',
      loading: '불러오는 중',
      placeholder: '선택',
      noData: '데이터 없음'
    },
    pagination: {
      goto: '이동',
      pagesize: '/page',
      total: '총 {total}',
      pageClassifier: ''
    },
    messagebox: {
      title: '메시지',
      confirm: '확인',
      cancel: '취소',
      error: '올바르지 않은 입력'
    },
    upload: {
      deleteTip: '클릭시 삭제됩니다',
      delete: '삭제',
      preview: '미리보기',
      continue: '계속하기'
    },
    table: {
      emptyText: '데이터 없음',
      confirmFilter: '확인',
      resetFilter: '초기화',
      clearFilter: '전체',
      sumText: '합'
    },
    tree: {
      emptyText: '데이터 없음'
    },
    transfer: {
      noMatch: '맞는 데이터가 없습니다',
      noData: '데이터 없음',
      titles: ['리스트 1', '리스트 2'],
      filterPlaceholder: ' 입력하세요',
      noCheckedFormat: '{total} 항목',
      hasCheckedFormat: '{checked}/{total} 선택됨'
    },
    image: {
      error: 'FAILED' // to be translated
    },
    pageHeader: {
      title: 'Back' // to be translated
    },
    popconfirm: {
      confirmButtonText: 'Yes', // to be translated
      cancelButtonText: 'No' // to be translated
    },
    empty: {
      description: '데이터 없음'
    }
  }
};
