export default {
  el: {
    colorpicker: {
      confirm: 'Sah',
      clear: '<PERSON><PERSON>'
    },
    datepicker: {
      now: '<PERSON><PERSON><PERSON>',
      today: '<PERSON> ini',
      cancel: '<PERSON><PERSON>',
      clear: '<PERSON><PERSON>',
      confirm: 'Sah',
      selectDate: '<PERSON><PERSON><PERSON>',
      selectTime: '<PERSON><PERSON><PERSON>',
      startDate: '<PERSON><PERSON><PERSON>',
      startTime: '<PERSON><PERSON>',
      endDate: '<PERSON>rik<PERSON>',
      endTime: 'Masa Tam<PERSON>',
      prevYear: '<PERSON><PERSON>pas',
      nextYear: '<PERSON><PERSON> Depan',
      prevMonth: '<PERSON><PERSON><PERSON>',
      nextMonth: '<PERSON>ula<PERSON> Depan',
      year: '<PERSON>hun',
      month1: 'Januari',
      month2: 'Febuari',
      month3: 'Mac',
      month4: 'April',
      month5: 'Mei',
      month6: 'Jun',
      month7: 'Julai',
      month8: 'Ogos',
      month9: 'September',
      month10: 'Oktober',
      month11: 'November',
      month12: 'Disember',
      weeks: {
        sun: 'Ahad',
        mon: 'Isnin',
        tue: '<PERSON><PERSON><PERSON>',
        wed: '<PERSON><PERSON>',
        thu: '<PERSON><PERSON><PERSON>',
        fri: '<PERSON><PERSON><PERSON>',
        sat: 'Sabtu'
      },
      months: {
        jan: '<PERSON><PERSON>ri',
        feb: '<PERSON><PERSON><PERSON>',
        mar: '<PERSON>',
        apr: 'April',
        may: 'Mei',
        jun: 'Jun',
        jul: 'Julai',
        aug: 'Ogos',
        sep: 'September',
        oct: 'Oktober',
        nov: 'November',
        dec: 'Disember'
      }
    },
    select: {
      loading: 'Sedang dimuat turun',
      noMatch: 'Tiada maklumat yang sepadan',
      noData: 'Tiada maklumat',
      placeholder: 'Sila pilih'
    },
    cascader: {
      noMatch: 'Tiada maklumat yang sepadan',
      loading: 'Sedang dimuat turun',
      placeholder: 'Sila pilih',
      noData: 'Tiada maklumat'
    },
    pagination: {
      goto: 'Seterusnya',
      pagesize: 'x/Halaman',
      total: 'Jumlah {total} ',
      pageClassifier: 'Halaman'
    },
    messagebox: {
      title: 'Tip',
      confirm: 'Sah',
      cancel: 'Batal',
      error: 'Data yang diisi tidak sah!'
    },
    upload: {
      deleteTip: 'Tekan "Padam" untuk memadam',
      delete: 'Padam',
      preview: 'Pratonton gambar',
      continue: 'Meneruskan muat naik'
    },
    table: {
      emptyText: 'Tiada maklumat',
      confirmFilter: 'Tapis',
      resetFilter: 'Set Semula',
      clearFilter: 'Semua',
      sumText: 'Jumlah'
    },
    tree: {
      emptyText: 'Tiada maklumat'
    },
    transfer: {
      noMatch: 'Tiada maklumat yang sepadan',
      noData: 'Tiada maklumat',
      titles: ['Senarai 1', 'Senarai 2'],
      filterPlaceholder: 'Masukkan kandungan carian',
      noCheckedFormat: 'Jumlah {total} item',
      hasCheckedFormat: 'Telah memilih {checked}/{total} item'
    },
    image: {
      error: 'Muat turun gagal'
    },
    pageHeader: {
      title: 'Kembali'
    },
    popconfirm: {
      confirmButtonText: 'Sah',
      cancelButtonText: 'Batal'
    },
    empty: {
      description: 'Tiada maklumat'
    }
  }
};
