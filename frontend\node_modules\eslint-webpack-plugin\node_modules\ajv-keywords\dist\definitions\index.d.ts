import type { Vocabulary, ErrorNoParams } from "ajv";
import type { DefinitionOptions } from "./_types";
import { PatternRequiredError } from "./patternRequired";
import { SelectError } from "./select";
export default function ajvKeywords(opts?: DefinitionOptions): Vocabulary;
export declare type AjvKeywordsError = PatternRequiredError | SelectError | ErrorNoParams<"range" | "exclusiveRange" | "anyRequired" | "oneRequired" | "allRequired" | "deepProperties" | "deepRequired" | "dynamicDefaults" | "instanceof" | "prohibited" | "regexp" | "transform" | "uniqueItemProperties">;
