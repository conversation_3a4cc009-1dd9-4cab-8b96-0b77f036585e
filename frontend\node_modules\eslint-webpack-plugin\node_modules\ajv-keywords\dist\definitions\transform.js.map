{"version": 3, "file": "transform.js", "sourceRoot": "", "sources": ["../../src/definitions/transform.ts"], "names": [], "mappings": ";;AACA,sDAAkE;AAkBlE,MAAM,SAAS,GAAwC;IACrD,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE;IAC/B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE;IAC3B,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE;IAC9B,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE;IAC7B,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;IACrB,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE;IACnC,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE;IACnC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC;CACrD,CAAA;AAED,MAAM,MAAM,GAER,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,EAAC,SAAS,EAAC,CAAC,CAAA;AAEvC,SAAS,OAAO;IACd,OAAO;QACL,OAAO,EAAE,WAAW;QACpB,UAAU,EAAE,OAAO;QACnB,MAAM,EAAE,MAAM;QACd,IAAI,CAAC,GAAe;YAClB,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;YACjD,MAAM,EAAC,UAAU,EAAE,kBAAkB,EAAC,GAAG,EAAE,CAAA;YAC3C,MAAM,MAAM,GAAa,MAAM,CAAA;YAC/B,IAAI,CAAC,MAAM,CAAC,MAAM;gBAAE,OAAM;YAC1B,IAAI,GAAqB,CAAA;YACzB,IAAI,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;gBACjC,MAAM,MAAM,GAAG,cAAc,CAAC,YAAY,CAAC,CAAA;gBAC3C,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAA,mBAAS,EAAC,MAAM,CAAC,EAAC,CAAC,CAAA;aACpE;YACD,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,UAAU,IAAI,mBAAmB,UAAU,gBAAgB,EAAE,GAAG,EAAE;gBACxE,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;gBAC/C,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,UAAU,IAAI,kBAAkB,GAAG,EAAE,IAAI,CAAC,CAAA;YAC3D,CAAC,CAAC,CAAA;YAEF,SAAS,aAAa,CAAC,EAAY;gBACjC,IAAI,CAAC,EAAE,CAAC,MAAM;oBAAE,OAAO,IAAI,CAAA;gBAC3B,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,EAAY,CAAA;gBAC5B,IAAI,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;oBAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,EAAE,CAAC,CAAA;gBAChF,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE;oBAClC,GAAG,EAAE,SAAS,CAAC,CAAkB,CAAC;oBAClC,IAAI,EAAE,IAAA,WAAC,EAAA,+DAA+D,IAAA,qBAAW,EAAC,CAAC,CAAC,EAAE;iBACvF,CAAC,CAAA;gBACF,MAAM,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,CAAA;gBAC7B,OAAO,GAAG,IAAI,CAAC,KAAK,YAAY,CAAC,CAAC,CAAC,IAAA,WAAC,EAAA,GAAG,IAAI,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,IAAA,WAAC,EAAA,GAAG,IAAI,IAAI,GAAG,GAAG,CAAA;YACpF,CAAC;QACH,CAAC;QACD,UAAU,EAAE;YACV,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAC;SACtD;KACF,CAAA;AACH,CAAC;AAED,SAAS,cAAc,CAAC,YAA6B;IACnD,kCAAkC;IAClC,MAAM,GAAG,GAAoB,EAAC,IAAI,EAAE,EAAE,EAAC,CAAA;IAEvC,kDAAkD;IAClD,IAAI,CAAC,YAAY,CAAC,IAAI;QAAE,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAA;IAClF,KAAK,MAAM,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE;QACjC,IAAI,OAAO,CAAC,KAAK,QAAQ;YAAE,SAAQ;QACnC,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;QACtB,8CAA8C;QAC9C,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAA;SAC9F;QACD,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;KAChB;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,SAAS,SAAS,CAAC,CAAS;IAC1B,OAAO,CAAC,CAAC,WAAW,EAAE,CAAA;AACxB,CAAC;AAED,kBAAe,MAAM,CAAA;AACrB,MAAM,CAAC,OAAO,GAAG,MAAM,CAAA"}