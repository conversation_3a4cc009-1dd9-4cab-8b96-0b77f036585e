{"name": "jest-worker", "version": "28.1.3", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-worker"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "devDependencies": {"@tsd/typescript": "~4.7.4", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0", "get-stream": "^6.0.0", "jest-leak-detector": "^28.1.3", "tsd-lite": "^0.5.6", "worker-farm": "^1.6.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1"}