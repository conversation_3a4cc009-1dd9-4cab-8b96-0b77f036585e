{"name": "figures", "version": "2.0.0", "description": "Unicode symbols with Windows CMD fallbacks", "license": "MIT", "repository": "sindresorhus/figures", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "make": "./makefile.js"}, "files": ["index.js"], "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "char", "symbol", "symbols", "figure", "figures", "fallback"], "dependencies": {"escape-string-regexp": "^1.0.5"}, "devDependencies": {"ava": "*", "markdown-table": "^1.0.0", "require-uncached": "^1.0.2", "xo": "*"}, "xo": {"esnext": true}}