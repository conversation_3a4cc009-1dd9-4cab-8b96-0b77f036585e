{"version": 3, "file": "object.js", "sourceRoot": "", "sources": ["../src/object.ts"], "names": [], "mappings": ";;;AACA,mCAAmC;AACnC,yCAA6C;AAC7C,mCAAwC;AAExC;;GAEG;AACI,MAAM,cAAc,GAAa,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;IAClE,sCAAsC;IACtC,IAAI,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC1D,OAAO,eAAe,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC;KACnE;IAED,+EAA+E;IAC/E,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK,MAAM,EAAE;QAClD,OAAO,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;KAChD;IAED,6DAA6D;IAC7D,MAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACrE,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAClE,CAAC,CAAC;AAdW,QAAA,cAAc,kBAczB;AAEF;;GAEG;AACH,MAAM,iBAAiB,GAAa,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;IAC7D,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IAEhC,uDAAuD;IACvD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;SAC5B,MAAM,CAAC,UAAU,MAAM,EAAE,GAAG;QAC3B,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAE7B,mCAAmC;QACnC,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,MAAM,CAAC;QAExC,gCAAgC;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE,CAAC,CAAC;QAErD,yCAAyC;QACzC,IAAI,0BAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAC3B,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,CAAC;YACjC,OAAO,MAAM,CAAC;SACf;QAED,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,gBAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC;QAChE,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,EAAc,CAAC;SACjB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;IAEnB,sCAAsC;IACtC,IAAI,MAAM,KAAK,EAAE;QAAE,OAAO,IAAI,CAAC;IAE/B,OAAO,IAAI,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,CAAC;AACnC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,cAAc,GAAa,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;IACtD,OAAO,YAAY,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;AAC9C,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,YAAY,GAA6B;IAC7C,gBAAgB,EAAE,qBAAa;IAC/B,iBAAiB,EAAE,iBAAiB;IACpC,gBAAgB,EAAE,CAAC,KAAY,EAAE,KAAa,EAAE,IAAU,EAAE,EAAE;QAC5D,OAAO,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAC7C,CAAC;IACD,eAAe,EAAE,CAAC,IAAU,EAAE,EAAE;QAC9B,OAAO,YAAY,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC;IACvC,CAAC;IACD,iBAAiB,EAAE,CAAC,GAAW,EAAE,KAAa,EAAE,IAAU,EAAE,EAAE;QAC5D,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC;IAC/C,CAAC;IACD,iBAAiB,EAAE,CAAC,GAAW,EAAE,EAAE;QACjC,OAAO,cAAc,GAAG,GAAG,CAAC;IAC9B,CAAC;IACD,kBAAkB,EAAE,CAAC,IAAa,EAAE,EAAE;QACpC,OAAO,eAAe,IAAI,GAAG,CAAC;IAChC,CAAC;IACD,cAAc,EAAE,CAAC,GAAa,EAAE,KAAa,EAAE,IAAU,EAAE,EAAE;QAC3D,OAAO,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7C,CAAC;IACD,cAAc,EAAE,CAAC,GAAkB,EAAE,KAAa,EAAE,IAAU,EAAE,EAAE;QAChE,OAAO,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7C,CAAC;IACD,iBAAiB,EAAE,MAAM;IACzB,iBAAiB,EAAE,cAAc;IACjC,iBAAiB,EAAE,cAAc;CAClC,CAAC", "sourcesContent": ["import { Next, ToString } from \"./types\";\nimport { quote<PERSON>ey } from \"./quote\";\nimport { USED_METHOD_KEY } from \"./function\";\nimport { arrayToString } from \"./array\";\n\n/**\n * Transform an object into a string.\n */\nexport const objectToString: ToString = (value, space, next, key) => {\n  // Support buffer in all environments.\n  if (typeof Buffer === \"function\" && Buffer.isBuffer(value)) {\n    return `Buffer.from(${next(value.toString(\"base64\"))}, 'base64')`;\n  }\n\n  // Support `global` under test environments that don't print `[object global]`.\n  if (typeof global === \"object\" && value === global) {\n    return globalToString(value, space, next, key);\n  }\n\n  // Use the internal object string to select stringify method.\n  const toString = OBJECT_TYPES[Object.prototype.toString.call(value)];\n  return toString ? toString(value, space, next, key) : undefined;\n};\n\n/**\n * Stringify an object of keys and values.\n */\nconst rawObjectToString: ToString = (obj, indent, next, key) => {\n  const eol = indent ? \"\\n\" : \"\";\n  const space = indent ? \" \" : \"\";\n\n  // Iterate over object keys and concat string together.\n  const values = Object.keys(obj)\n    .reduce(function (values, key) {\n      const fn = obj[key];\n      const result = next(fn, key);\n\n      // Omit `undefined` object entries.\n      if (result === undefined) return values;\n\n      // String format the value data.\n      const value = result.split(\"\\n\").join(`\\n${indent}`);\n\n      // Skip `key` prefix for function parser.\n      if (USED_METHOD_KEY.has(fn)) {\n        values.push(`${indent}${value}`);\n        return values;\n      }\n\n      values.push(`${indent}${quoteKey(key, next)}:${space}${value}`);\n      return values;\n    }, [] as string[])\n    .join(`,${eol}`);\n\n  // Avoid new lines in an empty object.\n  if (values === \"\") return \"{}\";\n\n  return `{${eol}${values}${eol}}`;\n};\n\n/**\n * Stringify global variable access.\n */\nconst globalToString: ToString = (value, space, next) => {\n  return `Function(${next(\"return this\")})()`;\n};\n\n/**\n * Convert JavaScript objects into strings.\n */\nconst OBJECT_TYPES: Record<string, ToString> = {\n  \"[object Array]\": arrayToString,\n  \"[object Object]\": rawObjectToString,\n  \"[object Error]\": (error: Error, space: string, next: Next) => {\n    return `new Error(${next(error.message)})`;\n  },\n  \"[object Date]\": (date: Date) => {\n    return `new Date(${date.getTime()})`;\n  },\n  \"[object String]\": (str: string, space: string, next: Next) => {\n    return `new String(${next(str.toString())})`;\n  },\n  \"[object Number]\": (num: number) => {\n    return `new Number(${num})`;\n  },\n  \"[object Boolean]\": (bool: boolean) => {\n    return `new Boolean(${bool})`;\n  },\n  \"[object Set]\": (set: Set<any>, space: string, next: Next) => {\n    return `new Set(${next(Array.from(set))})`;\n  },\n  \"[object Map]\": (map: Map<any, any>, space: string, next: Next) => {\n    return `new Map(${next(Array.from(map))})`;\n  },\n  \"[object RegExp]\": String,\n  \"[object global]\": globalToString,\n  \"[object Window]\": globalToString,\n};\n"]}