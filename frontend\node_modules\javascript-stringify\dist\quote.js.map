{"version": 3, "file": "quote.js", "sourceRoot": "", "sources": ["../src/quote.ts"], "names": [], "mappings": ";;;AAEA;;;;;GAKG;AACH,MAAM,SAAS,GAAG,0HAA0H,CAAC;AAE7I;;GAEG;AACH,MAAM,UAAU,GAAG,IAAI,GAAG,CAAiB;IACzC,CAAC,IAAI,EAAE,KAAK,CAAC;IACb,CAAC,IAAI,EAAE,KAAK,CAAC;IACb,CAAC,IAAI,EAAE,KAAK,CAAC;IACb,CAAC,IAAI,EAAE,KAAK,CAAC;IACb,CAAC,IAAI,EAAE,KAAK,CAAC;IACb,CAAC,GAAG,EAAE,KAAK,CAAC;IACZ,CAAC,GAAG,EAAE,KAAK,CAAC;IACZ,CAAC,IAAI,EAAE,MAAM,CAAC;CACf,CAAC,CAAC;AAEH;;;;;GAKG;AACH,SAAS,UAAU,CAAC,IAAY;IAC9B,OAAO,CACL,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC;QACpB,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAC3D,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,WAAW,CAAC,GAAW;IACrC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,GAAG,CAAC;AACnD,CAAC;AAFD,kCAEC;AAED;;GAEG;AACH,MAAM,cAAc,GAAG,IAAI,GAAG,CAC5B,CACE,qEAAqE;IACrE,6DAA6D;IAC7D,8DAA8D;IAC9D,0EAA0E;IAC1E,mEAAmE;IACnE,8DAA8D,CAC/D,CAAC,KAAK,CAAC,GAAG,CAAC,CACb,CAAC;AAEF;;GAEG;AACU,QAAA,mBAAmB,GAAG,4BAA4B,CAAC;AAEhE;;GAEG;AACH,SAAgB,mBAAmB,CAAC,IAAiB;IACnD,OAAO,CACL,OAAO,IAAI,KAAK,QAAQ;QACxB,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC;QACzB,2BAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAC/B,CAAC;AACJ,CAAC;AAND,kDAMC;AAED;;GAEG;AACH,SAAgB,QAAQ,CAAC,GAAgB,EAAE,IAAU;IACnD,OAAO,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpD,CAAC;AAFD,4BAEC;AAED;;GAEG;AACH,SAAgB,aAAa,CAAC,IAAmB,EAAE,IAAU;IAC3D,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,IAAI,mBAAmB,CAAC,GAAG,CAAC,EAAE;YAC5B,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC;SACrB;aAAM;YACL,MAAM,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;SAC5B;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAZD,sCAYC", "sourcesContent": ["import { Next } from \"./types\";\n\n/**\n * Match all characters that need to be escaped in a string. Modified from\n * source to match single quotes instead of double.\n *\n * Source: https://github.com/douglascrockford/JSON-js/blob/master/json2.js\n */\nconst ESCAPABLE = /[\\\\\\'\\x00-\\x1f\\x7f-\\x9f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g;\n\n/**\n * Map of characters to escape characters.\n */\nconst META_CHARS = new Map<string, string>([\n  [\"\\b\", \"\\\\b\"],\n  [\"\\t\", \"\\\\t\"],\n  [\"\\n\", \"\\\\n\"],\n  [\"\\f\", \"\\\\f\"],\n  [\"\\r\", \"\\\\r\"],\n  [\"'\", \"\\\\'\"],\n  ['\"', '\\\\\"'],\n  [\"\\\\\", \"\\\\\\\\\"],\n]);\n\n/**\n * Escape any character into its literal JavaScript string.\n *\n * @param  {string} char\n * @return {string}\n */\nfunction escapeChar(char: string) {\n  return (\n    META_CHARS.get(char) ||\n    `\\\\u${`0000${char.charCodeAt(0).toString(16)}`.slice(-4)}`\n  );\n}\n\n/**\n * Quote a string.\n */\nexport function quoteString(str: string) {\n  return `'${str.replace(ESCAPABLE, escapeChar)}'`;\n}\n\n/**\n * JavaScript reserved keywords.\n */\nconst RESERVED_WORDS = new Set(\n  (\n    \"break else new var case finally return void catch for switch while \" +\n    \"continue function this with default if throw delete in try \" +\n    \"do instanceof typeof abstract enum int short boolean export \" +\n    \"interface static byte extends long super char final native synchronized \" +\n    \"class float package throws const goto private transient debugger \" +\n    \"implements protected volatile double import public let yield\"\n  ).split(\" \")\n);\n\n/**\n * Test for valid JavaScript identifier.\n */\nexport const IS_VALID_IDENTIFIER = /^[A-Za-z_$][A-Za-z0-9_$]*$/;\n\n/**\n * Check if a variable name is valid.\n */\nexport function isValidVariableName(name: PropertyKey): name is string {\n  return (\n    typeof name === \"string\" &&\n    !RESERVED_WORDS.has(name) &&\n    IS_VALID_IDENTIFIER.test(name)\n  );\n}\n\n/**\n * Quote JavaScript key access.\n */\nexport function quoteKey(key: PropertyKey, next: Next) {\n  return isValidVariableName(key) ? key : next(key);\n}\n\n/**\n * Serialize the path to a string.\n */\nexport function stringifyPath(path: PropertyKey[], next: Next) {\n  let result = \"\";\n\n  for (const key of path) {\n    if (isValidVariableName(key)) {\n      result += `.${key}`;\n    } else {\n      result += `[${next(key)}]`;\n    }\n  }\n\n  return result;\n}\n"]}