{"name": "merge-source-map", "version": "1.1.0", "description": "Merge old source map and new source map in multi-transform flow", "main": "index.js", "scripts": {"test": "nyc tape test/*.js", "lint": "eslint index.js 'test/**/*.js'", "version": "npm run lint && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/keik/merge-source-map.git"}, "author": "keik <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/keik/merge-source-map/issues"}, "keywords": ["sourcemap", "source-map"], "dependencies": {"source-map": "^0.6.1"}, "devDependencies": {"babel-core": "^6.25.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "coffee-script": "^1.12.6", "convert-source-map": "^1.5.0", "escodegen": "^1.8.1", "eslint": "^3.19.0", "esprima": "^3.1.3", "estraverse": "^4.2.0", "nyc": "^8.4.0", "tape": "^4.6.3"}, "files": []}