{"name": "resize-detector", "version": "0.3.0", "description": "Element resize detection, both modern way and cross browser.", "main": "dist/index.js", "module": "esm/index.js", "scripts": {"build": "rollup -c", "prepare": "npm run build"}, "keywords": ["element", "resize", "observer"], "author": "<PERSON><PERSON> (<EMAIL>)", "license": "MIT", "repository": "Justineo/resize-detector", "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "babel-eslint": "^8.2.6", "eslint": "^4.19.1", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^5.2.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "rollup": "^1.32.1", "rollup-plugin-postcss": "^2.9.0", "rollup-plugin-terser": "^5.3.1"}, "dependencies": {}}