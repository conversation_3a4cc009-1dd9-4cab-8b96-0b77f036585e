// Generated by CoffeeScript 1.12.7
(function() {
  var iframe_template, utils;

  utils = require('./utils');

  iframe_template = "<!DOCTYPE html>\n<html>\n<head>\n  <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\n  <script src=\"{{ sockjs_url }}\"></script>\n  <script>\n    document.domain = document.domain;\n    SockJS.bootstrap_iframe();\n  </script>\n</head>\n<body>\n  <h2>Don't panic!</h2>\n  <p>This is a SockJS hidden iframe. It's used for cross domain magic.</p>\n</body>\n</html>";

  exports.app = {
    iframe: function(req, res) {
      var content, context, k, quoted_md5;
      context = {
        '{{ sockjs_url }}': this.options.sockjs_url
      };
      content = iframe_template;
      for (k in context) {
        content = content.replace(k, context[k]);
      }
      quoted_md5 = '"' + utils.md5_hex(content) + '"';
      if ('if-none-match' in req.headers && req.headers['if-none-match'] === quoted_md5) {
        res.statusCode = 304;
        return '';
      }
      res.setHeader('Content-Type', 'text/html; charset=UTF-8');
      res.setHeader('ETag', quoted_md5);
      return content;
    }
  };

}).call(this);
