"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const uniqueItemProperties_1 = __importDefault(require("../definitions/uniqueItemProperties"));
const uniqueItemProperties = (ajv) => ajv.addKeyword((0, uniqueItemProperties_1.default)());
exports.default = uniqueItemProperties;
module.exports = uniqueItemProperties;
//# sourceMappingURL=uniqueItemProperties.js.map