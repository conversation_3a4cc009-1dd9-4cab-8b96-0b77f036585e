{"version": 3, "file": "index.esm.js", "sources": ["../../node_modules/.pnpm/tslib@2.6.2/node_modules/tslib/tslib.es6.js", "../../src/composables/api.ts", "../../src/composables/autoresize.ts", "../../src/utils.ts", "../../src/composables/loading.ts", "../../src/wc.ts", "../../src/ECharts.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Ref } from \"vue-demi\";\nimport { EChartsType } from \"../types\";\n\nconst METHOD_NAMES = [\n  \"getWidth\",\n  \"getHeight\",\n  \"getDom\",\n  \"getOption\",\n  \"resize\",\n  \"dispatchAction\",\n  \"convertToPixel\",\n  \"convertFromPixel\",\n  \"containPixel\",\n  \"getDataURL\",\n  \"getConnectedDataURL\",\n  \"appendData\",\n  \"clear\",\n  \"isDisposed\",\n  \"dispose\"\n] as const;\n\ntype MethodName = (typeof METHOD_NAMES)[number];\n\ntype PublicMethods = Pick<EChartsType, MethodName>;\n\nexport function usePublicAPI(\n  chart: Ref<EChartsType | undefined>\n): PublicMethods {\n  function makePublicMethod<T extends MethodName>(\n    name: T\n  ): (...args: Parameters<EChartsType[T]>) => ReturnType<EChartsType[T]> {\n    return (...args) => {\n      if (!chart.value) {\n        throw new Error(\"ECharts is not initialized yet.\");\n      }\n      return (chart.value[name] as any).apply(chart.value, args);\n    };\n  }\n\n  function makePublicMethods(): PublicMethods {\n    const methods = Object.create(null);\n    METHOD_NAMES.forEach(name => {\n      methods[name] = makePublicMethod(name);\n    });\n\n    return methods as PublicMethods;\n  }\n\n  return makePublicMethods();\n}\n", "import { watch, type Ref, type PropType } from \"vue-demi\";\nimport { throttle } from \"echarts/core\";\nimport {\n  addListener,\n  removeListener,\n  type ResizeCallback\n} from \"resize-detector\";\nimport { type EChartsType } from \"../types\";\n\ntype AutoresizeProp =\n  | boolean\n  | {\n      throttle?: number;\n      onResize?: () => void;\n    };\n\nexport function useAutoresize(\n  chart: Ref<EChartsType | undefined>,\n  autoresize: Ref<AutoresizeProp | undefined>,\n  root: Ref<HTMLElement | undefined>\n): void {\n  let resizeListener: ResizeCallback | null = null;\n\n  watch([root, chart, autoresize], ([root, chart, autoresize], _, cleanup) => {\n    if (root && chart && autoresize) {\n      const autoresizeOptions = autoresize === true ? {} : autoresize;\n      const { throttle: wait = 100, onResize } = autoresizeOptions;\n\n      const callback = () => {\n        chart.resize();\n        onResize?.();\n      };\n\n      resizeListener = wait ? throttle(callback, wait) : callback;\n      addListener(root, resizeListener);\n    }\n\n    cleanup(() => {\n      if (root && resizeListener) {\n        removeListener(root, resizeListener);\n      }\n    });\n  });\n}\n\nexport const autoresizeProps = {\n  autoresize: [Boolean, Object] as PropType<AutoresizeProp>\n};\n", "import { unref, isRef } from \"vue-demi\";\nimport type { Injection } from \"./types\";\n\ntype Attrs = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [key: string]: any;\n};\n\n// Copied from\n// https://github.com/vuejs/vue-next/blob/5a7a1b8293822219283d6e267496bec02234b0bc/packages/shared/src/index.ts#L40-L41\nconst onRE = /^on[^a-z]/;\nexport const isOn = (key: string): boolean => onRE.test(key);\n\nexport function omitOn(attrs: Attrs): Attrs {\n  const result: Attrs = {};\n  for (const key in attrs) {\n    if (!isOn(key)) {\n      result[key] = attrs[key];\n    }\n  }\n\n  return result;\n}\n\nexport function unwrapInjected<T, V>(\n  injection: Injection<T>,\n  defaultValue: V\n): T | V {\n  const value = isRef(injection) ? unref(injection) : injection;\n\n  if (value && typeof value === \"object\" && \"value\" in value) {\n    return value.value || defaultValue;\n  }\n\n  return value || defaultValue;\n}\n", "import { unwrapInjected } from \"../utils\";\nimport {\n  inject,\n  computed,\n  watchEffect,\n  type Ref,\n  type InjectionKey,\n  type PropType\n} from \"vue-demi\";\nimport type { EChartsType, LoadingOptions } from \"../types\";\n\nexport const LOADING_OPTIONS_KEY =\n  \"ecLoadingOptions\" as unknown as InjectionKey<\n    LoadingOptions | Ref<LoadingOptions>\n  >;\n\nexport function useLoading(\n  chart: Ref<EChartsType | undefined>,\n  loading: Ref<boolean>,\n  loadingOptions: Ref<LoadingOptions | undefined>\n): void {\n  const defaultLoadingOptions = inject(LOADING_OPTIONS_KEY, {});\n  const realLoadingOptions = computed(() => ({\n    ...unwrapInjected(defaultLoadingOptions, {}),\n    ...loadingOptions?.value\n  }));\n\n  watchEffect(() => {\n    const instance = chart.value;\n    if (!instance) {\n      return;\n    }\n\n    if (loading.value) {\n      instance.showLoading(realLoadingOptions.value);\n    } else {\n      instance.hideLoading();\n    }\n  });\n}\n\nexport const loadingProps = {\n  loading: Boolean,\n  loadingOptions: Object as PropType<LoadingOptions>\n};\n", "let registered: boolean | null = null;\n\nexport const TAG_NAME = \"x-vue-echarts\";\n\nexport interface EChartsElement extends HTMLElement {\n  __dispose: (() => void) | null;\n}\n\nexport function register(): boolean {\n  if (registered != null) {\n    return registered;\n  }\n\n  if (\n    typeof HTMLElement === \"undefined\" ||\n    typeof customElements === \"undefined\"\n  ) {\n    return (registered = false);\n  }\n\n  try {\n    // Class definitions cannot be transpiled to ES5\n    // so we are doing a little trick here to ensure\n    // we are using native classes. As we use this as\n    // a progressive enhancement, it will be fine even\n    // if the browser doesn't support native classes.\n    const reg = new Function(\n      \"tag\",\n      `class EChartsElement extends HTMLElement {\n  __dispose = null;\n\n  disconnectedCallback() {\n    if (this.__dispose) {\n      this.__dispose();\n      this.__dispose = null;\n    }\n  }\n}\n\nif (customElements.get(tag) == null) {\n  customElements.define(tag, EChartsElement);\n}\n`\n    );\n    reg(TAG_NAME);\n  } catch (e) {\n    return (registered = false);\n  }\n\n  return (registered = true);\n}\n", "/* eslint-disable vue/multi-word-component-names */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n  defineComponent,\n  shallowRef,\n  toRefs,\n  watch,\n  computed,\n  inject,\n  onMounted,\n  onBeforeUnmount,\n  h,\n  nextTick,\n  watchEffect,\n  getCurrentInstance,\n  Vue2,\n  type PropType,\n  type InjectionKey\n} from \"vue-demi\";\nimport { init as initChart } from \"echarts/core\";\nimport type {\n  EChartsType,\n  EventTarget,\n  Option,\n  Theme,\n  ThemeInjection,\n  InitOptions,\n  InitOptionsInjection,\n  UpdateOptions,\n  UpdateOptionsInjection,\n  Emits\n} from \"./types\";\nimport {\n  usePublicAPI,\n  useAutoresize,\n  autoresizeProps,\n  useLoading,\n  loadingProps\n} from \"./composables\";\nimport { isOn, omitOn, unwrapInjected } from \"./utils\";\nimport { register, TAG_NAME, type EChartsElement } from \"./wc\";\nimport \"./style.css\";\n\nconst __CSP__ = false;\nconst wcRegistered = __CSP__ ? false : register();\n\nif (Vue2) {\n  Vue2.config.ignoredElements.push(TAG_NAME);\n}\n\nexport const THEME_KEY = \"ecTheme\" as unknown as InjectionKey<ThemeInjection>;\nexport const INIT_OPTIONS_KEY =\n  \"ecInitOptions\" as unknown as InjectionKey<InitOptionsInjection>;\nexport const UPDATE_OPTIONS_KEY =\n  \"ecUpdateOptions\" as unknown as InjectionKey<UpdateOptionsInjection>;\nexport { LOADING_OPTIONS_KEY } from \"./composables\";\n\nconst NATIVE_EVENT_RE = /(^&?~?!?)native:/;\n\nexport default defineComponent({\n  name: \"echarts\",\n  props: {\n    option: Object as PropType<Option>,\n    theme: {\n      type: [Object, String] as PropType<Theme>\n    },\n    initOptions: Object as PropType<InitOptions>,\n    updateOptions: Object as PropType<UpdateOptions>,\n    group: String,\n    manualUpdate: Boolean,\n    ...autoresizeProps,\n    ...loadingProps\n  },\n  emits: {} as unknown as Emits,\n  inheritAttrs: false,\n  setup(props, { attrs }) {\n    const root = shallowRef<EChartsElement>();\n    const inner = shallowRef<HTMLElement>();\n    const chart = shallowRef<EChartsType>();\n    const manualOption = shallowRef<Option>();\n    const defaultTheme = inject(THEME_KEY, null);\n    const defaultInitOptions = inject(INIT_OPTIONS_KEY, null);\n    const defaultUpdateOptions = inject(UPDATE_OPTIONS_KEY, null);\n\n    const { autoresize, manualUpdate, loading, loadingOptions } = toRefs(props);\n\n    const realOption = computed(\n      () => manualOption.value || props.option || null\n    );\n    const realTheme = computed(\n      () => props.theme || unwrapInjected(defaultTheme, {})\n    );\n    const realInitOptions = computed(\n      () => props.initOptions || unwrapInjected(defaultInitOptions, {})\n    );\n    const realUpdateOptions = computed(\n      () => props.updateOptions || unwrapInjected(defaultUpdateOptions, {})\n    );\n    const nonEventAttrs = computed(() => omitOn(attrs));\n    const nativeListeners: Record<string, unknown> = {};\n\n    // @ts-expect-error listeners for Vue 2 compatibility\n    const listeners = getCurrentInstance().proxy.$listeners;\n    const realListeners: Record<string, any> = {};\n\n    if (!listeners) {\n      // This is for Vue 3.\n      // We are converting all `on<Event>` props to event listeners compatible with Vue 2\n      // and collect them into `realListeners` so that we can bind them to the chart instance\n      // later in the same way.\n      // For `onNative:<event>` props, we just strip the `Native:` part and collect them into\n      // `nativeListeners` so that we can bind them to the root element directly.\n      Object.keys(attrs)\n        .filter(key => isOn(key))\n        .forEach(key => {\n          // onClick    -> c + lick\n          // onZr:click -> z + r:click\n          let event = key.charAt(2).toLowerCase() + key.slice(3);\n\n          // Collect native DOM events\n          if (event.indexOf(\"native:\") === 0) {\n            // native:click -> onClick\n            const nativeKey = `on${event.charAt(7).toUpperCase()}${event.slice(\n              8\n            )}`;\n\n            nativeListeners[nativeKey] = attrs[key];\n            return;\n          }\n\n          // clickOnce    -> ~click\n          // zr:clickOnce -> ~zr:click\n          if (event.substring(event.length - 4) === \"Once\") {\n            event = `~${event.substring(0, event.length - 4)}`;\n          }\n\n          realListeners[event] = attrs[key];\n        });\n    } else {\n      // This is for Vue 2.\n      // We just need to distinguish normal events and `native:<event>` events and\n      // collect them into `realListeners` and `nativeListeners` respectively.\n      // For `native:<event>` events, we just strip the `native:` part and collect them\n      // into `nativeListeners` so that we can bind them to the root element directly.\n      // native:click   -> click\n      // ~native:click  -> ~click\n      // &~!native:click -> &~!click\n      Object.keys(listeners).forEach(key => {\n        if (NATIVE_EVENT_RE.test(key)) {\n          nativeListeners[key.replace(NATIVE_EVENT_RE, \"$1\")] = listeners[key];\n        } else {\n          realListeners[key] = listeners[key];\n        }\n      });\n    }\n\n    function init(option?: Option) {\n      if (!inner.value) {\n        return;\n      }\n\n      const instance = (chart.value = initChart(\n        inner.value,\n        realTheme.value,\n        realInitOptions.value\n      ));\n\n      if (props.group) {\n        instance.group = props.group;\n      }\n\n      Object.keys(realListeners).forEach(key => {\n        let handler = realListeners[key];\n\n        if (!handler) {\n          return;\n        }\n\n        let event = key.toLowerCase();\n        if (event.charAt(0) === \"~\") {\n          event = event.substring(1);\n          handler.__once__ = true;\n        }\n\n        let target: EventTarget = instance;\n        if (event.indexOf(\"zr:\") === 0) {\n          target = instance.getZr();\n          event = event.substring(3);\n        }\n\n        if (handler.__once__) {\n          delete handler.__once__;\n\n          const raw = handler;\n\n          handler = (...args: any[]) => {\n            raw(...args);\n            target.off(event, handler);\n          };\n        }\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore EChartsType[\"on\"] is not compatible with ZRenderType[\"on\"]\n        // but it's okay here\n        target.on(event, handler);\n      });\n\n      function resize() {\n        if (instance && !instance.isDisposed()) {\n          instance.resize();\n        }\n      }\n\n      function commit() {\n        const opt = option || realOption.value;\n        if (opt) {\n          instance.setOption(opt, realUpdateOptions.value);\n        }\n      }\n\n      if (autoresize.value) {\n        // Try to make chart fit to container in case container size\n        // is changed synchronously or in already queued microtasks\n        nextTick(() => {\n          resize();\n          commit();\n        });\n      } else {\n        commit();\n      }\n    }\n\n    function setOption(option: Option, updateOptions?: UpdateOptions) {\n      if (props.manualUpdate) {\n        manualOption.value = option;\n      }\n\n      if (!chart.value) {\n        init(option);\n      } else {\n        chart.value.setOption(option, updateOptions || {});\n      }\n    }\n\n    function cleanup() {\n      if (chart.value) {\n        chart.value.dispose();\n        chart.value = undefined;\n      }\n    }\n\n    let unwatchOption: (() => void) | null = null;\n    watch(\n      manualUpdate,\n      manualUpdate => {\n        if (typeof unwatchOption === \"function\") {\n          unwatchOption();\n          unwatchOption = null;\n        }\n\n        if (!manualUpdate) {\n          unwatchOption = watch(\n            () => props.option,\n            (option, oldOption) => {\n              if (!option) {\n                return;\n              }\n              if (!chart.value) {\n                init();\n              } else {\n                chart.value.setOption(option, {\n                  // mutating `option` will lead to `notMerge: false` and\n                  // replacing it with new reference will lead to `notMerge: true`\n                  notMerge: option !== oldOption,\n                  ...realUpdateOptions.value\n                });\n              }\n            },\n            { deep: true }\n          );\n        }\n      },\n      {\n        immediate: true\n      }\n    );\n\n    watch(\n      [realTheme, realInitOptions],\n      () => {\n        cleanup();\n        init();\n      },\n      {\n        deep: true\n      }\n    );\n\n    watchEffect(() => {\n      if (props.group && chart.value) {\n        chart.value.group = props.group;\n      }\n    });\n\n    const publicApi = usePublicAPI(chart);\n\n    useLoading(chart, loading, loadingOptions);\n\n    useAutoresize(chart, autoresize, inner);\n\n    onMounted(() => {\n      init();\n    });\n\n    onBeforeUnmount(() => {\n      if (wcRegistered && root.value) {\n        // For registered web component, we can leverage the\n        // `disconnectedCallback` to dispose the chart instance\n        // so that we can delay the cleanup after exsiting leaving\n        // transition.\n        root.value.__dispose = cleanup;\n      } else {\n        cleanup();\n      }\n    });\n\n    return {\n      chart,\n      root,\n      inner,\n      setOption,\n      nonEventAttrs,\n      nativeListeners,\n      ...publicApi\n    };\n  },\n  render() {\n    // Vue 3 and Vue 2 have different vnode props format:\n    // See https://v3-migration.vuejs.org/breaking-changes/render-function-api.html#vnode-props-format\n    const attrs = (\n      Vue2\n        ? { attrs: this.nonEventAttrs, on: this.nativeListeners }\n        : { ...this.nonEventAttrs, ...this.nativeListeners }\n    ) as any;\n    attrs.ref = \"root\";\n    attrs.class = attrs.class ? [\"echarts\"].concat(attrs.class) : \"echarts\";\n    return h(TAG_NAME, attrs, [\n      h(\"div\", { ref: \"inner\", class: \"vue-echarts-inner\" })\n    ]);\n  }\n});\n"], "names": ["init", "initChart"], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAiBA;AACO,IAAI,QAAQ,GAAG,WAAW;AACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;AACrD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7D,YAAY,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACzF,SAAS;AACT,QAAQ,OAAO,CAAC,CAAC;AACjB,MAAK;AACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC3C,EAAC;AAkRD;AACuB,OAAO,eAAe,KAAK,UAAU,GAAG,eAAe,GAAG,UAAU,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE;AACvH,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AAC/B,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG,iBAAiB,EAAE,CAAC,CAAC,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,UAAU,GAAG,UAAU,EAAE,CAAC,CAAC;AACrF;;AC1TA,IAAM,YAAY,GAAG;IACnB,UAAU;IACV,WAAW;IACX,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,gBAAgB;IAChB,gBAAgB;IAChB,kBAAkB;IAClB,cAAc;IACd,YAAY;IACZ,qBAAqB;IACrB,YAAY;IACZ,OAAO;IACP,YAAY;IACZ,SAAS;CACD,CAAC;AAML,SAAU,YAAY,CAC1B,KAAmC,EAAA;IAEnC,SAAS,gBAAgB,CACvB,IAAO,EAAA;QAEP,OAAO,YAAA;YAAC,IAAO,IAAA,GAAA,EAAA,CAAA;iBAAP,IAAO,EAAA,GAAA,CAAA,EAAP,EAAO,GAAA,SAAA,CAAA,MAAA,EAAP,EAAO,EAAA,EAAA;gBAAP,IAAO,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;AACb,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;AAChB,gBAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;AACpD,aAAA;AACD,YAAA,OAAQ,KAAK,CAAC,KAAK,CAAC,IAAI,CAAS,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC7D,SAAC,CAAC;KACH;AAED,IAAA,SAAS,iBAAiB,GAAA;QACxB,IAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpC,QAAA,YAAY,CAAC,OAAO,CAAC,UAAA,IAAI,EAAA;YACvB,OAAO,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACzC,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,OAAwB,CAAC;KACjC;IAED,OAAO,iBAAiB,EAAE,CAAC;AAC7B;;SClCgB,aAAa,CAC3B,KAAmC,EACnC,UAA2C,EAC3C,IAAkC,EAAA;IAElC,IAAI,cAAc,GAA0B,IAAI,CAAC;AAEjD,IAAA,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE,UAAC,EAAyB,EAAE,CAAC,EAAE,OAAO,EAAA;AAApC,QAAA,IAAA,IAAI,QAAA,EAAE,KAAK,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,UAAU,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACxD,QAAA,IAAI,IAAI,IAAI,KAAK,IAAI,UAAU,EAAE;AAC/B,YAAA,IAAM,iBAAiB,GAAG,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,UAAU,CAAC;AACxD,YAAA,IAAA,EAAmC,GAAA,iBAAiB,CAAhC,QAAA,EAAV,IAAI,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,GAAG,GAAA,EAAA,EAAE,UAAQ,GAAK,iBAAiB,SAAtB,CAAuB;AAE7D,YAAA,IAAM,QAAQ,GAAG,YAAA;gBACf,KAAK,CAAC,MAAM,EAAE,CAAC;AACf,gBAAA,UAAQ,KAAR,IAAA,IAAA,UAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAQ,EAAI,CAAC;AACf,aAAC,CAAC;AAEF,YAAA,cAAc,GAAG,IAAI,GAAG,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC;AAC5D,YAAA,WAAW,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;AACnC,SAAA;AAED,QAAA,OAAO,CAAC,YAAA;YACN,IAAI,IAAI,IAAI,cAAc,EAAE;AAC1B,gBAAA,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;AACtC,aAAA;AACH,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;AAEM,IAAM,eAAe,GAAG;AAC7B,IAAA,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,CAA6B;CAC1D;;ACrCD,IAAM,IAAI,GAAG,WAAW,CAAC;AAClB,IAAM,IAAI,GAAG,UAAC,GAAW,EAAc,EAAA,OAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,EAAA,CAAC;AAEvD,SAAU,MAAM,CAAC,KAAY,EAAA;IACjC,IAAM,MAAM,GAAU,EAAE,CAAC;AACzB,IAAA,KAAK,IAAM,GAAG,IAAI,KAAK,EAAE;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACd,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1B,SAAA;AACF,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAEe,SAAA,cAAc,CAC5B,SAAuB,EACvB,YAAe,EAAA;AAEf,IAAA,IAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;IAE9D,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,EAAE;AAC1D,QAAA,OAAO,KAAK,CAAC,KAAK,IAAI,YAAY,CAAC;AACpC,KAAA;IAED,OAAO,KAAK,IAAI,YAAY,CAAC;AAC/B;;ACxBO,IAAM,mBAAmB,GAC9B,mBAEE;SAEY,UAAU,CACxB,KAAmC,EACnC,OAAqB,EACrB,cAA+C,EAAA;IAE/C,IAAM,qBAAqB,GAAG,MAAM,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;IAC9D,IAAM,kBAAkB,GAAG,QAAQ,CAAC,YAAA,EAAM,QACrC,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,cAAc,CAAC,qBAAqB,EAAE,EAAE,CAAC,CACzC,EAAA,cAAc,KAAd,IAAA,IAAA,cAAc,KAAd,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,cAAc,CAAE,KAAK,CACxB,EAAA,EAAA,CAAC,CAAC;AAEJ,IAAA,WAAW,CAAC,YAAA;AACV,QAAA,IAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO;AACR,SAAA;QAED,IAAI,OAAO,CAAC,KAAK,EAAE;AACjB,YAAA,QAAQ,CAAC,WAAW,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAChD,SAAA;AAAM,aAAA;YACL,QAAQ,CAAC,WAAW,EAAE,CAAC;AACxB,SAAA;AACH,KAAC,CAAC,CAAC;AACL,CAAC;AAEM,IAAM,YAAY,GAAG;AAC1B,IAAA,OAAO,EAAE,OAAO;AAChB,IAAA,cAAc,EAAE,MAAkC;CACnD;;AC1CM,IAAM,QAAQ,GAAG,eAAe;;AC4CvC,IAAI,IAAI,EAAE;IACR,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5C,CAAA;AAEM,IAAM,SAAS,GAAG,UAAqD;AACvE,IAAM,gBAAgB,GAC3B,gBAAiE;AAC5D,IAAM,kBAAkB,GAC7B,kBAAqE;AAGvE,IAAM,eAAe,GAAG,kBAAkB,CAAC;AAE3C,cAAe,eAAe,CAAC;AAC7B,IAAA,IAAI,EAAE,SAAS;AACf,IAAA,KAAK,sBACH,MAAM,EAAE,MAA0B,EAClC,KAAK,EAAE;AACL,YAAA,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAoB;AAC1C,SAAA,EACD,WAAW,EAAE,MAA+B,EAC5C,aAAa,EAAE,MAAiC,EAChD,KAAK,EAAE,MAAM,EACb,YAAY,EAAE,OAAO,IAClB,eAAe,CAAA,EACf,YAAY,CAChB;AACD,IAAA,KAAK,EAAE,EAAsB;AAC7B,IAAA,YAAY,EAAE,KAAK;AACnB,IAAA,KAAK,EAAL,UAAM,KAAK,EAAE,EAAS,EAAA;AAAP,QAAA,IAAA,KAAK,GAAA,EAAA,CAAA,KAAA,CAAA;AAClB,QAAA,IAAM,IAAI,GAAG,UAAU,EAAkB,CAAC;AAC1C,QAAA,IAAM,KAAK,GAAG,UAAU,EAAe,CAAC;AACxC,QAAA,IAAM,KAAK,GAAG,UAAU,EAAe,CAAC;AACxC,QAAA,IAAM,YAAY,GAAG,UAAU,EAAU,CAAC;QAC1C,IAAM,YAAY,GAAG,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC7C,IAAM,kBAAkB,GAAG,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAC1D,IAAM,oBAAoB,GAAG,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;AAExD,QAAA,IAAA,KAAwD,MAAM,CAAC,KAAK,CAAC,EAAnE,UAAU,GAAA,EAAA,CAAA,UAAA,EAAE,YAAY,kBAAA,EAAE,OAAO,aAAA,EAAE,cAAc,oBAAkB,CAAC;AAE5E,QAAA,IAAM,UAAU,GAAG,QAAQ,CACzB,YAAM,EAAA,OAAA,YAAY,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAA1C,EAA0C,CACjD,CAAC;AACF,QAAA,IAAM,SAAS,GAAG,QAAQ,CACxB,YAAM,EAAA,OAAA,KAAK,CAAC,KAAK,IAAI,cAAc,CAAC,YAAY,EAAE,EAAE,CAAC,CAA/C,EAA+C,CACtD,CAAC;AACF,QAAA,IAAM,eAAe,GAAG,QAAQ,CAC9B,YAAM,EAAA,OAAA,KAAK,CAAC,WAAW,IAAI,cAAc,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAA3D,EAA2D,CAClE,CAAC;AACF,QAAA,IAAM,iBAAiB,GAAG,QAAQ,CAChC,YAAM,EAAA,OAAA,KAAK,CAAC,aAAa,IAAI,cAAc,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAA/D,EAA+D,CACtE,CAAC;AACF,QAAA,IAAM,aAAa,GAAG,QAAQ,CAAC,YAAM,EAAA,OAAA,MAAM,CAAC,KAAK,CAAC,CAAb,EAAa,CAAC,CAAC;QACpD,IAAM,eAAe,GAA4B,EAAE,CAAC;QAGpD,IAAM,SAAS,GAAG,kBAAkB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;QACxD,IAAM,aAAa,GAAwB,EAAE,CAAC;QAE9C,IAAI,CAAC,SAAS,EAAE;AAOd,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;iBACf,MAAM,CAAC,UAAA,GAAG,EAAI,EAAA,OAAA,IAAI,CAAC,GAAG,CAAC,CAAT,EAAS,CAAC;iBACxB,OAAO,CAAC,UAAA,GAAG,EAAA;AAGV,gBAAA,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAGvD,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;oBAElC,IAAM,SAAS,GAAG,IAAK,CAAA,MAAA,CAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA,CAAA,MAAA,CAAG,KAAK,CAAC,KAAK,CAChE,CAAC,CACF,CAAE,CAAC;oBAEJ,eAAe,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;oBACxC,OAAO;AACR,iBAAA;AAID,gBAAA,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,EAAE;AAChD,oBAAA,KAAK,GAAG,GAAA,CAAA,MAAA,CAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAE,CAAC;AACpD,iBAAA;gBAED,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AACpC,aAAC,CAAC,CAAC;AACN,SAAA;AAAM,aAAA;YASL,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG,EAAA;AAChC,gBAAA,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AAC7B,oBAAA,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;AACtE,iBAAA;AAAM,qBAAA;oBACL,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;AACrC,iBAAA;AACH,aAAC,CAAC,CAAC;AACJ,SAAA;QAED,SAASA,MAAI,CAAC,MAAe,EAAA;AAC3B,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;gBAChB,OAAO;AACR,aAAA;YAED,IAAM,QAAQ,IAAI,KAAK,CAAC,KAAK,GAAGC,IAAS,CACvC,KAAK,CAAC,KAAK,EACX,SAAS,CAAC,KAAK,EACf,eAAe,CAAC,KAAK,CACtB,CAAC,CAAC;YAEH,IAAI,KAAK,CAAC,KAAK,EAAE;AACf,gBAAA,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC9B,aAAA;YAED,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG,EAAA;AACpC,gBAAA,IAAI,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;gBAEjC,IAAI,CAAC,OAAO,EAAE;oBACZ,OAAO;AACR,iBAAA;AAED,gBAAA,IAAI,KAAK,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;gBAC9B,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC3B,oBAAA,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC3B,oBAAA,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,iBAAA;gBAED,IAAI,MAAM,GAAgB,QAAQ,CAAC;gBACnC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC9B,oBAAA,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;AAC1B,oBAAA,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC5B,iBAAA;gBAED,IAAI,OAAO,CAAC,QAAQ,EAAE;oBACpB,OAAO,OAAO,CAAC,QAAQ,CAAC;oBAExB,IAAM,KAAG,GAAG,OAAO,CAAC;AAEpB,oBAAA,OAAO,GAAG,YAAA;wBAAC,IAAc,IAAA,GAAA,EAAA,CAAA;6BAAd,IAAc,EAAA,GAAA,CAAA,EAAd,EAAc,GAAA,SAAA,CAAA,MAAA,EAAd,EAAc,EAAA,EAAA;4BAAd,IAAc,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;wBACvB,KAAG,CAAA,KAAA,CAAA,KAAA,CAAA,EAAI,IAAI,CAAE,CAAA;AACb,wBAAA,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC7B,qBAAC,CAAC;AACH,iBAAA;AAKD,gBAAA,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC5B,aAAC,CAAC,CAAC;AAEH,YAAA,SAAS,MAAM,GAAA;AACb,gBAAA,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE;oBACtC,QAAQ,CAAC,MAAM,EAAE,CAAC;AACnB,iBAAA;aACF;AAED,YAAA,SAAS,MAAM,GAAA;AACb,gBAAA,IAAM,GAAG,GAAG,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC;AACvC,gBAAA,IAAI,GAAG,EAAE;oBACP,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAClD,iBAAA;aACF;YAED,IAAI,UAAU,CAAC,KAAK,EAAE;AAGpB,gBAAA,QAAQ,CAAC,YAAA;AACP,oBAAA,MAAM,EAAE,CAAC;AACT,oBAAA,MAAM,EAAE,CAAC;AACX,iBAAC,CAAC,CAAC;AACJ,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,EAAE,CAAC;AACV,aAAA;SACF;AAED,QAAA,SAAS,SAAS,CAAC,MAAc,EAAE,aAA6B,EAAA;YAC9D,IAAI,KAAK,CAAC,YAAY,EAAE;AACtB,gBAAA,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC;AAC7B,aAAA;AAED,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;gBAChBD,MAAI,CAAC,MAAM,CAAC,CAAC;AACd,aAAA;AAAM,iBAAA;gBACL,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,IAAI,EAAE,CAAC,CAAC;AACpD,aAAA;SACF;AAED,QAAA,SAAS,OAAO,GAAA;YACd,IAAI,KAAK,CAAC,KAAK,EAAE;AACf,gBAAA,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AACtB,gBAAA,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AACzB,aAAA;SACF;QAED,IAAI,aAAa,GAAwB,IAAI,CAAC;AAC9C,QAAA,KAAK,CACH,YAAY,EACZ,UAAA,YAAY,EAAA;AACV,YAAA,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;AACvC,gBAAA,aAAa,EAAE,CAAC;gBAChB,aAAa,GAAG,IAAI,CAAC;AACtB,aAAA;YAED,IAAI,CAAC,YAAY,EAAE;AACjB,gBAAA,aAAa,GAAG,KAAK,CACnB,YAAA,EAAM,OAAA,KAAK,CAAC,MAAM,CAAA,EAAA,EAClB,UAAC,MAAM,EAAE,SAAS,EAAA;oBAChB,IAAI,CAAC,MAAM,EAAE;wBACX,OAAO;AACR,qBAAA;AACD,oBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;AAChB,wBAAAA,MAAI,EAAE,CAAC;AACR,qBAAA;AAAM,yBAAA;AACL,wBAAA,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAG1B,QAAA,CAAA,EAAA,QAAQ,EAAE,MAAM,KAAK,SAAS,EAAA,EAC3B,iBAAiB,CAAC,KAAK,EAC1B,CAAC;AACJ,qBAAA;AACH,iBAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAC;AACH,aAAA;AACH,SAAC,EACD;AACE,YAAA,SAAS,EAAE,IAAI;AAChB,SAAA,CACF,CAAC;AAEF,QAAA,KAAK,CACH,CAAC,SAAS,EAAE,eAAe,CAAC,EAC5B,YAAA;AACE,YAAA,OAAO,EAAE,CAAC;AACV,YAAAA,MAAI,EAAE,CAAC;AACT,SAAC,EACD;AACE,YAAA,IAAI,EAAE,IAAI;AACX,SAAA,CACF,CAAC;AAEF,QAAA,WAAW,CAAC,YAAA;AACV,YAAA,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;gBAC9B,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AACjC,aAAA;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;AAEtC,QAAA,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;AAE3C,QAAA,aAAa,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AAExC,QAAA,SAAS,CAAC,YAAA;AACR,YAAAA,MAAI,EAAE,CAAC;AACT,SAAC,CAAC,CAAC;AAEH,QAAA,eAAe,CAAC,YAAA;AACd,YAMO;AACL,gBAAA,OAAO,EAAE,CAAC;AACX,aAAA;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,OAAA,QAAA,CAAA,EACE,KAAK,EAAA,KAAA,EACL,IAAI,EAAA,IAAA,EACJ,KAAK,EAAA,KAAA,EACL,SAAS,EAAA,SAAA,EACT,aAAa,EAAA,aAAA,EACb,eAAe,EAAA,eAAA,EAAA,EACZ,SAAS,CACZ,CAAA;KACH;AACD,IAAA,MAAM,EAAN,YAAA;QAGE,IAAM,KAAK,IACT,IAAI;AACF,cAAE,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE;cACxD,QAAA,CAAA,QAAA,CAAA,EAAA,EAAM,IAAI,CAAC,aAAa,CAAA,EAAK,IAAI,CAAC,eAAe,CAAE,CAChD,CAAC;AACT,QAAA,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC;QACnB,KAAK,CAAC,OAAK,CAAA,GAAG,KAAK,CAAC,OAAK,CAAA,GAAG,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,OAAK,CAAA,CAAC,GAAG,SAAS,CAAC;AACxE,QAAA,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE;AACxB,YAAA,CAAC,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,OAAK,EAAE,mBAAmB,EAAE,CAAC;AACvD,SAAA,CAAC,CAAC;KACJ;AACF,CAAA,CAAC;;;;"}