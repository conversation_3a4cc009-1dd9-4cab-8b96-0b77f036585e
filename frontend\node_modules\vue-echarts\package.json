{"name": "vue-echarts", "version": "6.7.3", "description": "Vue.js component for Apache ECharts™.", "author": "GU Yiling <<EMAIL>>", "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "files": ["dist", "scripts/postinstall.js"], "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.24.4", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.2.2", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.24", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.9.0", "comment-mark": "^1.1.1", "core-js": "^3.37.0", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.12", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.38", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.4.24", "vue2": "npm:vue@^2.7.16", "webpack": "^5.91.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}, "repository": "https://github.com/ecomfe/vue-echarts.git", "types": "dist/index.vue2_7.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}}