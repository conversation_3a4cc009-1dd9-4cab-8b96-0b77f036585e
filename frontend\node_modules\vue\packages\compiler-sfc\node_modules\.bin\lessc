#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Vue/vue/node_modules/.pnpm/less@4.2.0/node_modules/less/bin/node_modules:/Users/<USER>/Vue/vue/node_modules/.pnpm/less@4.2.0/node_modules/less/node_modules:/Users/<USER>/Vue/vue/node_modules/.pnpm/less@4.2.0/node_modules:/Users/<USER>/Vue/vue/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Vue/vue/node_modules/.pnpm/less@4.2.0/node_modules/less/bin/node_modules:/Users/<USER>/Vue/vue/node_modules/.pnpm/less@4.2.0/node_modules/less/node_modules:/Users/<USER>/Vue/vue/node_modules/.pnpm/less@4.2.0/node_modules:/Users/<USER>/Vue/vue/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../less/bin/lessc" "$@"
else
  exec node  "$basedir/../less/bin/lessc" "$@"
fi
