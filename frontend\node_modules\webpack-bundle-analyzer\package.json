{"name": "webpack-bundle-analyzer", "version": "4.10.2", "description": "Webpack plugin and CLI utility that represents bundle content as convenient interactive zoomable treemap", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/webpack-contrib/webpack-bundle-analyzer", "changelog": "https://github.com/webpack-contrib/webpack-bundle-analyzer/blob/master/CHANGELOG.md", "bugs": {"url": "https://github.com/webpack-contrib/webpack-bundle-analyzer/issues"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/webpack-bundle-analyzer.git"}, "main": "lib/index.js", "bin": "lib/bin/analyzer.js", "engines": {"node": ">= 10.13.0"}, "packageManager": "npm@6.14.8", "scripts": {"start": "gulp watch", "build": "gulp build", "npm-publish": "npm run lint && npm run build && npm test && npm publish", "lint": "eslint --ext js,jsx .", "install-test-webpack-versions": "./bin/install-test-webpack-versions.sh", "test": "npm run install-test-webpack-versions && jest --runInBand", "test-dev": "npm run install-test-webpack-versions && jest --watch --runInBand"}, "files": ["public", "lib"], "dependencies": {"@discoveryjs/json-ext": "0.5.7", "acorn": "^8.0.4", "acorn-walk": "^8.0.0", "commander": "^7.2.0", "debounce": "^1.2.1", "escape-string-regexp": "^4.0.0", "gzip-size": "^6.0.0", "html-escaper": "^2.0.2", "opener": "^1.5.2", "picocolors": "^1.0.0", "sirv": "^2.0.3", "ws": "^7.3.1"}, "devDependencies": {"@babel/core": "7.14.3", "@babel/plugin-proposal-class-properties": "7.13.0", "@babel/plugin-proposal-decorators": "7.14.2", "@babel/plugin-transform-runtime": "7.14.3", "@babel/preset-env": "7.14.2", "@babel/preset-react": "7.13.13", "@babel/runtime": "7.14.0", "@carrotsearch/foamtree": "3.5.0", "autoprefixer": "10.2.5", "babel-eslint": "10.1.0", "babel-loader": "8.2.2", "babel-plugin-lodash": "3.3.4", "chai": "4.3.4", "chai-subset": "1.6.0", "classnames": "2.3.1", "core-js": "3.12.1", "css-loader": "5.2.5", "cssnano": "5.0.4", "del": "6.0.0", "eslint": "5.16.0", "eslint-config-th0r": "2.0.0", "eslint-config-th0r-react": "2.0.1", "eslint-plugin-react": "7.23.2", "filesize": "^6.3.0", "globby": "11.0.3", "gulp": "4.0.2", "gulp-babel": "8.0.0", "jest": "27.2.2", "lodash.memoize": "^4.1.2", "lodash.merge": "^4.6.2", "lodash.partial": "^4.2.1", "mobx": "5.15.7", "mobx-react": "6.3.1", "postcss": "8.3.0", "postcss-icss-values": "2.0.2", "postcss-loader": "5.3.0", "preact": "10.5.13", "puppeteer": "10.4.0", "stream-combiner2": "1.1.1", "style-loader": "2.0.0", "terser-webpack-plugin": "5.1.2", "url-loader": "4.1.1", "webpack": "5.76.0", "webpack-cli": "3.3.12", "webpack-dev-server": "3.11.3"}, "keywords": ["webpack", "bundle", "analyzer", "modules", "size", "interactive", "chart", "treemap", "zoomable", "zoom"]}