"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const transform_1 = __importDefault(require("../definitions/transform"));
const transform = (ajv) => ajv.addKeyword((0, transform_1.default)());
exports.default = transform;
module.exports = transform;
//# sourceMappingURL=transform.js.map