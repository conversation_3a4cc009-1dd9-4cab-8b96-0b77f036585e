"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const exclusiveRange_1 = __importDefault(require("../definitions/exclusiveRange"));
const exclusiveRange = (ajv) => ajv.addKeyword((0, exclusiveRange_1.default)());
exports.default = exclusiveRange;
module.exports = exclusiveRange;
//# sourceMappingURL=exclusiveRange.js.map