# normalize-path [![NPM version](https://badge.fury.io/js/normalize-path.svg)](http://badge.fury.io/js/normalize-path)

> Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes.

## Install with [npm](npmjs.org)

```bash
npm i normalize-path --save
```

## Usage

```js
var normalize = require('normalize-path');

normalize('\\foo\\bar\\baz\\');
//=> '/foo/bar/baz'

normalize('./foo/bar/baz/');
//=> './foo/bar/baz'
```

Pass `false` as the last argument to **not** strip trailing slashes:

```js
normalize('./foo/bar/baz/', false);
//=> './foo/bar/baz/'

normalize('foo\\bar\\baz\\', false);
//=> 'foo/bar/baz/'
```

## Run tests

```bash
npm i -d && npm test
```


## Contributing
Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/normalize-path/issues).

## Author

**Jon Schlinkert**
 
+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert) 

## License
Copyright (c) 2015 Jon Schlinkert  
Released under the MIT license

***

_This file was generated by [verb](https://github.com/assemble/verb) on January 23, 2015._