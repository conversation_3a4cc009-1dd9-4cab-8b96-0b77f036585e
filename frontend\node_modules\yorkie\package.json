{"name": "yorkie", "version": "2.0.0", "description": "githooks management forked from husky", "engines": {"node": ">=4"}, "scripts": {"test": "jest", "format": "prettier --single-quote --no-semi --write **/*.js", "install": "node bin/install.js", "uninstall": "node bin/uninstall.js"}, "repository": {"type": "git", "url": "git://github.com/yyx990803/yorkie.git"}, "keywords": ["git", "hook", "hooks", "pre-commit", "precommit", "post-commit", "postcommit", "pre-push", "prepush", "post-merge", "postmerge", "test"], "authors": ["Typicode <<EMAIL>>", "<PERSON>"], "license": "MIT", "bugs": {"url": "https://github.com/yyx990803/yorkie/issues"}, "homepage": "https://github.com/yyx990803/yorkie", "devDependencies": {"jest": "^20.0.4", "mkdirp": "^0.5.1", "prettier": "^1.4.4", "rimraf": "^2.6.1", "tempy": "^0.1.0"}, "dependencies": {"execa": "^0.8.0", "is-ci": "^1.0.10", "normalize-path": "^1.0.0", "strip-indent": "^2.0.0"}, "standard": {"env": {"jest": true}}}