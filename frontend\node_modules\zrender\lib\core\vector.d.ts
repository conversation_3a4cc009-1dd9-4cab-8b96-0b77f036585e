import { MatrixArray } from './matrix';
export declare type VectorArray = number[];
export declare function create(x?: number, y?: number): VectorArray;
export declare function copy<T extends VectorArray>(out: T, v: VectorArray): T;
export declare function clone(v: VectorArray): VectorArray;
export declare function set<T extends VectorArray>(out: T, a: number, b: number): T;
export declare function add<T extends VectorArray>(out: T, v1: VectorArray, v2: VectorArray): T;
export declare function scaleAndAdd<T extends VectorArray>(out: T, v1: VectorArray, v2: VectorArray, a: number): T;
export declare function sub<T extends VectorArray>(out: T, v1: VectorArray, v2: VectorArray): T;
export declare function len(v: VectorArray): number;
export declare const length: typeof len;
export declare function lenSquare(v: VectorArray): number;
export declare const lengthSquare: typeof lenSquare;
export declare function mul<T extends VectorArray>(out: T, v1: VectorArray, v2: VectorArray): T;
export declare function div<T extends VectorArray>(out: T, v1: VectorArray, v2: VectorArray): T;
export declare function dot(v1: VectorArray, v2: VectorArray): number;
export declare function scale<T extends VectorArray>(out: T, v: VectorArray, s: number): T;
export declare function normalize<T extends VectorArray>(out: T, v: VectorArray): T;
export declare function distance(v1: VectorArray, v2: VectorArray): number;
export declare const dist: typeof distance;
export declare function distanceSquare(v1: VectorArray, v2: VectorArray): number;
export declare const distSquare: typeof distanceSquare;
export declare function negate<T extends VectorArray>(out: T, v: VectorArray): T;
export declare function lerp<T extends VectorArray>(out: T, v1: VectorArray, v2: VectorArray, t: number): T;
export declare function applyTransform<T extends VectorArray>(out: T, v: VectorArray, m: MatrixArray): T;
export declare function min<T extends VectorArray>(out: T, v1: VectorArray, v2: VectorArray): T;
export declare function max<T extends VectorArray>(out: T, v1: VectorArray, v2: VectorArray): T;
