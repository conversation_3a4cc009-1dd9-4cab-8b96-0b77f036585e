<template>
  <div id="app">
    <router-view/>
    <!-- 全局版权信息 -->
    <footer class="global-footer">
      <div class="footer-content">
        <p>&copy; 2025 永盛制冷维修有限公司-家电售卖与维修 | CHENKK-开发</p>
        <p class="icp-info">备案号：（暂时为空）</p>
      </div>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: #f5f5f5;
}

/* 全局版权信息样式 */
.global-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 10px 0;
  font-size: 12px;
  z-index: 1000;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

.footer-content p {
  margin: 3px 0;
  line-height: 1.4;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  font-weight: 500;
}

.icp-info {
  opacity: 0.9;
  font-size: 11px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* 为页面内容添加底部边距，避免被版权信息遮挡 */
#app {
  padding-bottom: 60px;
}

/* 移动端全局适配 */
@media (max-width: 768px) {
  /* 全局背景优化 */
  body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
  }

  /* 移动端版权信息调整 */
  .global-footer {
    padding: 8px 15px;
    font-size: 11px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .footer-content p {
    margin: 2px 0;
    line-height: 1.4;
    color: #495057;
    font-weight: 500;
  }

  .icp-info {
    font-size: 10px;
    color: #6c757d;
  }

  #app {
    padding-bottom: 55px;
  }

  /* 移动端通用样式 */
  .el-container {
    flex-direction: column !important;
  }

  .el-header {
    height: auto !important;
    padding: 10px 15px !important;
  }

  .el-main {
    padding: 15px !important;
  }

  /* 移动端表格适配 */
  .el-table {
    font-size: 12px;
  }

  .el-table th,
  .el-table td {
    padding: 8px 5px !important;
  }

  /* 移动端表单适配 */
  .el-form-item {
    margin-bottom: 15px !important;
  }

  .el-form-item__label {
    line-height: 1.2 !important;
    padding-bottom: 5px !important;
  }

  /* 移动端按钮适配 */
  .el-button {
    padding: 8px 15px !important;
    font-size: 13px !important;
  }

  .el-button--small {
    padding: 6px 10px !important;
    font-size: 12px !important;
  }

  /* 移动端卡片适配 */
  .el-card {
    margin-bottom: 15px !important;
  }

  .el-card__body {
    padding: 15px !important;
  }

  /* 移动端对话框适配 */
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .el-dialog__body {
    padding: 15px !important;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,.12), 0 0 6px rgba(0,0,0,.04);
}

.page-content {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,.12), 0 0 6px rgba(0,0,0,.04);
}

/* 不同端的主题色 */
.customer-theme {
  --primary-color: #409EFF;
  --primary-light: #ecf5ff;
}

.worker-theme {
  --primary-color: #67C23A;
  --primary-light: #f0f9ff;
}

.admin-theme {
  --primary-color: #E6A23C;
  --primary-light: #fdf6ec;
}
</style>
