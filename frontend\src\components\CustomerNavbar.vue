<template>
  <el-menu
    :default-active="activeIndex"
    class="customer-navbar"
    mode="horizontal"
    background-color="#409EFF"
    text-color="#fff"
    active-text-color="#ffd04b"
    @select="handleSelect"
  >
    <div class="navbar-brand">
      <i class="el-icon-s-tools"></i>
      <span>家电维修系统</span>
    </div>
    
    <el-menu-item index="/customer/home">
      <i class="el-icon-house"></i>
      <span>首页</span>
    </el-menu-item>
    
    <el-menu-item index="/customer/create-order">
      <i class="el-icon-plus"></i>
      <span>创建工单</span>
    </el-menu-item>
    
    <el-menu-item index="/customer/orders">
      <i class="el-icon-document"></i>
      <span>我的工单</span>
    </el-menu-item>
    
    <div class="navbar-user">
      <el-dropdown @command="handleCommand">
        <span class="user-info">
          <i class="el-icon-user"></i>
          {{ currentUser.name }}
          <i class="el-icon-arrow-down"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="profile">个人信息</el-dropdown-item>
          <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </el-menu>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'CustomerNavbar',
  computed: {
    ...mapGetters(['currentUser']),
    activeIndex() {
      return this.$route.path
    }
  },
  methods: {
    handleSelect(key) {
      if (key !== this.$route.path) {
        this.$router.push(key)
      }
    },
    
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$message.info('个人信息功能开发中')
          break
        case 'logout':
          this.$store.dispatch('logout')
          this.$message.success('退出登录成功')
          this.$router.push('/customer/login')
          break
      }
    }
  }
}
</script>

<style scoped>
.customer-navbar {
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-right: 40px;
}

.navbar-brand i {
  margin-right: 8px;
  font-size: 20px;
}

.navbar-user {
  margin-left: auto;
}

.user-info {
  cursor: pointer;
  color: white;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.user-info:hover {
  opacity: 0.8;
}

.el-menu-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.el-menu-item i {
  font-size: 16px;
}
</style>
