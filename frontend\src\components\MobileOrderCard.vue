<template>
  <div class="mobile-order-card" @click="$emit('view-detail', order)">
    <div class="card-header">
      <div class="order-info">
        <span class="order-id">#{{ order.id }}</span>
        <el-tag :type="getStatusType(order.status)" size="mini">
          {{ getStatusText(order.status) }}
        </el-tag>
      </div>
      <div class="urgency-badge" :class="order.urgency">
        {{ getUrgencyText(order.urgency) }}
      </div>
    </div>
    
    <div class="card-content">
      <div class="appliance-info">
        <i class="el-icon-cpu"></i>
        <span class="appliance-type">{{ getApplianceTypeName(order.appliance_type) }}</span>
        <span class="brand-model">{{ order.brand_model }}</span>
      </div>
      
      <div class="problem-desc">
        <i class="el-icon-warning-outline"></i>
        <span>{{ order.problem_description }}</span>
      </div>
      
      <div class="contact-info">
        <i class="el-icon-user"></i>
        <span>{{ order.contact_name }}</span>
        <i class="el-icon-phone"></i>
        <span>{{ order.contact_phone }}</span>
      </div>
      
      <div class="address-info">
        <i class="el-icon-location-outline"></i>
        <span>{{ order.address }}</span>
      </div>
      
      <div class="time-info">
        <div class="create-time">
          <i class="el-icon-time"></i>
          <span>创建：{{ formatDate(order.created_at) }}</span>
        </div>
        <div v-if="order.preferred_date" class="preferred-time">
          <i class="el-icon-date"></i>
          <span>预约：{{ order.preferred_date }} {{ getTimeSlotText(order.preferred_time) }}</span>
        </div>
      </div>
    </div>
    
    <div class="card-actions" v-if="showActions">
      <slot name="actions" :order="order"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileOrderCard',
  props: {
    order: {
      type: Object,
      required: true
    },
    showActions: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    getStatusText(status) {
      const statusMap = {
        'pending': '待接单',
        'accepted': '已接单',
        'in_progress': '维修中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    },
    
    getStatusType(status) {
      const typeMap = {
        'pending': 'info',
        'accepted': 'warning',
        'in_progress': 'primary',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    getUrgencyText(urgency) {
      const urgencyMap = {
        'low': '一般',
        'medium': '紧急',
        'high': '非常紧急'
      }
      return urgencyMap[urgency] || urgency
    },
    
    getApplianceTypeName(type) {
      const typeMap = {
        'air_conditioner': '空调',
        'washing_machine': '洗衣机',
        'refrigerator': '冰箱',
        'water_heater': '热水器',
        'television': '电视',
        'microwave': '微波炉'
      }
      return typeMap[type] || type
    },
    
    getTimeSlotText(timeSlot) {
      const slotMap = {
        'morning': '上午',
        'afternoon': '下午',
        'evening': '晚上'
      }
      return slotMap[timeSlot] || timeSlot
    },
    
    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style scoped>
.mobile-order-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

.mobile-order-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
  border-radius: 16px;
}

.mobile-order-card:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-4px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(83, 168, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(64, 158, 255, 0.1);
  position: relative;
  z-index: 1;
}

.order-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.order-id {
  font-weight: 700;
  background: linear-gradient(135deg, #409EFF 0%, #53a8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 18px;
  letter-spacing: 0.5px;
}

.urgency-badge {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.urgency-badge.low {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
  border: 1px solid rgba(25, 118, 210, 0.2);
}

.urgency-badge.medium {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  color: #f57c00;
  border: 1px solid rgba(245, 124, 0, 0.2);
}

.urgency-badge.high {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  color: #d32f2f;
  border: 1px solid rgba(211, 47, 47, 0.2);
}

.card-content {
  padding: 15px;
}

.appliance-info,
.problem-desc,
.contact-info,
.address-info {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.4;
}

.appliance-info i,
.problem-desc i,
.contact-info i,
.address-info i {
  color: #409EFF;
  margin-top: 2px;
  flex-shrink: 0;
}

.appliance-type {
  font-weight: 500;
  color: #303133;
}

.brand-model {
  color: #909399;
  background: #f5f7fa;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 12px;
  margin-left: 5px;
}

.contact-info {
  display: grid;
  grid-template-columns: auto 1fr auto 1fr;
  gap: 5px 8px;
  align-items: center;
}

.time-info {
  margin-top: 15px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.create-time,
.preferred-time {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.create-time i,
.preferred-time i {
  color: #c0c4cc;
}

.card-actions {
  padding: 10px 15px;
  background: #fafafa;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 移动端进一步优化 */
@media (max-width: 480px) {
  .card-header {
    padding: 12px;
  }
  
  .card-content {
    padding: 12px;
  }
  
  .order-id {
    font-size: 14px;
  }
  
  .appliance-info,
  .problem-desc,
  .contact-info,
  .address-info {
    font-size: 13px;
    margin-bottom: 10px;
  }
  
  .contact-info {
    grid-template-columns: 1fr;
    gap: 5px;
  }
  
  .card-actions {
    padding: 8px 12px;
  }
}
</style>
