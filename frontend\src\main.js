import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import axios from 'axios'
import ECharts from 'vue-echarts'
import 'echarts/lib/chart/bar'
import 'echarts/lib/chart/line'
import 'echarts/lib/chart/pie'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/title'

Vue.config.productionTip = false

// 配置Element UI
Vue.use(ElementUI)

// 注册ECharts组件
Vue.component('v-chart', ECharts)

// 配置axios
axios.defaults.timeout = 10000

// 请求拦截器
axios.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  response => {
    console.log('[前端] API响应成功:', response.config.url)
    return response.data
  },
  error => {
    console.log('[前端] API响应错误:', error.config?.url, error.response?.status)

    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 检查是否是token相关的错误
          const errorMessage = error.response?.data?.message || ''
          const isTokenError = errorMessage.includes('token') ||
                              errorMessage.includes('过期') ||
                              errorMessage.includes('无效') ||
                              errorMessage.includes('expired') ||
                              errorMessage.includes('invalid') ||
                              errorMessage.includes('unauthorized')

          if (isTokenError) {
            console.log('[前端] Token过期或无效，清除认证信息')
            // 清除认证信息
            localStorage.removeItem('token')
            localStorage.removeItem('user')
            // 清除store中的用户信息
            store.commit('CLEAR_AUTH')
            // 显示错误信息
            ElementUI.Message.error('登录已过期，请重新登录')
            // 不在这里进行路由跳转，让路由守卫处理
          } else {
            console.log('[前端] 401错误但非token问题，不清除认证信息')
            // 显示错误信息但不清除认证
            ElementUI.Message.error(errorMessage || '请求失败，请检查权限')
          }
          break
        case 403:
          console.log('[前端] 403错误，权限不足')
          ElementUI.Message.error('权限不足')
          break
        case 500:
          console.log('[前端] 500错误，服务器错误')
          ElementUI.Message.error('服务器错误')
          break
        default:
          console.log('[前端] 其他HTTP错误:', error.response.status)
          ElementUI.Message.error(error.response.data.message || '请求失败')
      }
    } else {
      console.log('[前端] 网络错误:', error.message)
      ElementUI.Message.error('网络错误')
    }
    return Promise.reject(error)
  }
)

Vue.prototype.$http = axios

// 添加安全的路由跳转方法
Vue.prototype.$safeRouter = {
  push(location) {
    return router.push(location).catch(err => {
      if (err.name !== 'NavigationDuplicated') {
        console.error('路由跳转错误:', err)
      }
    })
  },
  replace(location) {
    return router.replace(location).catch(err => {
      if (err.name !== 'NavigationDuplicated') {
        console.error('路由跳转错误:', err)
      }
    })
  }
}

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')



