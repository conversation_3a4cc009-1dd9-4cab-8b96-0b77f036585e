import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '../store'

// 导入各端的页面组件
import CustomerLogin from '../views/customer/Login.vue'
import CustomerRegister from '../views/customer/Register.vue'
import CustomerHome from '../views/customer/Home.vue'
import CustomerOrders from '../views/customer/Orders.vue'

import CustomerCreateOrder from '../views/customer/CreateOrder.vue'

import WorkerLogin from '../views/worker/Login.vue'
import WorkerHome from '../views/worker/Home.vue'
import WorkerOrders from '../views/worker/Orders.vue'

import WorkerSalary from '../views/worker/Salary.vue'
import WorkerAvailableOrders from '../views/worker/AvailableOrders.vue'

import AdminLogin from '../views/admin/Login.vue'
import AdminHome from '../views/admin/Home.vue'
import AdminOrders from '../views/admin/Orders.vue'
import AdminStatistics from '../views/admin/Statistics.vue'
import AdminUsers from '../views/admin/Users.vue'
import AdminSalary from '../views/admin/Salary.vue'

import HomePage from '../views/HomePage.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'HomePage',
    component: HomePage
  },
  
  // 客户端路由
  {
    path: '/customer',
    redirect: '/customer/login'
  },
  {
    path: '/customer/login',
    name: 'CustomerLogin',
    component: CustomerLogin,
    meta: { requiresGuest: true, userType: 'customer' }
  },
  {
    path: '/customer/register',
    name: 'CustomerRegister',
    component: CustomerRegister,
    meta: { requiresGuest: true, userType: 'customer' }
  },
  {
    path: '/customer/home',
    name: 'CustomerHome',
    component: CustomerHome,
    meta: { requiresAuth: true, userType: 'customer' }
  },
  {
    path: '/customer/orders',
    name: 'CustomerOrders',
    component: CustomerOrders,
    meta: { requiresAuth: true, userType: 'customer' }
  },

  {
    path: '/customer/create-order',
    name: 'CustomerCreateOrder',
    component: CustomerCreateOrder,
    meta: { requiresAuth: true, userType: 'customer' }
  },
  
  // 工人端路由
  {
    path: '/worker',
    redirect: '/worker/login'
  },
  {
    path: '/worker/login',
    name: 'WorkerLogin',
    component: WorkerLogin,
    meta: { requiresGuest: true, userType: 'worker' }
  },
  {
    path: '/worker/home',
    name: 'WorkerHome',
    component: WorkerHome,
    meta: { requiresAuth: true, userType: 'worker' }
  },
  {
    path: '/worker/orders',
    name: 'WorkerOrders',
    component: WorkerOrders,
    meta: { requiresAuth: true, userType: 'worker' }
  },

  {
    path: '/worker/salary',
    name: 'WorkerSalary',
    component: WorkerSalary,
    meta: { requiresAuth: true, userType: 'worker' }
  },
  {
    path: '/worker/available-orders',
    name: 'WorkerAvailableOrders',
    component: WorkerAvailableOrders,
    meta: { requiresAuth: true, userType: 'worker' }
  },
  
  // 管理端路由
  {
    path: '/admin',
    redirect: '/admin/login'
  },
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: AdminLogin,
    meta: { requiresGuest: true, userType: 'admin' }
  },
  {
    path: '/admin/home',
    name: 'AdminHome',
    component: AdminHome,
    meta: { requiresAuth: true, userType: 'admin' }
  },
  {
    path: '/admin/orders',
    name: 'AdminOrders',
    component: AdminOrders,
    meta: { requiresAuth: true, userType: 'admin' }
  },
  {
    path: '/admin/statistics',
    name: 'AdminStatistics',
    component: AdminStatistics,
    meta: { requiresAuth: true, userType: 'admin' }
  },
  {
    path: '/admin/users',
    name: 'AdminUsers',
    component: AdminUsers,
    meta: { requiresAuth: true, userType: 'admin' }
  },
  {
    path: '/admin/salary',
    name: 'AdminSalary',
    component: AdminSalary,
    meta: { requiresAuth: true, userType: 'admin' }
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  console.log('[前端] 路由守卫开始 - from:', from.path, 'to:', to.path)

  // 防止重定向循环
  if (to.path === from.path) {
    console.log('[前端] 检测到相同路径，跳过处理')
    return next()
  }

  const token = localStorage.getItem('token')
  const user = JSON.parse(localStorage.getItem('user') || '{}')
  const isAuthenticated = !!token && user.userType

  console.log('[前端] 认证状态:', {
    hasToken: !!token,
    userType: user.userType,
    isAuthenticated,
    toPath: to.path,
    toMeta: to.meta
  })

  // 需要认证的路由
  if (to.matched.some(record => record.meta.requiresAuth)) {
    console.log('[前端] 访问需要认证的路由')

    if (!isAuthenticated) {
      console.log('[前端] 用户未认证，准备重定向到登录页')
      const userType = to.meta.userType
      if (userType && to.path !== `/${userType}/login`) {
        console.log('[前端] 重定向到:', `/${userType}/login`)
        return next(`/${userType}/login`)
      } else {
        console.log('[前端] 重定向到首页')
        return next('/')
      }
    } else if (to.meta.userType && user.userType !== to.meta.userType) {
      console.log('[前端] 用户类型不匹配，当前用户类型:', user.userType, '需要类型:', to.meta.userType)

      if (user.userType && ['customer', 'worker', 'admin'].includes(user.userType)) {
        const targetPath = `/${user.userType}/home`
        if (to.path !== targetPath) {
          console.log('[前端] 重定向到用户主页:', targetPath)
          return next(targetPath)
        }
      } else {
        console.log('[前端] 用户类型无效，清除认证信息')
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        if (to.path !== '/') {
          console.log('[前端] 重定向到首页')
          return next('/')
        }
      }
    }
    console.log('[前端] 认证通过，继续访问')
    return next()
  }

  // 需要游客状态的路由（登录页等）
  if (to.matched.some(record => record.meta.requiresGuest)) {
    console.log('[前端] 访问需要游客状态的路由')

    if (isAuthenticated && user.userType) {
      console.log('[前端] 已登录用户访问登录页')

      // 检查是否访问的是相同类型的登录页
      const targetUserType = to.meta.userType
      if (targetUserType === user.userType) {
        // 访问相同类型的登录页，重定向到对应主页
        const targetPath = `/${user.userType}/home`
        if (to.path !== targetPath) {
          console.log('[前端] 重定向到相同类型用户主页:', targetPath)
          return next(targetPath)
        }
      } else {
        // 访问不同类型的登录页，允许切换（清除当前登录状态）
        console.log('[前端] 用户尝试切换到不同类型登录页，清除当前认证信息')
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        // 清除store中的认证信息
        if (store && store.commit) {
          store.commit('CLEAR_AUTH')
        }
      }
    }
    console.log('[前端] 游客状态检查通过')
    return next()
  }

  // 其他路由直接通过
  console.log('[前端] 普通路由，直接通过')
  next()
})

export default router
