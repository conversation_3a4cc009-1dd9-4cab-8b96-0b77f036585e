<template>
  <div class="home-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <div class="hero-section">
      <div class="hero-content">
        <div class="title-section">
          <h1 class="main-title">
            <span class="title-line">永盛制冷维修</span>
            <span class="title-line">有限公司</span>
          </h1>
          <p class="subtitle">专业的家电售卖与维修服务管理平台</p>
          <div class="title-decoration"></div>
        </div>

        <div class="portal-cards">
          <div class="portal-card customer-card" @click="goToPortal('customer')">
            <div class="card-background"></div>
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-user"></i>
              </div>
              <h3>客户端</h3>
              <p>报修家电故障，查看维修进度</p>
              <div class="card-button">
                <span>进入客户端</span>
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>

          <div class="portal-card worker-card" @click="goToPortal('worker')">
            <div class="card-background"></div>
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-s-tools"></i>
              </div>
              <h3>工人端</h3>
              <p>接单维修，记录维修过程</p>
              <div class="card-button">
                <span>进入工人端</span>
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>

          <div class="portal-card admin-card" @click="goToPortal('admin')">
            <div class="card-background"></div>
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-s-platform"></i>
              </div>
              <h3>管理后台</h3>
              <p>管理工单，查看统计报表</p>
              <div class="card-button">
                <span>进入管理后台</span>
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="features-section">
      <div class="container">
        <div class="section-header">
          <h2>系统特色</h2>
          <p>为您提供全方位的家电维修服务解决方案</p>
        </div>
        <div class="features-grid">
          <div class="feature-item" v-for="(feature, index) in features" :key="index" :style="{ animationDelay: index * 0.1 + 's' }">
            <div class="feature-icon">
              <i :class="feature.icon"></i>
            </div>
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HomePage',
  data() {
    return {
      features: [
        {
          icon: 'el-icon-mobile-phone',
          title: '便捷报修',
          description: '客户可以快速提交维修申请，上传故障图片'
        },
        {
          icon: 'el-icon-time',
          title: '实时跟踪',
          description: '实时查看维修进度，掌握工单状态'
        },
        {
          icon: 'el-icon-s-cooperation',
          title: '高效派单',
          description: '智能匹配维修师傅，提高服务效率'
        },
        {
          icon: 'el-icon-data-analysis',
          title: '数据分析',
          description: '完整的数据统计和分析报表'
        }
      ]
    }
  },
  mounted() {
    console.log('[前端] HomePage组件已挂载')
    const user = this.$store.getters.currentUser
    console.log('[前端] 当前用户状态:', user)

    // 添加全局清除函数供调试使用
    window.clearAuth = () => {
      console.log('[前端] 手动清除认证信息')
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      this.$store.dispatch('logout')
      location.reload()
    }
    console.log('[前端] 可以在控制台使用 clearAuth() 清除认证信息')
  },
  methods: {
    goToPortal(type) {
      console.log('[前端] 点击进入端口:', type)
      // 检查是否已登录对应类型的账户
      const user = this.$store.getters.currentUser
      console.log('[前端] 当前用户:', user)

      if (user && user.userType === type) {
        console.log('[前端] 用户已登录相同类型，跳转到主页:', `/${type}/home`)
        this.$router.push(`/${type}/home`)
      } else {
        // 如果用户已登录但类型不同，或者未登录，都跳转到对应的登录页
        if (user && user.userType && user.userType !== type) {
          console.log('[前端] 用户已登录不同类型，先退出登录')
          // 清除当前登录状态
          this.$store.dispatch('logout')
        }
        console.log('[前端] 跳转到登录页:', `/${type}/login`)
        this.$router.push(`/${type}/login`)
      }
    }
  }
}
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  top: 30%;
  right: 30%;
  animation-delay: 4s;
}

.shape-4 {
  width: 120px;
  height: 120px;
  bottom: 20%;
  left: 20%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  color: white;
  padding: 120px 0;
  text-align: center;
  position: relative;
  z-index: 1;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.title-section {
  margin-bottom: 80px;
}

.main-title {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 30px;
  line-height: 1.2;
  color: #ffffff;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(255, 255, 255, 0.5),
    0 0 40px rgba(255, 255, 255, 0.3);
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  from {
    text-shadow:
      2px 2px 4px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(255, 255, 255, 0.5),
      0 0 40px rgba(255, 255, 255, 0.3);
  }
  to {
    text-shadow:
      2px 2px 6px rgba(0, 0, 0, 0.4),
      0 0 30px rgba(255, 255, 255, 0.8),
      0 0 60px rgba(255, 255, 255, 0.5);
  }
}

.title-line {
  display: block;
  animation: slideInUp 1s ease-out;
}

.title-line:nth-child(2) {
  animation-delay: 0.3s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.subtitle {
  font-size: 1.4rem;
  margin-bottom: 40px;
  opacity: 0.95;
  font-weight: 300;
  animation: fadeInUp 1s ease-out 0.6s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 0.95;
    transform: translateY(0);
  }
}

.title-decoration {
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, transparent, #ffffff, transparent);
  margin: 0 auto;
  border-radius: 2px;
  animation: fadeInUp 1s ease-out 0.9s both;
}

.portal-cards {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
  max-width: 1200px;
  margin: 0 auto;
  animation: fadeInUp 1s ease-out 1.2s both;
}

.portal-card {
  position: relative;
  min-width: 320px;
  height: 280px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 20px;
  overflow: hidden;
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  transition: all 0.4s ease;
}

.portal-card:hover .card-background {
  background: rgba(255, 255, 255, 0.98);
  transform: scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-content {
  position: relative;
  z-index: 2;
  padding: 40px 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.card-icon {
  font-size: 3.5rem;
  margin-bottom: 25px;
  transition: all 0.3s ease;
}

.customer-card .card-icon {
  color: #409EFF;
}

.worker-card .card-icon {
  color: #67C23A;
}

.admin-card .card-icon {
  color: #E6A23C;
}

.portal-card:hover .card-icon {
  transform: scale(1.1) rotateY(360deg);
}

.portal-card h3 {
  font-size: 1.6rem;
  margin-bottom: 15px;
  color: #303133;
  font-weight: 600;
}

.portal-card p {
  color: #606266;
  margin-bottom: 30px;
  line-height: 1.6;
  font-size: 0.95rem;
}

.card-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-top: auto;
}

.customer-card .card-button {
  background: linear-gradient(45deg, #409EFF, #66b3ff);
  color: white;
}

.worker-card .card-button {
  background: linear-gradient(45deg, #67C23A, #85ce61);
  color: white;
}

.admin-card .card-button {
  background: linear-gradient(45deg, #E6A23C, #ebb563);
  color: white;
}

.portal-card:hover .card-button {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.features-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-header h2 {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #303133;
  font-weight: 700;
  background: linear-gradient(45deg, #303133, #606266);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-header p {
  font-size: 1.2rem;
  color: #606266;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
}

.feature-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.4s ease;
  opacity: 0;
  transform: translateY(50px);
  animation: slideInUp 0.6s ease-out forwards;
}

.feature-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 25px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.feature-icon i {
  font-size: 2.5rem;
  color: white;
}

.feature-item:hover .feature-icon {
  transform: scale(1.1) rotateY(360deg);
}

.feature-item h4 {
  font-size: 1.4rem;
  margin-bottom: 15px;
  color: #303133;
  font-weight: 600;
}

.feature-item p {
  color: #606266;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .portal-cards {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }

  .portal-card {
    min-width: 280px;
    max-width: 350px;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .hero-section {
    padding: 80px 20px;
  }

  .features-section {
    padding: 60px 20px;
  }

  .section-header h2 {
    font-size: 2.2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
  }

  .portal-card {
    min-width: 260px;
    height: 260px;
  }

  .card-content {
    padding: 30px 20px;
  }

  .card-icon {
    font-size: 3rem;
  }

  .portal-card h3 {
    font-size: 1.4rem;
  }
}
</style>
