<template>
  <div class="admin-home">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="admin-header">
        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-btn" @click="toggleMobileMenu">
          <i class="el-icon-s-unfold" v-if="!mobileMenuOpen"></i>
          <i class="el-icon-s-fold" v-else></i>
        </div>

        <div class="header-left">
          <h2>永盛制冷维修有限公司-管理系统</h2>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <i class="el-icon-user-solid"></i>
              <span class="user-name">{{ currentUser?.name || '管理员' }}</span>
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 移动端遮罩层 -->
      <div class="mobile-overlay" v-if="mobileMenuOpen" @click="closeMobileMenu"></div>

      <el-container>
        <!-- 侧边栏 -->
        <el-aside width="200px" class="admin-sidebar" :class="{ 'mobile-open': mobileMenuOpen }">
          <el-menu
            :default-active="$route.path"
            router
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
            @select="handleMenuClick"
          >
            <el-menu-item index="/admin/home">
              <i class="el-icon-s-home"></i>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="/admin/orders">
              <i class="el-icon-document"></i>
              <span>工单管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/users">
              <i class="el-icon-user"></i>
              <span>用户管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/statistics">
              <i class="el-icon-s-data"></i>
              <span>统计报表</span>
            </el-menu-item>
            <el-menu-item index="/admin/salary">
              <i class="el-icon-money"></i>
              <span>薪资管理</span>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="admin-main">
          <!-- 欢迎区域 -->
          <div class="welcome-section">
            <div class="welcome-content">
              <h1>管理控制台</h1>
              <p>欢迎回来，{{ currentUser?.name || '管理员' }}！系统运行正常</p>
              <div class="welcome-decoration"></div>
            </div>
          </div>

          <!-- 工单统计 -->
          <div class="section-title">
            <h2>工单统计</h2>
          </div>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon total">
                <i class="el-icon-document"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.orders?.total_orders || 0 }}</div>
                <div class="stat-label">总工单数</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon pending">
                <i class="el-icon-time"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.orders?.pending_orders || 0 }}</div>
                <div class="stat-label">待处理工单</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon processing">
                <i class="el-icon-loading"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.orders?.accepted_orders || 0 }}</div>
                <div class="stat-label">处理中工单</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon completed">
                <i class="el-icon-check"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.orders?.completed_orders || 0 }}</div>
                <div class="stat-label">已完成工单</div>
              </div>
            </div>
          </div>

          <!-- 用户统计 -->
          <div class="section-title">
            <h2>用户统计</h2>
          </div>
          <div class="stats-grid user-stats">
            <div class="stat-card">
              <div class="stat-icon customers">
                <i class="el-icon-user"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.users?.total_customers || 0 }}</div>
                <div class="stat-label">注册客户</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon workers">
                <i class="el-icon-s-tools"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.users?.total_workers || 0 }}</div>
                <div class="stat-label">在职工人</div>
              </div>
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="section-title">
            <h2>快速操作</h2>
          </div>
          <div class="quick-actions">
            <div class="action-grid">
              <div class="action-card" @click="$router.push('/admin/orders')">
                <div class="action-icon">
                  <i class="el-icon-document"></i>
                </div>
                <h3>管理工单</h3>
                <p>查看和管理所有工单</p>
              </div>
              <div class="action-card" @click="$router.push('/admin/users')">
                <div class="action-icon">
                  <i class="el-icon-user"></i>
                </div>
                <h3>管理用户</h3>
                <p>管理客户和工人账户</p>
              </div>
              <div class="action-card" @click="$router.push('/admin/statistics')">
                <div class="action-icon">
                  <i class="el-icon-s-data"></i>
                </div>
                <h3>查看报表</h3>
                <p>查看详细统计报表</p>
              </div>
              <div class="action-card" @click="$router.push('/admin/salary')">
                <div class="action-icon">
                  <i class="el-icon-money"></i>
                </div>
                <h3>薪资管理</h3>
                <p>管理工人薪资结算</p>
              </div>
            </div>
          </div>

          <!-- 最近工单 -->
          <el-card class="recent-orders" header="最近工单">
            <el-table :data="recentOrders" style="width: 100%">
              <el-table-column prop="id" label="工单号" width="80"></el-table-column>
              <el-table-column prop="customer_name" label="客户" width="100"></el-table-column>
              <el-table-column prop="appliance_type" label="设备类型" width="120"></el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  <el-tag :type="getStatusType(scope.row.status)">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.created_at) }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'AdminHome',
  data() {
    return {
      statistics: {
        orders: {},
        users: {}
      },
      recentOrders: [],
      mobileMenuOpen: false
    }
  },
  computed: {
    currentUser() {
      return this.$store.state.user
    }
  },
  async created() {
    await this.loadStatistics()
    await this.loadRecentOrders()
  },

  mounted() {
    // 监听窗口大小变化，桌面端自动关闭移动端菜单
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    async loadStatistics() {
      try {
        const result = await this.$store.dispatch('fetchStatistics')
        if (result.success) {
          this.statistics = this.$store.state.statistics || {}
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    async loadRecentOrders() {
      try {
        const result = await this.$store.dispatch('fetchAllOrders')
        if (result.success) {
          // 获取最近5条工单
          this.recentOrders = (this.$store.state.orders || []).slice(0, 5)
        }
      } catch (error) {
        console.error('加载最近工单失败:', error)
      }
    },

    handleCommand(command) {
      if (command === 'logout') {
        this.logout()
      }
    },

    async logout() {
      try {
        await this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 使用store的logout action
        const result = await this.$store.dispatch('logout')

        if (result.success) {
          this.$message.success('退出登录成功')
          this.$router.push('/')
        }
      } catch (error) {
        // 用户取消退出
        console.log('用户取消退出登录')
      }
    },

    // 移动端菜单控制
    toggleMobileMenu() {
      this.mobileMenuOpen = !this.mobileMenuOpen
    },

    closeMobileMenu() {
      this.mobileMenuOpen = false
    },

    // 处理菜单项点击，移动端自动关闭菜单
    handleMenuClick() {
      if (window.innerWidth <= 768) {
        this.closeMobileMenu()
      }
    },

    // 处理窗口大小变化
    handleResize() {
      if (window.innerWidth > 768 && this.mobileMenuOpen) {
        this.closeMobileMenu()
      }
    },

    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'accepted': 'info',
        'in_progress': 'primary',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        'pending': '待接单',
        'accepted': '已接单',
        'in_progress': '维修中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.admin-home {
  height: 100vh;
}

.admin-header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.user-info {
  cursor: pointer;
  color: #606266;
}

.admin-sidebar {
  background: #304156;
}

.admin-main {
  background: #f0f2f5;
  padding: 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 30px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-content h1 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 2.2rem;
  font-weight: 700;
}

.welcome-content p {
  color: #606266;
  font-size: 16px;
  margin-bottom: 20px;
}

.welcome-decoration {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, transparent, #E6A23C, transparent);
  margin: 0 auto;
  border-radius: 2px;
}

.section-title {
  margin-bottom: 20px;
}

.section-title h2 {
  color: #303133;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.user-stats {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 25px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  transition: all 0.3s ease;
}

.stat-icon i {
  font-size: 28px;
  color: white;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1) rotateY(360deg);
}

.stat-icon.total {
  background: linear-gradient(135deg, #E6A23C, #F7BA2A);
}
.stat-icon.pending {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}
.stat-icon.processing {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}
.stat-icon.completed {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}
.stat-icon.customers {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}
.stat-icon.workers {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-content {
  flex: 1;
}

.stat-number {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.quick-actions {
  margin-bottom: 30px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.action-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 30px 25px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.action-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(230, 162, 60, 0.15);
}

.action-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #E6A23C, #F7BA2A);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-icon i {
  font-size: 2.5rem;
  color: white;
}

.action-card:hover .action-icon {
  transform: scale(1.1) rotateY(360deg);
  box-shadow: 0 8px 25px rgba(230, 162, 60, 0.3);
}

.action-card h3 {
  color: #303133;
  margin-bottom: 12px;
  font-size: 1.3rem;
  font-weight: 600;
}

.action-card p {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.recent-orders {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 25px;
  margin-bottom: 20px;
}

.recent-orders .el-card__header {
  background: transparent;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 0 15px 0;
}

.recent-orders h2 {
  color: #303133;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.user-info {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
  color: white;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-info i {
  margin: 0 4px;
}

/* 桌面端隐藏移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
}

.mobile-overlay {
  display: none;
}

/* 移动端优化样式 */
@media (max-width: 768px) {
  .admin-home {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }

  /* 移动端导航栏优化 */
  .admin-header {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1) !important;
    border-radius: 0 0 25px 25px;
    margin-bottom: 20px;
    padding: 15px 20px !important;
    position: relative;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  /* 移动端菜单按钮 */
  .mobile-menu-btn {
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    margin-right: 15px;
  }

  .mobile-menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
  }

  .mobile-menu-btn:active {
    transform: translateY(0);
  }

  .header-left {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .admin-header h2 {
    color: #2c3e50 !important;
    font-size: 16px !important;
    font-weight: 700;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }

  .header-right {
    flex-shrink: 0;
  }

  .user-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 25px !important;
    padding: 10px 16px !important;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .user-name {
    display: none;
  }

  /* 移动端遮罩层 */
  .mobile-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 999 !important;
    backdrop-filter: blur(5px);
    cursor: pointer;
    display: block !important;
  }

  /* 移动端侧边栏优化 */
  .admin-sidebar {
    position: fixed !important;
    top: 0;
    left: -250px;
    width: 250px !important;
    height: 100vh;
    background: rgba(48, 65, 86, 0.95) !important;
    backdrop-filter: blur(20px);
    z-index: 1001;
    transition: all 0.3s ease;
    border-radius: 0 25px 25px 0;
    box-shadow: 5px 0 25px rgba(0, 0, 0, 0.2);
  }

  .admin-sidebar.mobile-open {
    left: 0;
  }

  .admin-sidebar .el-menu {
    background: transparent !important;
    border: none !important;
    padding-top: 80px;
  }

  .admin-sidebar .el-menu-item {
    background: transparent !important;
    border-radius: 12px;
    margin: 5px 15px;
    transition: all 0.3s ease;
  }

  .admin-sidebar .el-menu-item:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    transform: translateX(5px);
  }

  .admin-sidebar .el-menu-item.is-active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  }

  .admin-sidebar .el-menu-item i {
    margin-right: 10px;
    font-size: 16px;
  }

  .main-content {
    padding: 0 15px 20px 15px !important;
    background: transparent !important;
  }

  .welcome-section {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border-radius: 20px !important;
    padding: 25px 20px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .welcome-section h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 24px !important;
    font-weight: 700;
    margin-bottom: 10px !important;
  }

  .welcome-section p {
    color: #6c757d !important;
    font-size: 14px !important;
  }

  /* 统计卡片优化 */
  .stats-row {
    margin-bottom: 25px !important;
  }

  .stat-card {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border-radius: 16px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 15px !important;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
  }

  .stat-card .el-card__body {
    padding: 20px !important;
  }

  .stat-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .stat-info h3 {
    font-size: 28px !important;
    font-weight: 700;
    margin: 0 0 5px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .stat-info p {
    font-size: 13px !important;
    color: #6c757d !important;
    margin: 0;
    font-weight: 500;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px !important;
    color: white;
  }

  .stat-icon.orders {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stat-icon.customers {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .stat-icon.workers {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  .stat-icon.revenue {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }

  /* 快速操作按钮优化 */
  .quick-actions {
    margin-bottom: 25px !important;
  }

  .action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  .action-buttons .el-button {
    height: 50px !important;
    border-radius: 12px !important;
    font-size: 14px !important;
    font-weight: 500;
    border: none !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .action-buttons .el-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  .action-buttons .el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  }

  .action-buttons .el-button--success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
  }

  .action-buttons .el-button--warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
  }

  .action-buttons .el-button--info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
  }

  /* 最近工单卡片优化 */
  .recent-orders .el-card {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border-radius: 16px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  }

  .recent-orders h2 {
    color: #2c3e50 !important;
    font-size: 18px !important;
    font-weight: 600;
    margin-bottom: 15px !important;
  }

  /* 表格优化 */
  .el-table {
    background: transparent !important;
    border-radius: 12px;
    overflow: hidden;
  }

  .el-table th {
    background: rgba(102, 126, 234, 0.1) !important;
    color: #2c3e50 !important;
    font-weight: 600;
    border: none !important;
  }

  .el-table td {
    border: none !important;
    background: rgba(255, 255, 255, 0.8) !important;
  }

  .el-table tr:hover td {
    background: rgba(102, 126, 234, 0.1) !important;
  }

  /* 标签优化 */
  .el-tag {
    border-radius: 20px !important;
    border: none !important;
    font-weight: 500;
    padding: 4px 12px !important;
  }
}
</style>
