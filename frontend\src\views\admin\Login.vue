<template>
  <div class="login-container admin-theme">
    <div class="login-form">
      <div class="login-header">
        <div class="back-btn">
          <el-button icon="el-icon-arrow-left" @click="$router.push('/')">返回首页</el-button>
        </div>
        <h2>管理员登录</h2>
        <p>永盛制冷维修有限公司-家电售卖与维修</p>
      </div>
      
      <el-form 
        ref="loginForm" 
        :model="loginForm" 
        :rules="loginRules" 
        label-width="0"
        @submit.native.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="el-icon-user"
            size="large"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="el-icon-lock"
            size="large"
            show-password
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="warning" 
            size="large" 
            style="width: 100%"
            :loading="$store.state.loading"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminLogin',
  data() {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async handleLogin() {
      try {
        await this.$refs.loginForm.validate()
        
        const result = await this.$store.dispatch('login', {
          credentials: this.loginForm,
          userType: 'admin'
        })
        
        if (result.success) {
          this.$message.success('登录成功')
          this.$router.push('/admin/home')
        } else {
          this.$message.error(result.message)
        }
      } catch (error) {
        console.error('登录失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #E6A23C 0%, #f0c78a 100%);
}

.login-form {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .login-container {
    padding: 15px;
    align-items: flex-start;
    padding-top: 8vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
  }

  .login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
  }

  .login-form {
    width: 100%;
    max-width: 380px;
    padding: 35px 25px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 1;
  }

  .login-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    border-radius: 20px;
    pointer-events: none;
  }

  .back-btn {
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 12px !important;
    color: white !important;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .login-header {
    text-align: center;
    margin-bottom: 30px;
  }

  .login-header h1 {
    font-size: 24px !important;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
  }

  .login-header p {
    font-size: 14px !important;
    color: #6c757d;
    font-weight: 500;
  }

  .el-form-item {
    margin-bottom: 20px !important;
  }

  .el-input__inner {
    height: 50px !important;
    border-radius: 12px !important;
    border: 2px solid #e9ecef !important;
    font-size: 16px !important;
    padding: 0 20px !important;
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .el-input__inner:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    background: white !important;
  }

  .el-input__prefix {
    left: 15px !important;
  }

  .el-input__prefix .el-input__icon {
    color: #6c757d;
    font-size: 18px;
  }

  .login-btn {
    width: 100% !important;
    height: 50px !important;
    font-size: 16px !important;
    font-weight: 600;
    border-radius: 12px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
    transition: all 0.3s ease;
    margin-top: 10px;
  }

  .login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5) !important;
  }

  .login-btn:active {
    transform: translateY(0);
  }

  /* 表单标签优化 */
  .el-form-item__label {
    color: #495057 !important;
    font-weight: 600 !important;
    font-size: 14px !important;
  }

  /* 错误提示优化 */
  .el-form-item__error {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    padding: 5px 10px;
    border-radius: 8px;
    font-size: 12px;
    margin-top: 5px;
  }
}
</style>

.back-btn {
  position: absolute;
  top: 20px;
  left: 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
  padding-top: 20px;
}

.login-header h2 {
  color: #303133;
  margin-bottom: 10px;
}

.login-header p {
  color: #909399;
  font-size: 14px;
}
</style>
