<template>
  <div class="admin-orders">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="admin-header">
        <div class="header-left">
          <h2>工单管理</h2>
        </div>
        <div class="header-right">
          <el-button @click="$router.push('/admin/home')">返回首页</el-button>
        </div>
      </el-header>

      <!-- 主内容区 -->
      <el-main class="admin-main">
        <!-- 搜索和筛选 -->
        <el-card class="search-card" shadow="never">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item label="工单状态">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option label="待接单" value="pending"></el-option>
                <el-option label="已接单" value="accepted"></el-option>
                <el-option label="维修中" value="in_progress"></el-option>
                <el-option label="已完成" value="completed"></el-option>
                <el-option label="已取消" value="cancelled"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="设备类型">
              <el-select v-model="searchForm.appliance_type" placeholder="请选择设备类型" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option label="空调" value="air_conditioner"></el-option>
                <el-option label="洗衣机" value="washing_machine"></el-option>
                <el-option label="冰箱" value="refrigerator"></el-option>
                <el-option label="热水器" value="water_heater"></el-option>
                <el-option label="电视" value="television"></el-option>
                <el-option label="微波炉" value="microwave"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="客户姓名">
              <el-input v-model="searchForm.customer_name" placeholder="请输入客户姓名" clearable></el-input>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="searchOrders">搜索</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 工单列表 -->
        <el-card class="orders-card">
          <div slot="header" class="card-header">
            <span>工单列表 (共 {{ filteredOrders.length }} 条)</span>
            <el-button type="primary" size="small" @click="refreshOrders">刷新</el-button>
          </div>

          <el-table
            :data="paginatedOrders"
            style="width: 100%"
            v-loading="$store.state.loading"
            @row-click="showOrderDetail"
          >
            <el-table-column prop="id" label="工单号" width="80" sortable></el-table-column>
            <el-table-column prop="customer_name" label="客户" width="100"></el-table-column>
            <el-table-column prop="customer_phone" label="客户电话" width="120"></el-table-column>
            <el-table-column prop="appliance_type" label="设备类型" width="100">
              <template slot-scope="scope">
                {{ getApplianceTypeName(scope.row.appliance_type) }}
              </template>
            </el-table-column>
            <el-table-column prop="brand_model" label="品牌型号" width="150" show-overflow-tooltip></el-table-column>
            <el-table-column prop="worker_name" label="维修工人" width="100">
              <template slot-scope="scope">
                {{ scope.row.worker_name || '未分配' }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="urgency" label="紧急程度" width="100">
              <template slot-scope="scope">
                <el-tag :type="getUrgencyType(scope.row.urgency)" size="small">
                  {{ getUrgencyText(scope.row.urgency) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="150">
              <template slot-scope="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click.stop="showOrderDetail(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="filteredOrders.length">
            </el-pagination>
          </div>
        </el-card>
      </el-main>
    </el-container>

    <!-- 工单详情对话框 -->
    <el-dialog
      title="工单详情"
      :visible.sync="detailVisible"
      width="800px"
      @close="selectedOrder = null"
    >
      <div v-if="selectedOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工单号">{{ selectedOrder.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedOrder.status)">
              {{ getStatusText(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="客户姓名">{{ selectedOrder.customer_name }}</el-descriptions-item>
          <el-descriptions-item label="客户电话">{{ selectedOrder.customer_phone }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">{{ getApplianceTypeName(selectedOrder.appliance_type) }}</el-descriptions-item>
          <el-descriptions-item label="品牌型号">{{ selectedOrder.brand_model }}</el-descriptions-item>
          <el-descriptions-item label="维修工人">{{ selectedOrder.worker_name || '未分配' }}</el-descriptions-item>
          <el-descriptions-item label="工人电话">{{ selectedOrder.worker_phone || '未分配' }}</el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            <el-tag :type="getUrgencyType(selectedOrder.urgency)" size="small">
              {{ getUrgencyText(selectedOrder.urgency) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="预约日期">{{ selectedOrder.preferred_date }}</el-descriptions-item>
          <el-descriptions-item label="预约时间">{{ getTimeText(selectedOrder.preferred_time) }}</el-descriptions-item>
          <el-descriptions-item label="购买日期">{{ selectedOrder.purchase_date }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedOrder.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="接单时间">{{ formatDate(selectedOrder.accepted_at) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDate(selectedOrder.completed_at) }}</el-descriptions-item>
          <el-descriptions-item label="评分">
            <el-rate v-if="selectedOrder.rating" :value="selectedOrder.rating" disabled></el-rate>
            <span v-else>未评分</span>
          </el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>故障描述</h4>
          <p>{{ selectedOrder.problem_description }}</p>
        </div>

        <div class="detail-section" v-if="selectedOrder.address">
          <h4>服务地址</h4>
          <p>{{ selectedOrder.address }}</p>
        </div>

        <div class="detail-section" v-if="selectedOrder.remarks">
          <h4>备注信息</h4>
          <p>{{ selectedOrder.remarks }}</p>
        </div>

        <div class="detail-section" v-if="selectedOrder.comment">
          <h4>客户评价</h4>
          <p>{{ selectedOrder.comment }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AdminOrders',
  data() {
    return {
      orders: [],
      searchForm: {
        status: '',
        appliance_type: '',
        customer_name: ''
      },
      currentPage: 1,
      pageSize: 20,
      detailVisible: false,
      selectedOrder: null
    }
  },
  computed: {
    filteredOrders() {
      let filtered = this.orders

      if (this.searchForm.status) {
        filtered = filtered.filter(order => order.status === this.searchForm.status)
      }

      if (this.searchForm.appliance_type) {
        filtered = filtered.filter(order => order.appliance_type === this.searchForm.appliance_type)
      }

      if (this.searchForm.customer_name) {
        filtered = filtered.filter(order =>
          order.customer_name.includes(this.searchForm.customer_name)
        )
      }

      return filtered
    },

    paginatedOrders() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredOrders.slice(start, end)
    }
  },
  async created() {
    await this.loadOrders()
  },
  methods: {
    async loadOrders() {
      try {
        const result = await this.$store.dispatch('fetchAllOrders')
        if (result.success) {
          this.orders = this.$store.state.orders || []
        } else {
          this.$message.error(result.message || '获取工单列表失败')
        }
      } catch (error) {
        console.error('加载工单失败:', error)
        this.$message.error('加载工单失败')
      }
    },

    async refreshOrders() {
      await this.loadOrders()
      this.$message.success('刷新成功')
    },

    searchOrders() {
      this.currentPage = 1
    },

    resetSearch() {
      this.searchForm = {
        status: '',
        appliance_type: '',
        customer_name: ''
      }
      this.currentPage = 1
    },

    showOrderDetail(order) {
      this.selectedOrder = order
      this.detailVisible = true
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    handleCurrentChange(val) {
      this.currentPage = val
    },

    getApplianceTypeName(type) {
      const typeMap = {
        'air_conditioner': '空调',
        'washing_machine': '洗衣机',
        'refrigerator': '冰箱',
        'water_heater': '热水器',
        'television': '电视',
        'microwave': '微波炉'
      }
      return typeMap[type] || type
    },

    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'accepted': 'info',
        'in_progress': 'primary',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        'pending': '待接单',
        'accepted': '已接单',
        'in_progress': '维修中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    },

    getUrgencyType(urgency) {
      const urgencyMap = {
        'low': 'info',
        'medium': 'warning',
        'high': 'danger'
      }
      return urgencyMap[urgency] || 'info'
    },

    getUrgencyText(urgency) {
      const urgencyMap = {
        'low': '低',
        'medium': '中',
        'high': '高'
      }
      return urgencyMap[urgency] || urgency
    },

    getTimeText(time) {
      const timeMap = {
        'morning': '上午',
        'afternoon': '下午',
        'evening': '晚上'
      }
      return timeMap[time] || time
    },

    formatDate(dateString) {
      if (!dateString) return '未设置'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.admin-orders {
  height: 100vh;
}

.admin-header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.admin-main {
  background: #f0f2f5;
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.orders-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.order-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: #303133;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.detail-section p {
  margin: 0;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
}
</style>
