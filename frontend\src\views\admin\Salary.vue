<template>
  <div class="admin-salary">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="admin-header">
        <div class="header-left">
          <h2>薪资管理</h2>
        </div>
        <div class="header-right">
          <el-button @click="$router.push('/admin/home')">返回首页</el-button>
        </div>
      </el-header>

      <!-- 主内容区 -->
      <el-main class="admin-main">
        <!-- 操作区域 -->
        <el-card class="operation-card" shadow="never">
          <div class="operation-row">
            <div class="date-selector">
              <el-date-picker
                v-model="selectedMonth"
                type="month"
                placeholder="选择月份"
                format="yyyy年MM月"
                value-format="yyyy-MM"
                @change="handleMonthChange"
              >
              </el-date-picker>
            </div>
            
            <div class="actions">
              <el-button type="primary" @click="calculateAllSalaries" :loading="calculating">
                <i class="el-icon-calculator"></i>
                计算本月薪资
              </el-button>
              <el-button type="success" @click="exportSalaries">
                <i class="el-icon-download"></i>
                导出薪资表
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 薪资汇总 -->
        <el-card class="summary-card" header="薪资汇总">
          <div class="summary-stats">
            <div class="stat-item">
              <div class="stat-value">{{ summary.totalWorkers }}</div>
              <div class="stat-label">工人总数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">¥{{ formatMoney(summary.totalSalary) }}</div>
              <div class="stat-label">薪资总额</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">¥{{ formatMoney(summary.totalRevenue) }}</div>
              <div class="stat-label">营收总额</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ summary.totalOrders }}</div>
              <div class="stat-label">完成工单</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ (parseFloat(summary.avgRating) || 0).toFixed(1) }}</div>
              <div class="stat-label">平均评分</div>
            </div>
          </div>
        </el-card>

        <!-- 薪资列表 -->
        <el-card class="salary-list-card">
          <div slot="header" class="card-header">
            <span>{{ selectedMonth }} 薪资明细</span>
            <el-button type="primary" size="small" @click="loadSalarySummary">刷新</el-button>
          </div>
          
          <el-table 
            :data="salaryList" 
            style="width: 100%"
            v-loading="loading"
          >
            <el-table-column prop="worker_name" label="工人姓名" width="100"></el-table-column>
            <el-table-column prop="worker_phone" label="联系电话" width="120"></el-table-column>
            <el-table-column prop="completed_orders" label="完成工单" width="100" sortable></el-table-column>
            <el-table-column prop="total_revenue" label="营收金额" width="120" sortable>
              <template slot-scope="scope">
                ¥{{ formatMoney(scope.row.total_revenue) }}
              </template>
            </el-table-column>
            <el-table-column prop="base_salary" label="基础工资" width="120">
              <template slot-scope="scope">
                ¥{{ formatMoney(scope.row.base_salary) }}
              </template>
            </el-table-column>
            <el-table-column prop="commission_amount" label="提成收入" width="120">
              <template slot-scope="scope">
                ¥{{ formatMoney(scope.row.commission_amount) }}
              </template>
            </el-table-column>
            <el-table-column prop="performance_bonus" label="绩效奖金" width="120">
              <template slot-scope="scope">
                ¥{{ formatMoney(scope.row.performance_bonus) }}
              </template>
            </el-table-column>
            <el-table-column prop="attendance_bonus" label="全勤奖" width="100">
              <template slot-scope="scope">
                ¥{{ formatMoney(scope.row.attendance_bonus) }}
              </template>
            </el-table-column>
            <el-table-column prop="total_salary" label="总薪资" width="120" sortable>
              <template slot-scope="scope">
                <span class="total-salary">¥{{ formatMoney(scope.row.total_salary) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="avg_rating" label="平均评分" width="100">
              <template slot-scope="scope">
                <el-rate :value="parseFloat(scope.row.avg_rating) || 0" disabled show-score></el-rate>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="viewSalaryDetail(scope.row)">
                  查看详情
                </el-button>
                <el-button 
                  type="text" 
                  size="small" 
                  @click="calculateSingleSalary(scope.row.worker_id)"
                  :loading="calculating"
                >
                  重新计算
                </el-button>
                <el-button 
                  v-if="scope.row.status === 'draft'"
                  type="text" 
                  size="small" 
                  @click="confirmSalary(scope.row.id)"
                >
                  确认发放
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-main>
    </el-container>

    <!-- 薪资详情对话框 -->
    <el-dialog
      title="薪资详情"
      :visible.sync="detailVisible"
      width="800px"
      @close="selectedSalary = null"
    >
      <div v-if="selectedSalary" class="salary-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工人姓名">{{ selectedSalary.worker_name }}</el-descriptions-item>
          <el-descriptions-item label="计算月份">{{ selectedSalary.year }}年{{ selectedSalary.month }}月</el-descriptions-item>
          <el-descriptions-item label="完成工单">{{ selectedSalary.completed_orders }}单</el-descriptions-item>
          <el-descriptions-item label="营收金额">¥{{ formatMoney(selectedSalary.total_revenue) }}</el-descriptions-item>
          <el-descriptions-item label="出勤天数">{{ selectedSalary.attendance_days }}天</el-descriptions-item>
          <el-descriptions-item label="平均评分">
            <el-rate :value="parseFloat(selectedSalary.avg_rating) || 0" disabled show-score></el-rate>
          </el-descriptions-item>
          <el-descriptions-item label="计算时间">{{ formatDate(selectedSalary.calculated_at) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedSalary.status)">
              {{ getStatusText(selectedSalary.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="salary-breakdown">
          <h4>薪资构成</h4>
          <el-table :data="salaryDetails" style="width: 100%">
            <el-table-column prop="item_name" label="项目" width="150"></el-table-column>
            <el-table-column prop="amount" label="金额" width="120">
              <template slot-scope="scope">
                ¥{{ formatMoney(scope.row.amount) }}
              </template>
            </el-table-column>
            <el-table-column prop="description" label="说明"></el-table-column>
          </el-table>
          
          <div class="total-row">
            <strong>总计：¥{{ formatMoney(selectedSalary.total_salary) }}</strong>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'AdminSalary',
  data() {
    return {
      selectedMonth: this.getCurrentMonth(),
      loading: false,
      calculating: false,
      summary: {
        totalWorkers: 0,
        totalSalary: 0,
        totalRevenue: 0,
        totalOrders: 0,
        avgRating: 0
      },
      salaryList: [],
      detailVisible: false,
      selectedSalary: null,
      salaryDetails: []
    }
  },
  async created() {
    await this.loadSalarySummary()
  },
  methods: {
    getCurrentMonth() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      return `${year}-${month}`
    },

    async handleMonthChange() {
      await this.loadSalarySummary()
    },

    async loadSalarySummary() {
      if (!this.selectedMonth) return

      try {
        this.loading = true
        const [year, month] = this.selectedMonth.split('-')

        const response = await axios.get(`/api/salary/summary/${year}/${month}`)

        if (response.success) {
          this.summary = response.data.summary
          this.salaryList = response.data.salaries
        } else {
          this.$message.error(response.message || '获取薪资数据失败')
        }
      } catch (error) {
        console.error('获取薪资汇总失败:', error)
        this.$message.error('获取薪资数据失败')
      } finally {
        this.loading = false
      }
    },

    async calculateAllSalaries() {
      if (!this.selectedMonth) {
        this.$message.warning('请先选择月份')
        return
      }

      try {
        await this.$confirm('确定要计算所有工人的薪资吗？这将覆盖已有的计算结果。', '确认计算', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.calculating = true
        const [year, month] = this.selectedMonth.split('-')

        // 获取所有工人列表
        const workersResponse = await axios.get('/api/admin/users/workers')
        if (!workersResponse.success) {
          throw new Error('获取工人列表失败')
        }

        const workers = workersResponse.data
        let successCount = 0
        let failCount = 0

        // 为每个工人计算薪资
        for (const worker of workers) {
          try {
            await axios.post(`/api/salary/calculate/${worker.id}/${year}/${month}`)
            successCount++
          } catch (error) {
            console.error(`计算工人 ${worker.name} 薪资失败:`, error)
            failCount++
          }
        }

        this.$message.success(`薪资计算完成！成功：${successCount}人，失败：${failCount}人`)
        await this.loadSalarySummary()

      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量计算薪资失败:', error)
          this.$message.error('批量计算薪资失败')
        }
      } finally {
        this.calculating = false
      }
    },

    async calculateSingleSalary(workerId) {
      if (!this.selectedMonth) {
        this.$message.warning('请先选择月份')
        return
      }

      try {
        this.calculating = true
        const [year, month] = this.selectedMonth.split('-')

        const response = await axios.post(`/api/salary/calculate/${workerId}/${year}/${month}`)

        if (response.success) {
          this.$message.success('薪资计算成功')
          await this.loadSalarySummary()
        } else {
          this.$message.error(response.message || '薪资计算失败')
        }
      } catch (error) {
        console.error('计算薪资失败:', error)
        this.$message.error('薪资计算失败')
      } finally {
        this.calculating = false
      }
    },

    async viewSalaryDetail(salary) {
      try {
        this.loading = true

        const response = await axios.get(`/api/salary/detail/${salary.id}`)

        if (response.success) {
          this.selectedSalary = response.data.salary
          this.salaryDetails = response.data.details
          this.detailVisible = true
        } else {
          this.$message.error(response.message || '获取薪资详情失败')
        }
      } catch (error) {
        console.error('获取薪资详情失败:', error)
        this.$message.error('获取薪资详情失败')
      } finally {
        this.loading = false
      }
    },

    async confirmSalary(salaryId) {
      try {
        await this.$confirm('确定要确认发放这笔薪资吗？', '确认发放', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 这里可以添加确认发放的API调用
        this.$message.success('薪资确认发放成功')
        await this.loadSalarySummary()

      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('确认发放失败')
        }
      }
    },

    exportSalaries() {
      this.$message.info('导出功能开发中...')
    },

    getStatusType(status) {
      const statusMap = {
        'draft': 'info',
        'confirmed': 'warning',
        'paid': 'success'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        'draft': '草稿',
        'confirmed': '已确认',
        'paid': '已发放'
      }
      return statusMap[status] || status
    },

    formatMoney(amount) {
      if (!amount) return '0.00'
      return parseFloat(amount).toFixed(2)
    },

    formatDate(dateString) {
      if (!dateString) return '未设置'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.admin-salary {
  height: 100vh;
}

.admin-header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.admin-main {
  background: #f0f2f5;
  padding: 20px;
}

.operation-card {
  margin-bottom: 20px;
}

.operation-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-selector {
  flex: 1;
}

.actions {
  display: flex;
  gap: 10px;
}

.summary-card {
  margin-bottom: 20px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.salary-list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-salary {
  font-weight: bold;
  color: #E6A23C;
}

.salary-detail {
  max-height: 600px;
  overflow-y: auto;
}

.salary-breakdown {
  margin-top: 20px;
}

.salary-breakdown h4 {
  margin-bottom: 15px;
  color: #303133;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.total-row {
  margin-top: 15px;
  text-align: right;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 16px;
}

@media (max-width: 768px) {
  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .operation-row {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
}
</style>
