<template>
  <div class="admin-statistics">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="admin-header">
        <div class="header-left">
          <h2>统计报表</h2>
        </div>
        <div class="header-right">
          <el-button @click="$router.push('/admin/home')">返回首页</el-button>
        </div>
      </el-header>

      <!-- 主内容区 -->
      <el-main class="admin-main">
        <!-- 时间筛选 -->
        <el-card class="filter-card" shadow="never">
          <el-form :inline="true" :model="filterForm" class="filter-form">
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                @change="handleDateChange"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="loadStatistics">查询</el-button>
              <el-button @click="resetFilter">重置</el-button>
              <el-button type="success" @click="exportReport">导出报表</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 概览统计 -->
        <div class="stats-overview">
          <div class="stat-card">
            <div class="stat-icon total">
              <i class="el-icon-document"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.orders?.total_orders || 0 }}</h3>
              <p>总工单数</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon completed">
              <i class="el-icon-check"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.orders?.completed_orders || 0 }}</h3>
              <p>已完成工单</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon revenue">
              <i class="el-icon-money"></i>
            </div>
            <div class="stat-content">
              <h3>¥{{ statistics.revenue?.total_revenue || 0 }}</h3>
              <p>总收入</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon customers">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.users?.total_customers || 0 }}</h3>
              <p>注册客户</p>
            </div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-container">
          <!-- 工单状态分布 -->
          <el-card class="chart-card" header="工单状态分布">
            <div class="chart-placeholder">
              <el-table :data="orderStatusData" style="width: 100%">
                <el-table-column prop="status" label="状态">
                  <template slot-scope="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                      {{ getStatusText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="count" label="数量" sortable></el-table-column>
                <el-table-column prop="percentage" label="占比">
                  <template slot-scope="scope">
                    {{ scope.row.percentage }}%
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>

          <!-- 设备类型统计 -->
          <el-card class="chart-card" header="设备类型统计">
            <div class="chart-placeholder">
              <el-table :data="applianceTypeData" style="width: 100%">
                <el-table-column prop="type" label="设备类型">
                  <template slot-scope="scope">
                    {{ getApplianceTypeName(scope.row.type) }}
                  </template>
                </el-table-column>
                <el-table-column prop="count" label="维修次数" sortable></el-table-column>
                <el-table-column prop="percentage" label="占比">
                  <template slot-scope="scope">
                    {{ scope.row.percentage }}%
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </div>

        <!-- 工人绩效统计 -->
        <el-card class="performance-card" header="工人绩效统计">
          <el-table :data="workerPerformance" style="width: 100%">
            <el-table-column prop="name" label="工人姓名" width="100"></el-table-column>
            <el-table-column prop="total_orders" label="总工单数" width="100" sortable></el-table-column>
            <el-table-column prop="completed_orders" label="已完成" width="100" sortable></el-table-column>
            <el-table-column prop="completion_rate" label="完成率" width="100">
              <template slot-scope="scope">
                <el-progress
                  :percentage="scope.row.completion_rate"
                  :color="getProgressColor(scope.row.completion_rate)"
                ></el-progress>
              </template>
            </el-table-column>
            <el-table-column prop="avg_rating" label="平均评分" width="120">
              <template slot-scope="scope">
                <el-rate :value="parseFloat(scope.row.avg_rating) || 0" disabled show-score></el-rate>
              </template>
            </el-table-column>
            <el-table-column prop="total_revenue" label="总收入" width="100" sortable>
              <template slot-scope="scope">
                ¥{{ scope.row.total_revenue || 0 }}
              </template>
            </el-table-column>
            <el-table-column prop="commission" label="提成" width="100">
              <template slot-scope="scope">
                ¥{{ scope.row.commission || 0 }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 月度趋势 -->
        <el-card class="trend-card" header="月度工单趋势">
          <div class="trend-placeholder">
            <el-table :data="monthlyTrend" style="width: 100%">
              <el-table-column prop="month" label="月份" width="100"></el-table-column>
              <el-table-column prop="total_orders" label="总工单" width="100"></el-table-column>
              <el-table-column prop="completed_orders" label="已完成" width="100"></el-table-column>
              <el-table-column prop="revenue" label="收入" width="100">
                <template slot-scope="scope">
                  ¥{{ scope.row.revenue || 0 }}
                </template>
              </el-table-column>
              <el-table-column prop="growth_rate" label="增长率" width="100">
                <template slot-scope="scope">
                  <span :class="scope.row.growth_rate >= 0 ? 'positive' : 'negative'">
                    {{ scope.row.growth_rate >= 0 ? '+' : '' }}{{ scope.row.growth_rate }}%
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'AdminStatistics',
  data() {
    return {
      filterForm: {
        dateRange: []
      },
      statistics: {
        orders: {},
        users: {},
        revenue: {}
      },
      orderStatusData: [],
      applianceTypeData: [],
      workerPerformance: [],
      monthlyTrend: []
    }
  },
  async created() {
    await this.loadStatistics()
  },
  methods: {
    async loadStatistics() {
      try {
        // 加载基础统计数据
        const result = await this.$store.dispatch('fetchStatistics')
        if (result.success) {
          this.statistics = this.$store.state.statistics || {}
        }

        // 加载详细统计数据
        await this.loadDetailedStats()
      } catch (error) {
        console.error('加载统计数据失败:', error)
        this.$message.error('加载统计数据失败')
      }
    },

    async loadDetailedStats() {
      try {
        this.loading = true

        // 获取工单状态分布
        await this.loadOrderStatusData()

        // 获取设备类型统计
        await this.loadApplianceTypeData()

        // 获取工人绩效统计
        await this.loadWorkerPerformanceData()

        // 获取月度趋势
        await this.loadMonthlyTrendData()

      } catch (error) {
        console.error('加载详细统计失败:', error)
        this.$message.error('加载统计数据失败')
      } finally {
        this.loading = false
      }
    },

    async loadOrderStatusData() {
      try {
        const result = await this.$store.dispatch('fetchOrderStatusStats')
        if (result.success) {
          const data = result.data
          const totalOrders = data.total || 0

          this.orderStatusData = [
            {
              status: 'pending',
              count: data.pending || 0,
              percentage: totalOrders ? Math.round((data.pending || 0) / totalOrders * 100) : 0
            },
            {
              status: 'accepted',
              count: data.accepted || 0,
              percentage: totalOrders ? Math.round((data.accepted || 0) / totalOrders * 100) : 0
            },
            {
              status: 'in_progress',
              count: data.in_progress || 0,
              percentage: totalOrders ? Math.round((data.in_progress || 0) / totalOrders * 100) : 0
            },
            {
              status: 'completed',
              count: data.completed || 0,
              percentage: totalOrders ? Math.round((data.completed || 0) / totalOrders * 100) : 0
            },
            {
              status: 'cancelled',
              count: data.cancelled || 0,
              percentage: totalOrders ? Math.round((data.cancelled || 0) / totalOrders * 100) : 0
            }
          ]
        }
      } catch (error) {
        console.error('获取工单状态统计失败:', error)
      }
    },

    async loadApplianceTypeData() {
      try {
        const result = await this.$store.dispatch('fetchApplianceTypeStats')
        if (result.success) {
          this.applianceTypeData = result.data || []
        }
      } catch (error) {
        console.error('获取设备类型统计失败:', error)
      }
    },

    async loadWorkerPerformanceData() {
      try {
        const result = await this.$store.dispatch('fetchWorkerPerformanceStats')
        if (result.success) {
          this.workerPerformance = result.data || []
        }
      } catch (error) {
        console.error('获取工人绩效统计失败:', error)
      }
    },

    async loadMonthlyTrendData() {
      try {
        const result = await this.$store.dispatch('fetchMonthlyTrendStats')
        if (result.success) {
          this.monthlyTrend = result.data || []
        }
      } catch (error) {
        console.error('获取月度趋势统计失败:', error)
      }
    },

    handleDateChange() {
      // 处理日期范围变化
      this.loadStatistics()
    },

    resetFilter() {
      this.filterForm.dateRange = []
      this.loadStatistics()
    },

    exportReport() {
      // 导出报表功能
      this.$message.info('导出功能开发中...')
    },

    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'accepted': 'info',
        'in_progress': 'primary',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        'pending': '待接单',
        'accepted': '已接单',
        'in_progress': '维修中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    },

    getApplianceTypeName(type) {
      const typeMap = {
        'air_conditioner': '空调',
        'washing_machine': '洗衣机',
        'refrigerator': '冰箱',
        'water_heater': '热水器',
        'television': '电视',
        'microwave': '微波炉'
      }
      return typeMap[type] || type
    },

    getProgressColor(percentage) {
      if (percentage >= 90) return '#67c23a'
      if (percentage >= 70) return '#e6a23c'
      return '#f56c6c'
    }
  }
}
</script>

<style scoped>
.admin-statistics {
  height: 100vh;
}

.admin-header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.admin-main {
  background: #f0f2f5;
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.total { background: #409EFF; }
.stat-icon.completed { background: #67C23A; }
.stat-icon.revenue { background: #E6A23C; }
.stat-icon.customers { background: #909399; }

.stat-content h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
}

.stat-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-placeholder {
  height: 300px;
  overflow-y: auto;
}

.performance-card {
  margin-bottom: 20px;
}

.trend-card {
  margin-bottom: 20px;
}

.trend-placeholder {
  height: 300px;
  overflow-y: auto;
}

.positive {
  color: #67c23a;
}

.negative {
  color: #f56c6c;
}

@media (max-width: 768px) {
  .charts-container {
    grid-template-columns: 1fr;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
