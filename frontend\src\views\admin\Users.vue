<template>
  <div class="admin-users">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="admin-header">
        <div class="header-left">
          <h2>用户管理</h2>
        </div>
        <div class="header-right">
          <el-button @click="$router.push('/admin/home')">返回首页</el-button>
        </div>
      </el-header>

      <!-- 主内容区 -->
      <el-main class="admin-main">
        <!-- 用户类型切换 -->
        <el-card class="user-type-card" shadow="never">
          <el-radio-group v-model="activeUserType" @change="handleUserTypeChange">
            <el-radio-button label="customers">客户管理</el-radio-button>
            <el-radio-button label="workers">工人管理</el-radio-button>
            <el-radio-button label="admins">管理员管理</el-radio-button>
          </el-radio-group>
        </el-card>

        <!-- 搜索和操作 -->
        <el-card class="search-card" shadow="never">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item label="用户名">
              <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable></el-input>
            </el-form-item>

            <el-form-item label="姓名">
              <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable></el-input>
            </el-form-item>

            <el-form-item label="状态" v-if="activeUserType === 'workers'">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option label="在职" value="active"></el-option>
                <el-option label="离职" value="inactive"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="searchUsers">搜索</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <el-button type="success" @click="showAddDialog" v-if="activeUserType === 'workers'">添加工人</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 用户列表 -->
        <el-card class="users-card">
          <div slot="header" class="card-header">
            <span>{{ getUserTypeText() }}列表 (共 {{ filteredUsers.length }} 条)</span>
            <el-button type="primary" size="small" @click="refreshUsers">刷新</el-button>
          </div>

          <el-table
            :data="paginatedUsers"
            style="width: 100%"
            v-loading="loading"
          >
            <el-table-column prop="id" label="ID" width="60" sortable></el-table-column>
            <el-table-column prop="username" label="用户名" width="120"></el-table-column>
            <el-table-column prop="name" label="姓名" width="100"></el-table-column>
            <el-table-column prop="phone" label="电话" width="130"></el-table-column>

            <!-- 客户特有字段 -->
            <el-table-column v-if="activeUserType === 'customers'" prop="address" label="地址" show-overflow-tooltip></el-table-column>

            <!-- 工人特有字段 -->
            <template v-if="activeUserType === 'workers'">
              <el-table-column prop="skills" label="技能" width="200" show-overflow-tooltip></el-table-column>
              <el-table-column prop="commission_rate" label="提成比例" width="100">
                <template slot-scope="scope">
                  {{ (scope.row.commission_rate * 100).toFixed(0) }}%
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                    {{ scope.row.status === 'active' ? '在职' : '离职' }}
                  </el-tag>
                </template>
              </el-table-column>
            </template>

            <!-- 管理员特有字段 -->
            <template v-if="activeUserType === 'admins'">
              <el-table-column prop="role" label="角色" width="100">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.role === 'super_admin' ? 'danger' : 'primary'">
                    {{ scope.row.role === 'super_admin' ? '超级管理员' : '管理员' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="last_login" label="最后登录" width="150">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.last_login) }}
                </template>
              </el-table-column>
            </template>

            <el-table-column prop="created_at" label="注册时间" width="150">
              <template slot-scope="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="showUserDetail(scope.row)">
                  查看详情
                </el-button>
                <el-button
                  v-if="activeUserType === 'workers'"
                  type="text"
                  size="small"
                  @click="editUser(scope.row)"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="activeUserType === 'workers'"
                  type="text"
                  size="small"
                  style="color: #f56c6c;"
                  @click="deleteUser(scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="filteredUsers.length">
            </el-pagination>
          </div>
        </el-card>
      </el-main>
    </el-container>

    <!-- 用户详情对话框 -->
    <el-dialog
      title="用户详情"
      :visible.sync="detailVisible"
      width="600px"
      @close="selectedUser = null"
    >
      <div v-if="selectedUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ selectedUser.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ selectedUser.username }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ selectedUser.name }}</el-descriptions-item>
          <el-descriptions-item label="电话">{{ selectedUser.phone }}</el-descriptions-item>

          <!-- 客户特有信息 -->
          <el-descriptions-item v-if="activeUserType === 'customers'" label="地址" :span="2">
            {{ selectedUser.address }}
          </el-descriptions-item>

          <!-- 工人特有信息 -->
          <template v-if="activeUserType === 'workers'">
            <el-descriptions-item label="技能" :span="2">{{ selectedUser.skills }}</el-descriptions-item>
            <el-descriptions-item label="提成比例">{{ (selectedUser.commission_rate * 100).toFixed(0) }}%</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="selectedUser.status === 'active' ? 'success' : 'danger'">
                {{ selectedUser.status === 'active' ? '在职' : '离职' }}
              </el-tag>
            </el-descriptions-item>
          </template>

          <!-- 管理员特有信息 -->
          <template v-if="activeUserType === 'admins'">
            <el-descriptions-item label="角色">
              <el-tag :type="selectedUser.role === 'super_admin' ? 'danger' : 'primary'">
                {{ selectedUser.role === 'super_admin' ? '超级管理员' : '管理员' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="最后登录">{{ formatDate(selectedUser.last_login) }}</el-descriptions-item>
          </template>

          <el-descriptions-item label="注册时间">{{ formatDate(selectedUser.created_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 添加/编辑工人对话框 -->
    <el-dialog
      :title="editMode ? '编辑工人' : '添加工人'"
      :visible.sync="editVisible"
      width="500px"
      @close="resetEditForm"
    >
      <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" :disabled="editMode"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!editMode">
          <el-input v-model="editForm.password" type="password"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="editForm.name"></el-input>
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="editForm.phone"></el-input>
        </el-form-item>
        <el-form-item label="技能" prop="skills">
          <el-input v-model="editForm.skills" placeholder="请输入技能，用逗号分隔"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="editMode">
          <el-select v-model="editForm.status">
            <el-option label="在职" value="active"></el-option>
            <el-option label="离职" value="inactive"></el-option>
          </el-select>
        </el-form-item>

        <!-- 薪资配置 -->
        <el-divider content-position="left">薪资配置</el-divider>
        <el-form-item label="基础工资" prop="base_salary">
          <el-input-number
            v-model="editForm.base_salary"
            :min="0"
            :max="50000"
            :step="100"
            placeholder="基础工资"
          ></el-input-number>
          <span style="margin-left: 10px; color: #909399;">元/月</span>
        </el-form-item>
        <el-form-item label="提成比例" prop="commission_rate">
          <el-input-number
            v-model="editForm.commission_rate"
            :min="0"
            :max="1"
            :step="0.01"
            :precision="2"
            placeholder="提成比例"
          ></el-input-number>
          <span style="margin-left: 10px; color: #909399;">例如：0.15 表示 15%</span>
        </el-form-item>
        <el-form-item label="绩效奖金比例" prop="performance_bonus_rate">
          <el-input-number
            v-model="editForm.performance_bonus_rate"
            :min="0"
            :max="1"
            :step="0.01"
            :precision="2"
            placeholder="绩效奖金比例"
          ></el-input-number>
          <span style="margin-left: 10px; color: #909399;">例如：0.05 表示 5%</span>
        </el-form-item>
        <el-form-item label="全勤奖" prop="attendance_bonus">
          <el-input-number
            v-model="editForm.attendance_bonus"
            :min="0"
            :max="5000"
            :step="50"
            placeholder="全勤奖"
          ></el-input-number>
          <span style="margin-left: 10px; color: #909399;">元/月</span>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="editVisible = false">取消</el-button>
        <el-button type="primary" @click="saveUser" :loading="saving">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AdminUsers',
  data() {
    return {
      activeUserType: 'customers',
      users: [],
      loading: false,
      saving: false,
      searchForm: {
        username: '',
        name: '',
        status: ''
      },
      currentPage: 1,
      pageSize: 20,
      detailVisible: false,
      editVisible: false,
      selectedUser: null,
      editMode: false,
      editForm: {
        username: '',
        password: '',
        name: '',
        phone: '',
        skills: '',
        status: 'active',
        base_salary: 3000,
        commission_rate: 0.15,
        performance_bonus_rate: 0.05,
        attendance_bonus: 200
      },
      editRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入电话', trigger: 'blur' }
        ],
        skills: [
          { required: true, message: '请输入技能', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    filteredUsers() {
      let filtered = this.users

      if (this.searchForm.username) {
        filtered = filtered.filter(user =>
          user.username.includes(this.searchForm.username)
        )
      }

      if (this.searchForm.name) {
        filtered = filtered.filter(user =>
          user.name.includes(this.searchForm.name)
        )
      }

      if (this.searchForm.status && this.activeUserType === 'workers') {
        filtered = filtered.filter(user => user.status === this.searchForm.status)
      }

      return filtered
    },

    paginatedUsers() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredUsers.slice(start, end)
    }
  },
  async created() {
    await this.loadUsers()
  },
  methods: {
    async loadUsers() {
      try {
        this.loading = true
        const result = await this.$store.dispatch('fetchUsers', this.activeUserType)
        if (result.success) {
          this.users = this.$store.state.users[this.activeUserType] || []
        } else {
          this.$message.error(result.message || '获取用户列表失败')
        }
      } catch (error) {
        console.error('加载用户失败:', error)
        this.$message.error('加载用户失败')
      } finally {
        this.loading = false
      }
    },

    async handleUserTypeChange() {
      this.currentPage = 1
      this.resetSearch()
      await this.loadUsers()
    },

    async refreshUsers() {
      await this.loadUsers()
      this.$message.success('刷新成功')
    },

    searchUsers() {
      this.currentPage = 1
    },

    resetSearch() {
      this.searchForm = {
        username: '',
        name: '',
        status: ''
      }
      this.currentPage = 1
    },

    showUserDetail(user) {
      this.selectedUser = user
      this.detailVisible = true
    },

    showAddDialog() {
      this.editMode = false
      this.resetEditForm()
      this.editVisible = true
    },

    async editUser(user) {
      this.editMode = true
      this.editForm = {
        id: user.id,
        username: user.username,
        name: user.name,
        phone: user.phone,
        skills: user.skills,
        status: user.status,
        base_salary: 3000,
        commission_rate: 0.15,
        performance_bonus_rate: 0.05,
        attendance_bonus: 200
      }

      // 如果是工人，加载薪资配置
      if (this.activeUserType === 'workers') {
        try {
          const response = await this.$store.dispatch('fetchWorkerSalaryConfig', user.id)
          if (response.success && response.data) {
            this.editForm.base_salary = response.data.base_salary
            this.editForm.commission_rate = response.data.commission_rate
            this.editForm.performance_bonus_rate = response.data.performance_bonus_rate
            this.editForm.attendance_bonus = response.data.attendance_bonus
          }
        } catch (error) {
          console.error('加载薪资配置失败:', error)
        }
      }

      this.editVisible = true
    },

    resetEditForm() {
      this.editForm = {
        username: '',
        password: '',
        name: '',
        phone: '',
        skills: '',
        status: 'active',
        base_salary: 3000,
        commission_rate: 0.15,
        performance_bonus_rate: 0.05,
        attendance_bonus: 200
      }
      if (this.$refs.editForm) {
        this.$refs.editForm.resetFields()
      }
    },

    async saveUser() {
      try {
        await this.$refs.editForm.validate()
        this.saving = true

        let result
        if (this.editMode) {
          result = await this.$store.dispatch('updateUser', {
            userType: this.activeUserType,
            userData: this.editForm
          })
        } else {
          result = await this.$store.dispatch('createUser', {
            userType: this.activeUserType,
            userData: this.editForm
          })
        }

        if (result.success) {
          this.$message.success(this.editMode ? '更新成功' : '添加成功')
          this.editVisible = false
          await this.loadUsers()
        } else {
          this.$message.error(result.message || '操作失败')
        }
      } catch (error) {
        console.error('保存用户失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.saving = false
      }
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    handleCurrentChange(val) {
      this.currentPage = val
    },

    getUserTypeText() {
      const typeMap = {
        'customers': '客户',
        'workers': '工人',
        'admins': '管理员'
      }
      return typeMap[this.activeUserType] || '用户'
    },

    formatDate(dateString) {
      if (!dateString) return '未设置'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    },

    async deleteUser(user) {
      try {
        await this.$confirm(`确定要删除工人"${user.name}"吗？删除后将无法恢复！`, '警告', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const result = await this.$store.dispatch('deleteUser', {
          userType: this.activeUserType,
          userId: user.id
        })

        if (result.success) {
          this.$message.success('删除成功')
          await this.loadUsers()
        } else {
          this.$message.error(result.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除用户失败:', error)
          this.$message.error('删除失败')
        }
      }
    }
  }
}
</script>

<style scoped>
.admin-users {
  height: 100vh;
}

.admin-header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.admin-main {
  background: #f0f2f5;
  padding: 20px;
}

.user-type-card {
  margin-bottom: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.users-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.user-detail {
  max-height: 500px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}
</style>
