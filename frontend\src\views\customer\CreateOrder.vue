<template>
  <div class="create-order">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button icon="el-icon-arrow-left" @click="$safeRouter.push('/customer/home')">返回首页</el-button>
          <h2>创建维修工单</h2>
        </div>
        <div class="header-right">
          <span class="user-info">
            <i class="el-icon-user"></i>
            {{ currentUser.name }}
          </span>
        </div>
      </el-header>

      <!-- 主内容区 -->
      <el-main class="main-content">
        <el-card class="form-card">
          <div slot="header" class="card-header">
            <span>快速下单</span>
            <small>简单几步，一句话说清楚哪里坏了就可以下单</small>
          </div>

          <el-form
            ref="orderForm"
            :model="orderForm"
            :rules="orderRules"
            label-width="120px"
            @submit.native.prevent="submitOrder"
          >
            <!-- 基本信息 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="家电类型" prop="appliance_type">
                  <el-select v-model="orderForm.appliance_type" placeholder="请选择家电类型" style="width: 100%">
                    <el-option label="洗衣机" value="washing_machine"></el-option>
                    <el-option label="冰箱" value="refrigerator"></el-option>
                    <el-option label="空调" value="air_conditioner"></el-option>
                    <el-option label="电视" value="television"></el-option>
                    <el-option label="微波炉" value="microwave"></el-option>
                    <el-option label="热水器" value="water_heater"></el-option>
                    <el-option label="油烟机" value="range_hood"></el-option>
                    <el-option label="燃气灶" value="gas_stove"></el-option>
                    <el-option label="其他" value="other"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="品牌型号（可选）">
                  <el-input v-model="orderForm.brand_model" placeholder="如果知道品牌型号可以填写，如：海尔XQG80-B12866"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 紧急程度 -->
            <el-form-item label="紧急程度" prop="urgency">
              <el-radio-group v-model="orderForm.urgency">
                <el-radio label="low">一般</el-radio>
                <el-radio label="medium">紧急</el-radio>
                <el-radio label="high">非常紧急</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 故障描述 -->
            <el-form-item label="故障描述" prop="problem_description">
              <el-input
                v-model="orderForm.problem_description"
                type="textarea"
                :rows="3"
                placeholder="一句话说清楚哪里坏了就可以，如：洗衣机不转了、空调不制冷、冰箱不冷冻..."
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>

            <!-- 联系信息 -->
            <el-divider content-position="left">联系信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="联系人" prop="contact_name">
                  <el-input v-model="orderForm.contact_name" placeholder="请输入联系人姓名"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="contact_phone">
                  <el-input v-model="orderForm.contact_phone" placeholder="请输入手机号码"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="详细地址" prop="address">
              <el-input
                v-model="orderForm.address"
                placeholder="请输入详细地址，如：北京市朝阳区xxx小区x号楼x单元xxx室"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>

            <!-- 图片上传 -->
            <el-form-item label="故障图片">
              <el-upload
                class="upload-demo"
                action="#"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :before-upload="beforeUpload"
                :file-list="fileList"
                list-type="picture"
                multiple
                :limit="5"
                :on-exceed="handleExceed"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">
                  只能上传jpg/png文件，且不超过2MB，最多5张图片
                </div>
              </el-upload>
            </el-form-item>

            <!-- 备注 -->
            <el-form-item label="备注信息">
              <el-input
                v-model="orderForm.remarks"
                type="textarea"
                :rows="3"
                placeholder="其他需要说明的信息（选填）"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>

            <!-- 提交按钮 -->
            <el-form-item>
              <el-button type="primary" size="large" @click="submitOrder" :loading="submitting">
                提交工单
              </el-button>
              <el-button size="large" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-main>
    </el-container>

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="previewVisible" title="图片预览">
      <img width="100%" :src="previewImageUrl" alt="预览图片">
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'CustomerCreateOrder',
  computed: {
    ...mapGetters(['currentUser'])
  },
  data() {
    return {
      submitting: false,
      previewVisible: false,
      previewImageUrl: '',
      fileList: [],
      orderForm: {
        appliance_type: '',
        brand_model: '',
        urgency: 'low',
        problem_description: '',
        contact_name: '',
        contact_phone: '',
        address: '',
        remarks: ''
      },
      orderRules: {
        appliance_type: [
          { required: true, message: '请选择家电类型', trigger: 'change' }
        ],
        problem_description: [
          { required: true, message: '请描述故障现象', trigger: 'blur' },
          { min: 3, message: '故障描述至少3个字符', trigger: 'blur' }
        ],
        contact_name: [
          { required: true, message: '请输入联系人姓名', trigger: 'blur' }
        ],
        contact_phone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        address: [
          { required: true, message: '请输入详细地址', trigger: 'blur' },
          { min: 3, message: '地址信息至少3个字符', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    // 初始化联系信息
    if (this.currentUser) {
      this.orderForm.contact_name = this.currentUser.name || ''
      this.orderForm.contact_phone = this.currentUser.phone || ''
    }
  },
  methods: {
    async submitOrder() {
      try {
        await this.$refs.orderForm.validate()

        this.submitting = true

        // 准备提交数据
        const orderData = {
          ...this.orderForm,
          // 确保空的purchase_date被设置为null或空字符串
          purchase_date: this.orderForm.purchase_date || null,
          images: this.fileList.map(file => file.url || file.response?.url).filter(Boolean)
        }

        console.log('[前端] 提交工单数据:', orderData)

        const result = await this.$store.dispatch('createOrder', orderData)

        if (result.success) {
          this.$message.success('工单提交成功！')
          this.$safeRouter.push('/customer/orders')
        } else {
          this.$message.error(result.message || '提交失败，请重试')
        }
      } catch (error) {
        console.error('[前端] 提交工单失败:', error)
        if (error.message !== 'validation failed') {
          this.$message.error('提交失败，请检查网络连接')
        }
      } finally {
        this.submitting = false
      }
    },

    resetForm() {
      this.$refs.orderForm.resetFields()
      this.fileList = []
    },

    // 图片上传相关方法
    handlePreview(file) {
      this.previewImageUrl = file.url
      this.previewVisible = true
    },

    handleRemove(file, fileList) {
      this.fileList = fileList
    },

    beforeUpload(file) {
      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPGOrPNG) {
        this.$message.error('只能上传 JPG/PNG 格式的图片!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }

      // 模拟上传成功
      const reader = new FileReader()
      reader.onload = (e) => {
        this.fileList.push({
          name: file.name,
          url: e.target.result
        })
      }
      reader.readAsDataURL(file)

      return false // 阻止自动上传
    },

    handleExceed(files, fileList) {
      this.$message.warning(`最多只能上传5张图片，当前选择了 ${files.length} 张图片，共选择了 ${files.length + fileList.length} 张图片`)
    }
  }
}
</script>

<style scoped>
.create-order {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
}

.user-info {
  color: white;
  font-size: 14px;
}

.main-content {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.form-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.card-header span {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.card-header small {
  color: #909399;
  font-size: 13px;
}

.el-form {
  padding: 20px 0;
}

.el-divider {
  margin: 30px 0 20px 0;
}

.upload-demo {
  margin-top: 10px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 7px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 10px;
  }

  .header-left {
    gap: 10px;
  }

  .header-left h2 {
    font-size: 16px;
  }

  .el-form {
    padding: 10px 0;
  }

  /* 移动端表单优化 */
  .el-form-item__label {
    font-size: 14px !important;
    font-weight: 500;
  }

  .el-input__inner,
  .el-textarea__inner {
    font-size: 14px !important;
  }

  .el-select {
    width: 100% !important;
  }

  .el-date-picker {
    width: 100% !important;
  }

  /* 移动端上传组件优化 */
  .upload-demo {
    text-align: center;
  }

  .el-upload {
    width: 100%;
  }

  .el-upload-dragger {
    width: 100% !important;
    height: 120px !important;
  }

  /* 移动端按钮优化 */
  .submit-btn {
    width: 100% !important;
    height: 45px !important;
    font-size: 16px !important;
    margin-top: 20px;
  }

  /* 移动端卡片间距 */
  .el-card {
    margin-bottom: 15px !important;
  }

  .el-card__body {
    padding: 20px 15px !important;
  }
}
</style>
