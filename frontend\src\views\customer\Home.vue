<template>
  <div class="customer-home">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <h2>客户端 - 永盛制冷维修有限公司</h2>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <i class="el-icon-user"></i>
              {{ currentUser.name }}
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-header>
      
      <!-- 主内容区 -->
      <el-main class="main-content">
        <div class="welcome-section">
          <div class="welcome-content">
            <h1>欢迎回来，{{ currentUser.name }}！</h1>
            <p>您可以在这里提交维修申请和查看工单状态</p>
            <div class="welcome-decoration"></div>
          </div>
        </div>

        <!-- 数据统计 -->
        <el-row :gutter="20" class="stats-row">
          <el-col :span="8">
            <el-card class="stat-card" shadow="hover">
              <div class="stat-content">
                <div class="stat-icon pending">
                  <i class="el-icon-document"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ customerStats.totalOrders }}</div>
                  <div class="stat-label">总工单数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stat-card" shadow="hover">
              <div class="stat-content">
                <div class="stat-icon processing">
                  <i class="el-icon-loading"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ customerStats.inProgress }}</div>
                  <div class="stat-label">进行中</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stat-card" shadow="hover">
              <div class="stat-content">
                <div class="stat-icon completed">
                  <i class="el-icon-circle-check"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ customerStats.completed }}</div>
                  <div class="stat-label">已完成</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <div class="quick-actions">
          <h2>快捷操作</h2>
          <el-row :gutter="20" class="action-row">
            <el-col :span="8">
              <el-card class="action-card" shadow="hover" @click.native="$safeRouter.push('/customer/create-order')">
                <div class="card-content">
                  <div class="action-icon">
                    <i class="el-icon-plus"></i>
                  </div>
                  <h3>创建工单</h3>
                  <p>提交新的维修申请</p>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="action-card" shadow="hover" @click.native="$safeRouter.push('/customer/orders')">
                <div class="card-content">
                  <div class="action-icon">
                    <i class="el-icon-document"></i>
                  </div>
                  <h3>我的工单</h3>
                  <p>查看工单状态和历史</p>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="action-card contact-card" shadow="hover">
                <div class="card-content">
                  <div class="action-icon">
                    <i class="el-icon-phone"></i>
                  </div>
                  <h3>联系客服</h3>
                  <p>📞 13471579359</p>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <div class="recent-orders" v-if="recentOrders.length > 0">
          <h2>最近的工单</h2>
          <el-table :data="recentOrders" style="width: 100%">
            <el-table-column prop="id" label="工单号" width="100"></el-table-column>
            <el-table-column prop="appliance_type" label="家电类型" width="120"></el-table-column>
            <el-table-column prop="problem_description" label="问题描述" show-overflow-tooltip></el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180">
              <template slot-scope="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-main>
    </el-container>



    <!-- 悬浮微信二维码 -->
    <div class="floating-wechat" @click="toggleQRCode">
      <div class="wechat-label">微信二维码</div>
      <div class="wechat-icon">
        <i class="el-icon-chat-dot-round"></i>
      </div>
      <div class="qr-popup" v-show="showQRCode" @click.stop>
        <div class="qr-header">
          <span>扫码添加微信</span>
          <i class="el-icon-close" @click="showQRCode = false"></i>
        </div>
        <div class="qr-content">
          <p class="qr-title">微信二维码</p>
          <img src="/images/微信二维码.jpg" alt="微信二维码 A13471579359" class="floating-qr-image" />
          <p class="wechat-id">微信号：A13471579359</p>
          <p class="qr-tip">长按保存二维码，微信扫码添加</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'CustomerHome',
  data() {
    return {
      showQRCode: false
    }
  },
  computed: {
    ...mapGetters(['currentUser']),
    recentOrders() {
      return this.$store.state.orders.slice(0, 5)
    },
    customerStats() {
      const orders = this.$store.state.orders || []
      return {
        totalOrders: orders.length,
        inProgress: orders.filter(order =>
          ['pending', 'accepted', 'in_progress'].includes(order.status)
        ).length,
        completed: orders.filter(order =>
          order.status === 'completed'
        ).length
      }
    }
  },
  async created() {
    await this.$store.dispatch('fetchCustomerOrders')
  },

  mounted() {
    // 点击外部关闭二维码弹窗
    document.addEventListener('click', this.handleClickOutside)
  },

  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside)
  },
  methods: {
    toggleQRCode() {
      this.showQRCode = !this.showQRCode
    },

    handleClickOutside(event) {
      const floatingWechat = document.querySelector('.floating-wechat')
      if (floatingWechat && !floatingWechat.contains(event.target)) {
        this.showQRCode = false
      }
    },



    async handleCommand(command) {
      if (command === 'logout') {
        try {
          await this.$confirm('确定要退出登录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })

          const result = await this.$store.dispatch('logout')

          if (result.success) {
            this.$message.success('退出登录成功')
            this.$safeRouter.push('/customer/login')
          }
        } catch (error) {
          // 用户取消退出
          console.log('用户取消退出登录')
        }
      }
    },
    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'accepted': 'info',
        'in_progress': 'primary',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    },
    getStatusText(status) {
      const statusMap = {
        'pending': '待接单',
        'accepted': '已接单',
        'in_progress': '维修中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || '未知状态'
    },
    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.customer-home {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left h2 {
  margin: 0;
}

.user-info {
  cursor: pointer;
  color: white;
  font-size: 14px;
}

.user-info:hover {
  opacity: 0.8;
}

.main-content {
  padding: 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 30px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-content h1 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 2.2rem;
  font-weight: 700;
}

.welcome-content p {
  color: #606266;
  font-size: 16px;
  margin-bottom: 20px;
}

.welcome-decoration {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, transparent, #409EFF, transparent);
  margin: 0 auto;
  border-radius: 2px;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 16px;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 25px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-icon i {
  font-size: 24px;
  color: white;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.stat-icon.processing {
  background: linear-gradient(135deg, #E6A23C, #F7BA2A);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.quick-actions {
  margin-bottom: 40px;
}

.quick-actions h2 {
  color: #303133;
  margin-bottom: 20px;
  font-size: 1.5rem;
  font-weight: 600;
}

.action-row {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.action-card {
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 16px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 200px;
}

.action-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(64, 158, 255, 0.15);
  background: rgba(255, 255, 255, 0.98);
}

.card-content {
  text-align: center;
  padding: 35px 25px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.action-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #409EFF, #66B1FF);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-icon i {
  font-size: 2.5rem;
  color: white;
}

.action-card:hover .action-icon {
  transform: scale(1.1) rotateY(360deg);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
}

.card-content h3 {
  color: #303133;
  margin-bottom: 12px;
  font-size: 1.3rem;
  font-weight: 600;
}

.card-content p {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.recent-orders {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.recent-orders h2 {
  color: #303133;
  margin-bottom: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .customer-home {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }

  .header {
    height: auto !important;
    padding: 15px 20px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-radius: 0 0 25px 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    position: relative;
    z-index: 1000;
  }

  .header-left h2 {
    font-size: 16px !important;
    margin: 0;
    color: #2c3e50 !important;
    font-weight: 700;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
  }

  .header-right {
    flex-shrink: 0;
  }

  .user-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 25px !important;
    padding: 8px 16px !important;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  }

  .main-content {
    padding: 0 15px 20px 15px !important;
    background: transparent !important;
  }

  .welcome-section {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-radius: 20px !important;
    padding: 30px 25px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .welcome-section h1 {
    font-size: 26px !important;
    margin-bottom: 12px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
  }

  .welcome-section p {
    font-size: 15px !important;
    color: #6c757d !important;
    font-weight: 500;
  }

  .quick-actions {
    margin-bottom: 25px;
  }

  .action-card {
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-radius: 16px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease;
    min-height: 180px;
  }

  .action-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
  }

  .action-card .card-content {
    padding: 25px 20px;
  }

  .action-card .card-content i {
    font-size: 40px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .action-card .card-content h3 {
    font-size: 17px;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 600;
  }

  .action-card .card-content p {
    font-size: 13px;
    color: #6c757d;
    font-weight: 500;
  }

  /* 联系卡片桌面端样式 */
  .contact-card .contact-info {
    margin-top: 10px;
  }

  .contact-card .phone-number {
    font-size: 14px;
    color: #409EFF;
    font-weight: 600;
    margin: 6px 0;
  }

  .contact-card .wechat-info {
    font-size: 13px;
    color: #67C23A;
    margin: 6px 0;
  }



  .recent-orders h2 {
    font-size: 18px;
    margin-bottom: 15px;
  }

  /* 移动端表格优化 */
  .el-table {
    font-size: 12px;
  }

  .el-table .cell {
    padding: 0 5px;
    line-height: 1.3;
  }

  /* 移动端状态标签 */
  .el-tag {
    font-size: 11px;
    padding: 2px 6px;
  }



  /* 联系卡片样式 */
  .contact-card .contact-info {
    margin-top: 8px;
  }

  .contact-card .phone-number {
    font-size: 12px;
    color: #409EFF;
    font-weight: 600;
    margin: 4px 0;
  }

  .contact-card .wechat-info {
    font-size: 11px;
    color: #67C23A;
    margin: 4px 0;
  }


}

/* 悬浮微信二维码样式 */
.floating-wechat {
  position: fixed;
  right: 20px;
  bottom: 100px;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 10px;
}

.wechat-label {
  background: #07C160;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(7, 193, 96, 0.3);
}

.wechat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #07C160, #00A854);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
  transition: all 0.3s ease;
}

.wechat-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(7, 193, 96, 0.4);
}

.wechat-icon i {
  font-size: 28px;
  color: white;
}

.qr-popup {
  position: absolute;
  right: 70px;
  bottom: 0;
  width: 280px;
  max-width: calc(100vw - 100px);
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.qr-header {
  background: linear-gradient(135deg, #07C160, #00A854);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.qr-header i {
  cursor: pointer;
  font-size: 18px;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.qr-header i:hover {
  opacity: 1;
}

.qr-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
}

.qr-content .qr-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  margin-top: 0;
}

.floating-qr-image {
  width: 100%;
  max-width: 180px;
  height: auto;
  aspect-ratio: 1;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  margin-bottom: 15px;
  object-fit: contain;
}

.wechat-id {
  font-size: 16px;
  font-weight: bold;
  color: #07C160;
  margin-bottom: 8px;
}



/* 移动端响应式样式 */
@media (max-width: 768px) {
  .action-card {
    height: 150px;
  }

  .action-card .card-content {
    padding: 20px 15px;
  }

  .floating-wechat {
    right: 15px;
    bottom: 80px;
    flex-direction: column;
    gap: 5px;
  }

  .wechat-label {
    font-size: 10px;
    padding: 4px 8px;
  }

  .wechat-icon {
    width: 50px;
    height: 50px;
  }

  .wechat-icon i {
    font-size: 24px;
  }

  .qr-popup {
    right: 60px;
    width: 250px;
    max-width: calc(100vw - 80px);
  }

  .qr-content {
    padding: 15px;
  }

  .floating-qr-image {
    max-width: 150px;
  }


}

@media (max-width: 480px) {
  .action-card {
    height: 130px;
  }

  .action-card .card-content {
    padding: 15px 12px;
  }

  .qr-popup {
    right: 10px;
    left: 10px;
    width: auto;
    max-width: none;
  }

  .floating-qr-image {
    max-width: 120px;
  }


}
</style>
