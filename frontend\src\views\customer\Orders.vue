<template>
  <div class="customer-orders">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button icon="el-icon-arrow-left" @click="$safeRouter.push('/customer/home')">返回首页</el-button>
          <h2>我的工单</h2>
        </div>
        <div class="header-right">
          <el-button type="primary" icon="el-icon-plus" @click="$safeRouter.push('/customer/create-order')">
            创建工单
          </el-button>
        </div>
      </el-header>

      <!-- 主内容区 -->
      <el-main class="main-content">
        <!-- 筛选和搜索 -->
        <el-card class="filter-card" shadow="never">
          <el-row :gutter="20" type="flex" align="middle">
            <el-col :span="6">
              <el-select v-model="filterStatus" placeholder="工单状态" clearable @change="handleFilter">
                <el-option label="全部状态" value=""></el-option>
                <el-option label="待接单" value="pending"></el-option>
                <el-option label="已接单" value="accepted"></el-option>
                <el-option label="维修中" value="in_progress"></el-option>
                <el-option label="已完成" value="completed"></el-option>
                <el-option label="已取消" value="cancelled"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="filterType" placeholder="家电类型" clearable @change="handleFilter">
                <el-option label="全部类型" value=""></el-option>
                <el-option label="洗衣机" value="washing_machine"></el-option>
                <el-option label="冰箱" value="refrigerator"></el-option>
                <el-option label="空调" value="air_conditioner"></el-option>
                <el-option label="电视" value="television"></el-option>
                <el-option label="微波炉" value="microwave"></el-option>
                <el-option label="热水器" value="water_heater"></el-option>
                <el-option label="油烟机" value="range_hood"></el-option>
                <el-option label="燃气灶" value="gas_stove"></el-option>
                <el-option label="其他" value="other"></el-option>
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索工单号、品牌型号或问题描述"
                prefix-icon="el-icon-search"
                @input="handleSearch"
                clearable
              ></el-input>
            </el-col>
            <el-col :span="4">
              <el-button @click="refreshOrders" icon="el-icon-refresh">刷新</el-button>
            </el-col>
          </el-row>
        </el-card>

        <!-- 工单统计 -->
        <el-row :gutter="20" class="stats-row">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ orderStats.total }}</div>
                <div class="stat-label">总工单数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number pending">{{ orderStats.pending }}</div>
                <div class="stat-label">待接单</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number processing">{{ orderStats.processing }}</div>
                <div class="stat-label">处理中</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number completed">{{ orderStats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 工单列表 -->
        <el-card class="orders-card">
          <div slot="header" class="card-header">
            <span>工单列表</span>
            <small>共 {{ filteredOrders.length }} 条记录</small>
          </div>

          <!-- 桌面端表格 -->
          <el-table
            :data="paginatedOrders"
            v-loading="loading"
            style="width: 100%"
            @row-click="viewOrderDetail"
            row-class-name="order-row"
            class="desktop-table"
          >
            <el-table-column prop="id" label="工单号" width="100" fixed="left">
              <template slot-scope="scope">
                <span class="order-id">#{{ scope.row.id }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="appliance_type" label="家电类型" width="120">
              <template slot-scope="scope">
                {{ getApplianceTypeName(scope.row.appliance_type) }}
              </template>
            </el-table-column>

            <el-table-column prop="brand_model" label="品牌型号" width="150" show-overflow-tooltip></el-table-column>

            <el-table-column prop="problem_description" label="问题描述" min-width="200" show-overflow-tooltip></el-table-column>

            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="urgency" label="紧急程度" width="100">
              <template slot-scope="scope">
                <el-tag :type="getUrgencyType(scope.row.urgency)" size="small">
                  {{ getUrgencyText(scope.row.urgency) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="worker_name" label="维修师傅" width="120">
              <template slot-scope="scope">
                {{ scope.row.worker_name || '未分配' }}
              </template>
            </el-table-column>

            <el-table-column prop="created_at" label="创建时间" width="160">
              <template slot-scope="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="150" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" @click.stop="viewOrderDetail(scope.row)">查看</el-button>
                <el-button
                  v-if="scope.row.status === 'pending'"
                  size="mini"
                  type="danger"
                  @click.stop="cancelOrder(scope.row)"
                >
                  取消
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 移动端卡片列表 -->
          <div class="mobile-order-list" v-loading="loading">
            <MobileOrderCard
              v-for="order in paginatedOrders"
              :key="order.id"
              :order="order"
              @view-detail="viewOrderDetail"
            >
              <template #actions="{ order }">
                <el-button size="mini" @click.stop="viewOrderDetail(order)">查看</el-button>
                <el-button
                  v-if="order.status === 'pending'"
                  size="mini"
                  type="danger"
                  @click.stop="cancelOrder(order)"
                >
                  取消
                </el-button>
              </template>
            </MobileOrderCard>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="filteredOrders.length"
            ></el-pagination>
          </div>
        </el-card>
      </el-main>
    </el-container>

    <!-- 工单详情对话框 -->
    <el-dialog
      :visible.sync="detailVisible"
      title="工单详情"
      width="800px"
      :before-close="handleDetailClose"
    >
      <order-detail
        v-if="selectedOrder"
        :order="selectedOrder"
        @refresh="refreshOrders"
      ></order-detail>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import OrderDetail from './components/OrderDetail.vue'
import MobileOrderCard from '@/components/MobileOrderCard.vue'

export default {
  name: 'CustomerOrders',
  components: {
    OrderDetail,
    MobileOrderCard
  },
  computed: {
    ...mapGetters(['currentUser']),
    orders() {
      return this.$store.state.orders || []
    },
    filteredOrders() {
      let filtered = this.orders

      // 状态筛选
      if (this.filterStatus) {
        filtered = filtered.filter(order => order.status === this.filterStatus)
      }

      // 类型筛选
      if (this.filterType) {
        filtered = filtered.filter(order => order.appliance_type === this.filterType)
      }

      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(order =>
          order.id.toString().includes(keyword) ||
          order.brand_model.toLowerCase().includes(keyword) ||
          order.problem_description.toLowerCase().includes(keyword)
        )
      }

      return filtered
    },
    paginatedOrders() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredOrders.slice(start, end)
    },
    orderStats() {
      const stats = {
        total: this.orders.length,
        pending: 0,
        processing: 0,
        completed: 0
      }

      this.orders.forEach(order => {
        switch (order.status) {
          case 'pending':
            stats.pending++
            break
          case 'accepted':
          case 'in_progress':
            stats.processing++
            break
          case 'completed':
            stats.completed++
            break
        }
      })

      return stats
    }
  },
  data() {
    return {
      loading: false,
      detailVisible: false,
      selectedOrder: null,
      filterStatus: '',
      filterType: '',
      searchKeyword: '',
      currentPage: 1,
      pageSize: 10
    }
  },
  async created() {
    await this.refreshOrders()
  },
  methods: {
    async refreshOrders() {
      this.loading = true
      try {
        await this.$store.dispatch('fetchCustomerOrders')
      } catch (error) {
        console.error('[前端] 获取工单列表失败:', error)
        this.$message.error('获取工单列表失败')
      } finally {
        this.loading = false
      }
    },

    handleFilter() {
      this.currentPage = 1
    },

    handleSearch() {
      this.currentPage = 1
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    handleCurrentChange(val) {
      this.currentPage = val
    },

    viewOrderDetail(order) {
      this.selectedOrder = order
      this.detailVisible = true
    },

    handleDetailClose() {
      this.detailVisible = false
      this.selectedOrder = null
    },

    async cancelOrder(order) {
      try {
        await this.$confirm('确定要取消这个工单吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 这里调用取消工单的API
        this.$message.success('工单已取消')
        await this.refreshOrders()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('取消工单失败')
        }
      }
    },

    getApplianceTypeName(type) {
      const typeMap = {
        'washing_machine': '洗衣机',
        'refrigerator': '冰箱',
        'air_conditioner': '空调',
        'television': '电视',
        'microwave': '微波炉',
        'water_heater': '热水器',
        'range_hood': '油烟机',
        'gas_stove': '燃气灶',
        'other': '其他'
      }
      return typeMap[type] || type
    },

    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'accepted': 'info',
        'in_progress': 'primary',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        'pending': '待接单',
        'accepted': '已接单',
        'in_progress': '维修中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || '未知状态'
    },

    getUrgencyType(urgency) {
      const urgencyMap = {
        'low': 'info',
        'medium': 'warning',
        'high': 'danger'
      }
      return urgencyMap[urgency] || 'info'
    },

    getUrgencyText(urgency) {
      const urgencyMap = {
        'low': '一般',
        'medium': '紧急',
        'high': '非常紧急'
      }
      return urgencyMap[urgency] || '一般'
    },

    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.customer-orders {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
}

.main-content {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-number.pending {
  color: #E6A23C;
}

.stat-number.processing {
  color: #409EFF;
}

.stat-number.completed {
  color: #67C23A;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.orders-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 桌面端和移动端显示控制 */
.desktop-table {
  display: table;
}

.mobile-order-list {
  display: none;
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.card-header span {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-header small {
  color: #909399;
  font-size: 12px;
}

.order-row {
  cursor: pointer;
}

.order-row:hover {
  background-color: #f5f7fa;
}

.order-id {
  font-weight: 600;
  color: #409EFF;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  /* 移动端显示控制 */
  .desktop-table {
    display: none !important;
  }

  .mobile-order-list {
    display: block !important;
  }

  .main-content {
    padding: 0 15px 20px 15px !important;
    background: transparent !important;
  }

  .customer-orders {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }

  .header {
    height: auto !important;
    padding: 15px 20px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-radius: 0 0 25px 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    position: relative;
    z-index: 1000;
  }

  .header-left {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .header-left h2 {
    font-size: 16px !important;
    margin: 0;
    color: #2c3e50 !important;
    font-weight: 700;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
  }

  .header-right {
    flex-shrink: 0;
  }

  .user-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 25px !important;
    padding: 8px 16px !important;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  }

  .stats-row .el-col {
    margin-bottom: 10px;
  }

  .el-table {
    font-size: 12px;
  }

  /* 移动端表格进一步优化 */
  .el-table .cell {
    padding: 0 3px !important;
    line-height: 1.2;
    word-break: break-all;
  }

  .el-table th,
  .el-table td {
    padding: 6px 3px !important;
  }

  /* 移动端按钮优化 */
  .el-button--mini {
    padding: 4px 8px !important;
    font-size: 11px !important;
  }

  /* 移动端状态标签 */
  .el-tag--mini {
    font-size: 10px !important;
    padding: 1px 4px !important;
  }

  /* 移动端分页优化 */
  .el-pagination {
    text-align: center;
    padding: 10px 0;
  }

  .el-pagination .el-pager li {
    min-width: 25px;
    height: 25px;
    line-height: 25px;
    font-size: 12px;
  }

  .el-pagination .btn-prev,
  .el-pagination .btn-next {
    min-width: 25px;
    height: 25px;
    line-height: 25px;
  }

  /* 移动端对话框优化 */
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .el-dialog__header {
    padding: 15px 15px 10px !important;
  }

  .el-dialog__body {
    padding: 10px 15px 15px !important;
  }

  .el-dialog__title {
    font-size: 16px !important;
  }
}
</style>
