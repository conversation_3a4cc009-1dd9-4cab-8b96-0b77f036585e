<template>
  <div class="available-orders">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button icon="el-icon-arrow-left" @click="$safeRouter.push('/worker/home')">返回首页</el-button>
          <h2>可接工单</h2>
        </div>
        <div class="header-right">
          <span class="user-info">
            <i class="el-icon-user"></i>
            {{ currentUser.name }}
          </span>
        </div>
      </el-header>

      <!-- 主内容区 -->
      <el-main class="main-content">
        <!-- 筛选条件 -->
        <el-card class="filter-card" shadow="never">
          <el-row :gutter="20" type="flex" align="middle">
            <el-col :span="4">
              <el-select v-model="filterType" placeholder="家电类型" clearable @change="handleFilter">
                <el-option label="全部类型" value=""></el-option>
                <el-option label="洗衣机" value="washing_machine"></el-option>
                <el-option label="冰箱" value="refrigerator"></el-option>
                <el-option label="空调" value="air_conditioner"></el-option>
                <el-option label="电视" value="television"></el-option>
                <el-option label="微波炉" value="microwave"></el-option>
                <el-option label="热水器" value="water_heater"></el-option>
                <el-option label="油烟机" value="range_hood"></el-option>
                <el-option label="燃气灶" value="gas_stove"></el-option>
                <el-option label="其他" value="other"></el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="filterUrgency" placeholder="紧急程度" clearable @change="handleFilter">
                <el-option label="全部" value=""></el-option>
                <el-option label="一般" value="low"></el-option>
                <el-option label="紧急" value="medium"></el-option>
                <el-option label="非常紧急" value="high"></el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="filterDistance" placeholder="距离范围" clearable @change="handleFilter">
                <el-option label="全部" value=""></el-option>
                <el-option label="5公里内" value="5"></el-option>
                <el-option label="10公里内" value="10"></el-option>
                <el-option label="20公里内" value="20"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索工单号、品牌型号或地址"
                prefix-icon="el-icon-search"
                @input="handleSearch"
                clearable
              ></el-input>
            </el-col>
            <el-col :span="3">
              <el-button @click="refreshOrders" icon="el-icon-refresh">刷新</el-button>
            </el-col>
            <el-col :span="3">
              <el-switch
                v-model="autoRefresh"
                active-text="自动刷新"
                @change="toggleAutoRefresh"
              ></el-switch>
            </el-col>
          </el-row>
        </el-card>

        <!-- 工单列表 -->
        <div class="orders-container">
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>

          <div v-else-if="filteredOrders.length === 0" class="empty-state">
            <i class="el-icon-document"></i>
            <p>暂无可接工单</p>
            <el-button @click="refreshOrders">刷新试试</el-button>
          </div>

          <div v-else class="orders-grid">
            <el-card
              v-for="order in paginatedOrders"
              :key="order.id"
              class="order-card"
              shadow="hover"
            >
              <div class="order-header">
                <div class="order-id">#{{ order.id }}</div>
                <div class="order-urgency">
                  <el-tag :type="getUrgencyType(order.urgency)" size="small">
                    {{ getUrgencyText(order.urgency) }}
                  </el-tag>
                </div>
              </div>

              <div class="order-info">
                <div class="appliance-info">
                  <h3>{{ getApplianceTypeName(order.appliance_type) }}</h3>
                  <p class="brand">{{ order.brand_model }}</p>
                </div>

                <div class="problem-desc">
                  <p>{{ order.problem_description }}</p>
                </div>

                <div class="location-info">
                  <div class="address">
                    <i class="el-icon-location"></i>
                    <span>{{ order.address }}</span>
                  </div>
                  <div class="distance" v-if="order.distance">
                    <i class="el-icon-position"></i>
                    <span>{{ order.distance }}km</span>
                  </div>
                </div>

                <div class="time-info">
                  <div class="preferred-time">
                    <i class="el-icon-time"></i>
                    <span>{{ formatDate(order.preferred_date) }} {{ getTimeSlotText(order.preferred_time) }}</span>
                  </div>
                  <div class="created-time">
                    <i class="el-icon-clock"></i>
                    <span>{{ formatRelativeTime(order.created_at) }}</span>
                  </div>
                </div>

                <div class="customer-info">
                  <div class="contact">
                    <i class="el-icon-user"></i>
                    <span>{{ order.contact_name }}</span>
                  </div>
                  <div class="phone">
                    <i class="el-icon-phone"></i>
                    <span>{{ order.contact_phone }}</span>
                  </div>
                </div>
              </div>

              <div class="order-actions">
                <el-button size="small" @click="viewOrderDetail(order)">查看详情</el-button>
                <el-button
                  type="primary"
                  size="small"
                  @click="acceptOrder(order)"
                  :loading="acceptingOrders.includes(order.id)"
                >
                  接单
                </el-button>
              </div>
            </el-card>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="filteredOrders.length > 0">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[12, 24, 48]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="filteredOrders.length"
            ></el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>

    <!-- 工单详情对话框 -->
    <el-dialog
      :visible.sync="detailVisible"
      title="工单详情"
      width="800px"
      :before-close="handleDetailClose"
    >
      <order-detail
        v-if="selectedOrder"
        :order="selectedOrder"
        :show-accept-button="true"
        @accept="acceptOrder"
        @refresh="refreshOrders"
      ></order-detail>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import OrderDetail from './components/WorkerOrderDetail.vue'

export default {
  name: 'WorkerAvailableOrders',
  components: {
    OrderDetail
  },
  computed: {
    ...mapGetters(['currentUser']),
    availableOrders() {
      return this.$store.state.availableOrders || []
    },
    filteredOrders() {
      let filtered = this.availableOrders

      // 类型筛选
      if (this.filterType) {
        filtered = filtered.filter(order => order.appliance_type === this.filterType)
      }

      // 紧急程度筛选
      if (this.filterUrgency) {
        filtered = filtered.filter(order => order.urgency === this.filterUrgency)
      }

      // 距离筛选
      if (this.filterDistance) {
        filtered = filtered.filter(order =>
          order.distance && order.distance <= parseInt(this.filterDistance)
        )
      }

      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(order =>
          order.id.toString().includes(keyword) ||
          order.brand_model.toLowerCase().includes(keyword) ||
          order.address.toLowerCase().includes(keyword)
        )
      }

      return filtered
    },
    paginatedOrders() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredOrders.slice(start, end)
    }
  },
  data() {
    return {
      loading: false,
      detailVisible: false,
      selectedOrder: null,
      filterType: '',
      filterUrgency: '',
      filterDistance: '',
      searchKeyword: '',
      currentPage: 1,
      pageSize: 12,
      autoRefresh: false,
      refreshTimer: null,
      acceptingOrders: []
    }
  },
  async created() {
    await this.refreshOrders()
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },
  methods: {
    async refreshOrders() {
      this.loading = true
      try {
        await this.$store.dispatch('fetchAvailableOrders')
      } catch (error) {
        console.error('[前端] 获取可接工单失败:', error)
        this.$message.error('获取可接工单失败')
      } finally {
        this.loading = false
      }
    },

    handleFilter() {
      this.currentPage = 1
    },

    handleSearch() {
      this.currentPage = 1
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    handleCurrentChange(val) {
      this.currentPage = val
    },

    toggleAutoRefresh(enabled) {
      if (enabled) {
        this.refreshTimer = setInterval(() => {
          this.refreshOrders()
        }, 30000) // 30秒刷新一次
        this.$message.success('已开启自动刷新')
      } else {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
        }
        this.$message.info('已关闭自动刷新')
      }
    },

    viewOrderDetail(order) {
      this.selectedOrder = order
      this.detailVisible = true
    },

    handleDetailClose() {
      this.detailVisible = false
      this.selectedOrder = null
    },

    async acceptOrder(order) {
      try {
        this.acceptingOrders.push(order.id)

        await this.$confirm(`确定要接受工单 #${order.id} 吗？`, '确认接单', {
          confirmButtonText: '确定接单',
          cancelButtonText: '取消',
          type: 'info'
        })

        const result = await this.$store.dispatch('acceptOrder', order.id)

        if (result.success) {
          this.$message.success('接单成功！')
          this.detailVisible = false
          await this.refreshOrders()
        } else {
          this.$message.error(result.message || '接单失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('[前端] 接单失败:', error)
          this.$message.error('接单失败，请重试')
        }
      } finally {
        const index = this.acceptingOrders.indexOf(order.id)
        if (index > -1) {
          this.acceptingOrders.splice(index, 1)
        }
      }
    },

    getApplianceTypeName(type) {
      const typeMap = {
        'washing_machine': '洗衣机',
        'refrigerator': '冰箱',
        'air_conditioner': '空调',
        'television': '电视',
        'microwave': '微波炉',
        'water_heater': '热水器',
        'range_hood': '油烟机',
        'gas_stove': '燃气灶',
        'other': '其他'
      }
      return typeMap[type] || type
    },

    getUrgencyType(urgency) {
      const urgencyMap = {
        'low': 'info',
        'medium': 'warning',
        'high': 'danger'
      }
      return urgencyMap[urgency] || 'info'
    },

    getUrgencyText(urgency) {
      const urgencyMap = {
        'low': '一般',
        'medium': '紧急',
        'high': '非常紧急'
      }
      return urgencyMap[urgency] || '一般'
    },

    getTimeSlotText(timeSlot) {
      const slotMap = {
        'morning': '上午 (9:00-12:00)',
        'afternoon': '下午 (13:00-17:00)',
        'evening': '晚上 (18:00-20:00)'
      }
      return slotMap[timeSlot] || ''
    },

    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },

    formatRelativeTime(dateString) {
      if (!dateString) return '-'
      const now = new Date()
      const date = new Date(dateString)
      const diff = now - date
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(minutes / 60)
      const days = Math.floor(hours / 24)

      if (days > 0) {
        return `${days}天前`
      } else if (hours > 0) {
        return `${hours}小时前`
      } else if (minutes > 0) {
        return `${minutes}分钟前`
      } else {
        return '刚刚'
      }
    }
  }
}
</script>

<style scoped>
.available-orders {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #67C23A;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
}

.user-info {
  color: white;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.main-content {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.orders-container {
  min-height: 400px;
}

.loading-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.empty-state i {
  font-size: 4rem;
  color: #C0C4CC;
  margin-bottom: 20px;
}

.empty-state p {
  color: #909399;
  font-size: 16px;
  margin-bottom: 20px;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.order-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.order-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.order-id {
  font-size: 18px;
  font-weight: 600;
  color: #67C23A;
}

.order-info {
  margin-bottom: 20px;
}

.appliance-info {
  margin-bottom: 15px;
}

.appliance-info h3 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 16px;
}

.appliance-info .brand {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.problem-desc {
  margin-bottom: 15px;
}

.problem-desc p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.location-info, .time-info, .customer-info {
  margin-bottom: 10px;
}

.location-info .address, .location-info .distance,
.time-info .preferred-time, .time-info .created-time,
.customer-info .contact, .customer-info .phone {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
  font-size: 13px;
  color: #909399;
}

.location-info .address i, .location-info .distance i,
.time-info .preferred-time i, .time-info .created-time i,
.customer-info .contact i, .customer-info .phone i {
  width: 14px;
}

.order-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 10px;
  }

  .orders-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .order-card {
    margin-bottom: 0;
  }

  .filter-card .el-row .el-col {
    margin-bottom: 10px;
  }
}

@media (max-width: 480px) {
  .header-left {
    gap: 10px;
  }

  .header-left h2 {
    font-size: 16px;
  }

  .order-actions {
    flex-direction: column;
  }

  .order-actions .el-button {
    width: 100%;
  }
}
</style>
