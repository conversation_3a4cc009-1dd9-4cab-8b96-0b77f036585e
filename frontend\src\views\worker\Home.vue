<template>
  <div class="worker-home">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <h2>工人端 - 永盛制冷维修有限公司</h2>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <i class="el-icon-user"></i>
              {{ currentUser.name }}
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">个人信息</el-dropdown-item>
              <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主内容区 -->
      <el-main class="main-content">
        <div class="welcome-section">
          <h1>欢迎回来，{{ currentUser.name }}师傅！</h1>
          <p>今天也要为客户提供优质的维修服务哦～</p>
        </div>

        <!-- 数据统计 -->
        <el-row :gutter="20" class="stats-row">
          <el-col :span="6">
            <el-card class="stat-card" shadow="hover">
              <div class="stat-content">
                <div class="stat-icon pending">
                  <i class="el-icon-time"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ workerStats.availableOrders }}</div>
                  <div class="stat-label">可接工单</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card" shadow="hover">
              <div class="stat-content">
                <div class="stat-icon processing">
                  <i class="el-icon-s-tools"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ workerStats.inProgress }}</div>
                  <div class="stat-label">进行中</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card" shadow="hover">
              <div class="stat-content">
                <div class="stat-icon completed">
                  <i class="el-icon-circle-check"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ workerStats.completed }}</div>
                  <div class="stat-label">已完成</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card" shadow="hover">
              <div class="stat-content">
                <div class="stat-icon rating">
                  <i class="el-icon-star-on"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ workerStats.avgRating }}</div>
                  <div class="stat-label">平均评分</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 快捷操作 -->
        <div class="quick-actions">
          <h2>快捷操作</h2>
          <el-row :gutter="20" class="action-row">
            <el-col :span="8">
              <el-card class="action-card" shadow="hover" @click.native="$safeRouter.push('/worker/available-orders')">
                <div class="card-content">
                  <div class="action-icon">
                    <i class="el-icon-document-add"></i>
                  </div>
                  <h3>接单</h3>
                  <p>查看并接受新的维修工单</p>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="action-card" shadow="hover" @click.native="$safeRouter.push('/worker/orders')">
                <div class="card-content">
                  <div class="action-icon">
                    <i class="el-icon-s-order"></i>
                  </div>
                  <h3>我的工单</h3>
                  <p>管理正在进行的维修工单</p>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="action-card" shadow="hover" @click.native="$safeRouter.push('/worker/salary')">
                <div class="card-content">
                  <div class="action-icon">
                    <i class="el-icon-money"></i>
                  </div>
                  <h3>我的薪资</h3>
                  <p>查看薪资明细和历史记录</p>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 今日工单 -->
        <div class="today-orders" v-if="todayOrders.length > 0">
          <h2>今日工单</h2>
          <el-table :data="todayOrders" style="width: 100%">
            <el-table-column prop="id" label="工单号" width="100">
              <template slot-scope="scope">
                <span class="order-id">#{{ scope.row.id }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="appliance_type" label="家电类型" width="120">
              <template slot-scope="scope">
                {{ getApplianceTypeName(scope.row.appliance_type) }}
              </template>
            </el-table-column>
            <el-table-column prop="customer_name" label="客户" width="100"></el-table-column>
            <el-table-column prop="address" label="地址" show-overflow-tooltip></el-table-column>
            <el-table-column prop="preferred_time" label="预约时间" width="120">
              <template slot-scope="scope">
                {{ getTimeSlotText(scope.row.preferred_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button size="mini" @click="viewOrderDetail(scope.row)">查看</el-button>
                <el-button
                  v-if="scope.row.status === 'accepted'"
                  size="mini"
                  type="primary"
                  @click="startRepair(scope.row)"
                >
                  开始
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <i class="el-icon-document"></i>
          <p>今日暂无工单</p>
          <el-button type="primary" @click="$safeRouter.push('/worker/available-orders')">
            去接单
          </el-button>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'WorkerHome',
  computed: {
    ...mapGetters(['currentUser']),
    workerOrders() {
      return this.$store.state.orders || []
    },
    todayOrders() {
      const today = new Date().toDateString()
      return this.workerOrders.filter(order => {
        const orderDate = new Date(order.preferred_date).toDateString()
        return orderDate === today && ['accepted', 'in_progress'].includes(order.status)
      })
    },
    workerStats() {
      const stats = {
        availableOrders: 0,
        inProgress: 0,
        completed: 0,
        avgRating: 0
      }

      // 从真实数据计算统计信息
      stats.availableOrders = this.$store.state.availableOrders.length || 0
      stats.inProgress = this.workerOrders.filter(order =>
        ['accepted', 'in_progress'].includes(order.status)
      ).length
      stats.completed = this.workerOrders.filter(order =>
        order.status === 'completed'
      ).length

      // 计算平均评分
      const completedWithRating = this.workerOrders.filter(order =>
        order.status === 'completed' && order.rating
      )
      if (completedWithRating.length > 0) {
        const totalRating = completedWithRating.reduce((sum, order) => sum + order.rating, 0)
        stats.avgRating = (totalRating / completedWithRating.length).toFixed(1)
      } else {
        stats.avgRating = '暂无评分'
      }

      return stats
    }
  },
  async created() {
    await this.$store.dispatch('fetchWorkerOrders')
    await this.$store.dispatch('fetchAvailableOrders')
  },
  methods: {
    async handleCommand(command) {
      if (command === 'logout') {
        try {
          await this.$confirm('确定要退出登录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })

          const result = await this.$store.dispatch('logout')

          if (result.success) {
            this.$message.success('退出登录成功')
            this.$safeRouter.push('/worker/login')
          }
        } catch (error) {
          // 用户取消退出
          console.log('用户取消退出登录')
        }
      } else if (command === 'profile') {
        this.$message.info('个人信息功能开发中')
      }
    },



    viewOrderDetail(order) {
      this.$safeRouter.push(`/worker/orders?id=${order.id}`)
    },

    startRepair(order) {
      this.$safeRouter.push(`/worker/orders?id=${order.id}&action=start`)
    },

    getApplianceTypeName(type) {
      const typeMap = {
        'washing_machine': '洗衣机',
        'refrigerator': '冰箱',
        'air_conditioner': '空调',
        'television': '电视',
        'microwave': '微波炉',
        'water_heater': '热水器',
        'range_hood': '油烟机',
        'gas_stove': '燃气灶',
        'other': '其他'
      }
      return typeMap[type] || type
    },

    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'accepted': 'info',
        'in_progress': 'primary',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        'pending': '待接单',
        'accepted': '已接单',
        'in_progress': '维修中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || '未知状态'
    },

    getTimeSlotText(timeSlot) {
      const slotMap = {
        'morning': '上午',
        'afternoon': '下午',
        'evening': '晚上'
      }
      return slotMap[timeSlot] || ''
    }
  }
}
</script>

<style scoped>
.worker-home {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #67C23A;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
}

.user-info {
  cursor: pointer;
  color: white;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.user-info:hover {
  opacity: 0.8;
}

.main-content {
  padding: 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.welcome-section h1 {
  color: #303133;
  margin-bottom: 10px;
}

.welcome-section p {
  color: #606266;
  font-size: 16px;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.stat-icon i {
  font-size: 24px;
  color: white;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #E6A23C, #F7BA2A);
}

.stat-icon.processing {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-icon.rating {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.quick-actions {
  margin-bottom: 30px;
}

.quick-actions h2 {
  color: #303133;
  margin-bottom: 20px;
}

.action-row {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.action-card {
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 16px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 200px;
}

.action-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(103, 194, 58, 0.15);
  background: rgba(255, 255, 255, 0.98);
}

.card-content {
  text-align: center;
  padding: 35px 25px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.action-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #67C23A, #85ce61);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-icon i {
  font-size: 2.5rem;
  color: white;
}

.action-card:hover .action-icon {
  transform: scale(1.1) rotateY(360deg);
  box-shadow: 0 8px 25px rgba(103, 194, 58, 0.3);
}

.card-content h3 {
  color: #303133;
  margin-bottom: 12px;
  font-size: 1.3rem;
  font-weight: 600;
}

.card-content p {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.today-orders {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.today-orders h2 {
  color: #303133;
  margin-bottom: 20px;
}

.order-id {
  font-weight: 600;
  color: #67C23A;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.empty-state i {
  font-size: 4rem;
  color: #C0C4CC;
  margin-bottom: 20px;
}

.empty-state p {
  color: #909399;
  font-size: 16px;
  margin-bottom: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .worker-home {
    background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
    min-height: 100vh;
  }

  .header {
    height: auto !important;
    padding: 15px 20px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-radius: 0 0 25px 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    position: relative;
    z-index: 1000;
  }

  .header-left h2 {
    font-size: 16px !important;
    margin: 0;
    color: #2c3e50 !important;
    font-weight: 700;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
  }

  .header-right {
    flex-shrink: 0;
  }

  .user-info {
    background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%) !important;
    color: white !important;
    border-radius: 25px !important;
    padding: 8px 16px !important;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(103, 194, 58, 0.4);
  }

  .main-content {
    padding: 0 15px 20px 15px !important;
    background: transparent !important;
  }

  .welcome-section {
    padding: 25px 20px;
    margin-bottom: 25px;
  }

  .welcome-section h1 {
    font-size: 22px;
    margin-bottom: 10px;
  }

  .welcome-section p {
    font-size: 14px;
  }

  .stats-row {
    margin-bottom: 20px;
  }

  .stat-card {
    margin-bottom: 15px;
  }

  .stat-content {
    padding: 15px;
  }

  .stat-value {
    font-size: 24px;
  }

  .stat-label {
    font-size: 12px;
  }

  .stat-icon {
    font-size: 30px;
  }

  .quick-actions {
    margin-bottom: 25px;
  }

  .action-row {
    flex-direction: column;
    gap: 15px;
  }

  .action-card {
    margin-bottom: 15px;
    min-height: 160px;
  }

  .action-card .card-content {
    padding: 25px 20px;
  }

  .action-icon {
    width: 60px !important;
    height: 60px !important;
    margin: 0 auto 15px !important;
  }

  .action-icon i {
    font-size: 2rem !important;
  }

  .action-card .card-content h3 {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .action-card .card-content p {
    font-size: 12px;
  }

  .recent-orders h2 {
    font-size: 18px;
    margin-bottom: 15px;
  }

  /* 移动端表格优化 */
  .el-table {
    font-size: 12px;
  }

  .el-table .cell {
    padding: 0 5px;
    line-height: 1.3;
  }

  /* 移动端状态标签 */
  .el-tag {
    font-size: 11px;
    padding: 2px 6px;
  }

  /* 移动端按钮组 */
  .el-button-group .el-button {
    padding: 6px 10px;
    font-size: 12px;
  }
}
</style>
