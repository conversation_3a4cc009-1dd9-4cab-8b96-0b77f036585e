<template>
  <div class="worker-orders">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button icon="el-icon-arrow-left" @click="$safeRouter.push('/worker/home')">返回首页</el-button>
          <h2>我的工单</h2>
        </div>
        <div class="header-right">
          <el-button type="primary" icon="el-icon-plus" @click="$safeRouter.push('/worker/available-orders')">
            接单
          </el-button>
        </div>
      </el-header>

      <!-- 主内容区 -->
      <el-main class="main-content">
        <!-- 筛选和搜索 -->
        <el-card class="filter-card" shadow="never">
          <el-row :gutter="20" type="flex" align="middle">
            <el-col :span="4">
              <el-select v-model="filterStatus" placeholder="工单状态" clearable @change="handleFilter">
                <el-option label="全部状态" value=""></el-option>
                <el-option label="已接单" value="accepted"></el-option>
                <el-option label="维修中" value="in_progress"></el-option>
                <el-option label="已完成" value="completed"></el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="filterType" placeholder="家电类型" clearable @change="handleFilter">
                <el-option label="全部类型" value=""></el-option>
                <el-option label="洗衣机" value="washing_machine"></el-option>
                <el-option label="冰箱" value="refrigerator"></el-option>
                <el-option label="空调" value="air_conditioner"></el-option>
                <el-option label="电视" value="television"></el-option>
                <el-option label="微波炉" value="microwave"></el-option>
                <el-option label="热水器" value="water_heater"></el-option>
                <el-option label="油烟机" value="range_hood"></el-option>
                <el-option label="燃气灶" value="gas_stove"></el-option>
                <el-option label="其他" value="other"></el-option>
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索工单号、客户姓名或地址"
                prefix-icon="el-icon-search"
                @input="handleSearch"
                clearable
              ></el-input>
            </el-col>
            <el-col :span="4">
              <el-button @click="refreshOrders" icon="el-icon-refresh">刷新</el-button>
            </el-col>
            <el-col :span="4">
              <el-button @click="showHistory" icon="el-icon-data-line">维修历史</el-button>
            </el-col>
          </el-row>
        </el-card>

        <!-- 工单统计 -->
        <el-row :gutter="20" class="stats-row">
          <el-col :span="8">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number accepted">{{ orderStats.accepted }}</div>
                <div class="stat-label">已接单</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number processing">{{ orderStats.processing }}</div>
                <div class="stat-label">维修中</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number completed">{{ orderStats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 工单列表 -->
        <el-card class="orders-card">
          <div slot="header" class="card-header">
            <span>工单列表</span>
            <small>共 {{ filteredOrders.length }} 条记录</small>
          </div>

          <el-table
            :data="paginatedOrders"
            v-loading="loading"
            style="width: 100%"
            @row-click="viewOrderDetail"
            row-class-name="order-row"
          >
            <el-table-column prop="id" label="工单号" width="100" fixed="left">
              <template slot-scope="scope">
                <span class="order-id">#{{ scope.row.id }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="appliance_type" label="家电类型" width="120">
              <template slot-scope="scope">
                {{ getApplianceTypeName(scope.row.appliance_type) }}
              </template>
            </el-table-column>

            <el-table-column prop="brand_model" label="品牌型号" width="150" show-overflow-tooltip></el-table-column>

            <el-table-column prop="customer_name" label="客户" width="100">
              <template slot-scope="scope">
                {{ scope.row.contact_name }}
              </template>
            </el-table-column>

            <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip></el-table-column>

            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="urgency" label="紧急程度" width="100">
              <template slot-scope="scope">
                <el-tag :type="getUrgencyType(scope.row.urgency)" size="small">
                  {{ getUrgencyText(scope.row.urgency) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="preferred_date" label="预约时间" width="160">
              <template slot-scope="scope">
                {{ formatDate(scope.row.preferred_date) }}
                {{ getTimeSlotText(scope.row.preferred_time) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" @click.stop="viewOrderDetail(scope.row)">查看</el-button>
                <el-button
                  v-if="scope.row.status === 'accepted'"
                  size="mini"
                  type="primary"
                  @click.stop="startRepair(scope.row)"
                >
                  开始维修
                </el-button>
                <el-button
                  v-if="scope.row.status === 'in_progress'"
                  size="mini"
                  type="success"
                  @click.stop="updateProgress(scope.row)"
                >
                  更新进度
                </el-button>
                <el-button
                  v-if="scope.row.status === 'in_progress'"
                  size="mini"
                  type="warning"
                  @click.stop="completeRepair(scope.row)"
                >
                  完成维修
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="filteredOrders.length"
            ></el-pagination>
          </div>
        </el-card>
      </el-main>
    </el-container>

    <!-- 工单详情对话框 -->
    <el-dialog
      :visible.sync="detailVisible"
      title="工单详情"
      width="900px"
      :before-close="handleDetailClose"
    >
      <worker-order-detail
        v-if="selectedOrder"
        :order="selectedOrder"
        @refresh="refreshOrders"
        @start-repair="startRepair"
        @update-progress="updateProgress"
        @complete-repair="completeRepair"
      ></worker-order-detail>
    </el-dialog>

    <!-- 维修进度更新对话框 -->
    <el-dialog
      :visible.sync="progressVisible"
      title="更新维修进度"
      width="600px"
    >
      <progress-update
        v-if="selectedOrder"
        :order="selectedOrder"
        @submit="handleProgressSubmit"
        @cancel="progressVisible = false"
      ></progress-update>
    </el-dialog>

    <!-- 完成维修对话框 -->
    <el-dialog
      :visible.sync="completeVisible"
      title="完成维修"
      width="600px"
    >
      <complete-repair
        v-if="selectedOrder"
        :order="selectedOrder"
        @submit="handleCompleteSubmit"
        @cancel="completeVisible = false"
      ></complete-repair>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import WorkerOrderDetail from './components/WorkerOrderDetail.vue'
import ProgressUpdate from './components/ProgressUpdate.vue'
import CompleteRepair from './components/CompleteRepair.vue'

export default {
  name: 'WorkerOrders',
  components: {
    WorkerOrderDetail,
    ProgressUpdate,
    CompleteRepair
  },
  computed: {
    ...mapGetters(['currentUser']),
    orders() {
      return this.$store.state.orders || []
    },
    filteredOrders() {
      let filtered = this.orders.filter(order =>
        ['accepted', 'in_progress', 'completed'].includes(order.status)
      )

      // 状态筛选
      if (this.filterStatus) {
        filtered = filtered.filter(order => order.status === this.filterStatus)
      }

      // 类型筛选
      if (this.filterType) {
        filtered = filtered.filter(order => order.appliance_type === this.filterType)
      }

      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(order =>
          order.id.toString().includes(keyword) ||
          order.contact_name.toLowerCase().includes(keyword) ||
          order.address.toLowerCase().includes(keyword)
        )
      }

      return filtered
    },
    paginatedOrders() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredOrders.slice(start, end)
    },
    orderStats() {
      const stats = {
        accepted: 0,
        processing: 0,
        completed: 0
      }

      this.orders.forEach(order => {
        switch (order.status) {
          case 'accepted':
            stats.accepted++
            break
          case 'in_progress':
            stats.processing++
            break
          case 'completed':
            stats.completed++
            break
        }
      })

      return stats
    }
  },
  data() {
    return {
      loading: false,
      detailVisible: false,
      progressVisible: false,
      completeVisible: false,
      selectedOrder: null,
      filterStatus: '',
      filterType: '',
      searchKeyword: '',
      currentPage: 1,
      pageSize: 10
    }
  },
  async created() {
    await this.refreshOrders()

    // 检查URL参数
    const { id, action } = this.$route.query
    if (id) {
      const order = this.orders.find(o => o.id.toString() === id)
      if (order) {
        this.selectedOrder = order
        if (action === 'start') {
          this.startRepair(order)
        } else {
          this.detailVisible = true
        }
      }
    }
  },
  methods: {
    async refreshOrders() {
      this.loading = true
      try {
        await this.$store.dispatch('fetchWorkerOrders')
      } catch (error) {
        console.error('[前端] 获取工单列表失败:', error)
        this.$message.error('获取工单列表失败')
      } finally {
        this.loading = false
      }
    },

    handleFilter() {
      this.currentPage = 1
    },

    handleSearch() {
      this.currentPage = 1
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    handleCurrentChange(val) {
      this.currentPage = val
    },

    showHistory() {
      this.$message.info('维修历史功能开发中')
    },

    viewOrderDetail(order) {
      this.selectedOrder = order
      this.detailVisible = true
    },

    handleDetailClose() {
      this.detailVisible = false
      this.selectedOrder = null
      // 清除URL参数，避免重复导航错误
      if (this.$route.query.id || this.$route.query.action) {
        this.$safeRouter.replace({
          path: this.$route.path,
          query: {}
        })
      }
    },

    async startRepair(order) {
      try {
        await this.$confirm(`确定开始维修工单 #${order.id} 吗？`, '开始维修', {
          confirmButtonText: '开始维修',
          cancelButtonText: '取消',
          type: 'info'
        })

        const result = await this.$store.dispatch('startRepair', order.id)

        if (result.success) {
          this.$message.success('已开始维修')
          this.detailVisible = false
          await this.refreshOrders()
        } else {
          this.$message.error(result.message || '开始维修失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('开始维修失败')
        }
      }
    },

    updateProgress(order) {
      this.selectedOrder = order
      this.progressVisible = true
    },

    async handleProgressSubmit(progressData) {
      try {
        console.log('[前端] 更新维修进度:', progressData)
        this.progressVisible = false
        await this.refreshOrders()
      } catch (error) {
        this.$message.error('更新进度失败')
      }
    },

    completeRepair(order) {
      this.selectedOrder = order
      this.completeVisible = true
    },

    async handleCompleteSubmit(completeData) {
      try {
        console.log('[前端] 完成维修:', completeData)
        this.completeVisible = false
        await this.refreshOrders()
      } catch (error) {
        this.$message.error('完成维修失败')
      }
    },

    getApplianceTypeName(type) {
      const typeMap = {
        'washing_machine': '洗衣机',
        'refrigerator': '冰箱',
        'air_conditioner': '空调',
        'television': '电视',
        'microwave': '微波炉',
        'water_heater': '热水器',
        'range_hood': '油烟机',
        'gas_stove': '燃气灶',
        'other': '其他'
      }
      return typeMap[type] || type
    },

    getStatusType(status) {
      const statusMap = {
        'accepted': 'info',
        'in_progress': 'primary',
        'completed': 'success'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        'accepted': '已接单',
        'in_progress': '维修中',
        'completed': '已完成'
      }
      return statusMap[status] || '未知状态'
    },

    getUrgencyType(urgency) {
      const urgencyMap = {
        'low': 'info',
        'medium': 'warning',
        'high': 'danger'
      }
      return urgencyMap[urgency] || 'info'
    },

    getUrgencyText(urgency) {
      const urgencyMap = {
        'low': '一般',
        'medium': '紧急',
        'high': '非常紧急'
      }
      return urgencyMap[urgency] || '一般'
    },

    getTimeSlotText(timeSlot) {
      const slotMap = {
        'morning': '上午',
        'afternoon': '下午',
        'evening': '晚上'
      }
      return slotMap[timeSlot] || ''
    },

    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style scoped>
.worker-orders {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #67C23A;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
}

.main-content {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-number.accepted {
  color: #409EFF;
}

.stat-number.processing {
  color: #E6A23C;
}

.stat-number.completed {
  color: #67C23A;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.orders-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.card-header span {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-header small {
  color: #909399;
  font-size: 12px;
}

.order-row {
  cursor: pointer;
}

.order-row:hover {
  background-color: #f5f7fa;
}

.order-id {
  font-weight: 600;
  color: #67C23A;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 10px;
  }

  .header-left {
    gap: 10px;
  }

  .header-left h2 {
    font-size: 16px;
  }

  .stats-row .el-col {
    margin-bottom: 10px;
  }

  .el-table {
    font-size: 12px;
  }

  .filter-card .el-row .el-col {
    margin-bottom: 10px;
  }
}
</style>
