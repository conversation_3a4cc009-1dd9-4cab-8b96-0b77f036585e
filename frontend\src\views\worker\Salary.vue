<template>
  <div class="worker-salary">
    <div class="page-header">
      <h2>我的薪资</h2>
      <el-button @click="$router.push('/worker/home')">返回首页</el-button>
    </div>

    <!-- 当前月份薪资概览 -->
    <el-card class="current-salary-card" header="本月薪资概览">
      <div v-if="currentSalary" class="salary-overview">
        <div class="salary-main">
          <div class="total-amount">
            <span class="amount">¥{{ formatMoney(currentSalary.total_salary) }}</span>
            <span class="label">本月总薪资</span>
          </div>
          <div class="salary-status">
            <el-tag :type="getStatusType(currentSalary.status)" size="medium">
              {{ getStatusText(currentSalary.status) }}
            </el-tag>
          </div>
        </div>
        
        <div class="salary-breakdown">
          <div class="breakdown-item">
            <span class="item-label">基础工资</span>
            <span class="item-value">¥{{ formatMoney(currentSalary.base_salary) }}</span>
          </div>
          <div class="breakdown-item">
            <span class="item-label">提成收入</span>
            <span class="item-value">¥{{ formatMoney(currentSalary.commission_amount) }}</span>
          </div>
          <div class="breakdown-item">
            <span class="item-label">绩效奖金</span>
            <span class="item-value">¥{{ formatMoney(currentSalary.performance_bonus) }}</span>
          </div>
          <div class="breakdown-item">
            <span class="item-label">全勤奖</span>
            <span class="item-value">¥{{ formatMoney(currentSalary.attendance_bonus) }}</span>
          </div>
          <div class="breakdown-item">
            <span class="item-label">加班费</span>
            <span class="item-value">¥{{ formatMoney(currentSalary.overtime_pay) }}</span>
          </div>
        </div>
        
        <div class="performance-stats">
          <div class="stat-item">
            <div class="stat-value">{{ currentSalary.completed_orders }}</div>
            <div class="stat-label">完成工单</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">¥{{ formatMoney(currentSalary.total_revenue) }}</div>
            <div class="stat-label">营收金额</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ currentSalary.attendance_days }}</div>
            <div class="stat-label">出勤天数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ (parseFloat(currentSalary.avg_rating) || 0).toFixed(1) }}</div>
            <div class="stat-label">平均评分</div>
          </div>
        </div>
      </div>
      
      <div v-else class="no-salary">
        <el-empty description="本月暂无薪资数据"></el-empty>
      </div>
    </el-card>

    <!-- 历史薪资记录 -->
    <el-card class="history-card" header="历史薪资记录">
      <el-table 
        :data="salaryHistory" 
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="year" label="年份" width="80"></el-table-column>
        <el-table-column prop="month" label="月份" width="80">
          <template slot-scope="scope">
            {{ scope.row.month }}月
          </template>
        </el-table-column>
        <el-table-column prop="completed_orders" label="完成工单" width="100"></el-table-column>
        <el-table-column prop="total_revenue" label="营收金额" width="120">
          <template slot-scope="scope">
            ¥{{ formatMoney(scope.row.total_revenue) }}
          </template>
        </el-table-column>
        <el-table-column prop="base_salary" label="基础工资" width="120">
          <template slot-scope="scope">
            ¥{{ formatMoney(scope.row.base_salary) }}
          </template>
        </el-table-column>
        <el-table-column prop="commission_amount" label="提成收入" width="120">
          <template slot-scope="scope">
            ¥{{ formatMoney(scope.row.commission_amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="total_salary" label="总薪资" width="120">
          <template slot-scope="scope">
            <span class="total-salary">¥{{ formatMoney(scope.row.total_salary) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="avg_rating" label="平均评分" width="100">
          <template slot-scope="scope">
            <el-rate :value="parseFloat(scope.row.avg_rating) || 0" disabled show-score></el-rate>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewSalaryDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 薪资详情对话框 -->
    <el-dialog
      title="薪资详情"
      :visible.sync="detailVisible"
      width="600px"
      @close="selectedSalary = null"
    >
      <div v-if="selectedSalary" class="salary-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="计算月份">{{ selectedSalary.year }}年{{ selectedSalary.month }}月</el-descriptions-item>
          <el-descriptions-item label="完成工单">{{ selectedSalary.completed_orders }}单</el-descriptions-item>
          <el-descriptions-item label="营收金额">¥{{ formatMoney(selectedSalary.total_revenue) }}</el-descriptions-item>
          <el-descriptions-item label="出勤天数">{{ selectedSalary.attendance_days }}天</el-descriptions-item>
          <el-descriptions-item label="平均评分">
            <el-rate :value="parseFloat(selectedSalary.avg_rating) || 0" disabled show-score></el-rate>
          </el-descriptions-item>
          <el-descriptions-item label="计算时间">{{ formatDate(selectedSalary.calculated_at) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="salary-breakdown-detail">
          <h4>薪资构成</h4>
          <el-table :data="salaryDetails" style="width: 100%">
            <el-table-column prop="item_name" label="项目" width="150"></el-table-column>
            <el-table-column prop="amount" label="金额" width="120">
              <template slot-scope="scope">
                ¥{{ formatMoney(scope.row.amount) }}
              </template>
            </el-table-column>
            <el-table-column prop="description" label="说明"></el-table-column>
          </el-table>
          
          <div class="total-row">
            <strong>总计：¥{{ formatMoney(selectedSalary.total_salary) }}</strong>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'WorkerSalary',
  data() {
    return {
      loading: false,
      currentSalary: null,
      salaryHistory: [],
      detailVisible: false,
      selectedSalary: null,
      salaryDetails: []
    }
  },
  computed: {
    currentUser() {
      return this.$store.state.user
    }
  },
  async created() {
    await this.loadSalaryData()
  },
  methods: {
    async loadSalaryData() {
      if (!this.currentUser) return
      
      try {
        this.loading = true
        
        const response = await axios.get(`/api/salary/list/${this.currentUser.id}`)
        
        if (response.success) {
          this.salaryHistory = response.data
          
          // 获取当前月份的薪资
          const now = new Date()
          const currentYear = now.getFullYear()
          const currentMonth = now.getMonth() + 1
          
          this.currentSalary = this.salaryHistory.find(s => 
            s.year === currentYear && s.month === currentMonth
          )
        } else {
          this.$message.error(response.message || '获取薪资数据失败')
        }
      } catch (error) {
        console.error('获取薪资数据失败:', error)
        this.$message.error('获取薪资数据失败')
      } finally {
        this.loading = false
      }
    },
    
    async viewSalaryDetail(salary) {
      try {
        this.loading = true
        
        const response = await axios.get(`/api/salary/detail/${salary.id}`)
        
        if (response.success) {
          this.selectedSalary = response.data.salary
          this.salaryDetails = response.data.details
          this.detailVisible = true
        } else {
          this.$message.error(response.message || '获取薪资详情失败')
        }
      } catch (error) {
        console.error('获取薪资详情失败:', error)
        this.$message.error('获取薪资详情失败')
      } finally {
        this.loading = false
      }
    },
    
    getStatusType(status) {
      const statusMap = {
        'draft': 'info',
        'confirmed': 'warning',
        'paid': 'success'
      }
      return statusMap[status] || 'info'
    },
    
    getStatusText(status) {
      const statusMap = {
        'draft': '计算中',
        'confirmed': '已确认',
        'paid': '已发放'
      }
      return statusMap[status] || status
    },
    
    formatMoney(amount) {
      if (!amount) return '0.00'
      return parseFloat(amount).toFixed(2)
    },
    
    formatDate(dateString) {
      if (!dateString) return '未设置'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.worker-salary {
  padding: 20px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.current-salary-card {
  margin-bottom: 20px;
}

.salary-overview {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 30px;
  align-items: start;
}

.salary-main {
  text-align: center;
}

.total-amount .amount {
  display: block;
  font-size: 36px;
  font-weight: bold;
  color: #E6A23C;
  margin-bottom: 5px;
}

.total-amount .label {
  font-size: 14px;
  color: #909399;
}

.salary-status {
  margin-top: 15px;
}

.salary-breakdown {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.item-label {
  color: #606266;
}

.item-value {
  font-weight: bold;
  color: #303133;
}

.performance-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.no-salary {
  text-align: center;
  padding: 40px;
}

.history-card {
  margin-bottom: 20px;
}

.total-salary {
  font-weight: bold;
  color: #E6A23C;
}

.salary-breakdown-detail {
  margin-top: 20px;
}

.salary-breakdown-detail h4 {
  margin-bottom: 15px;
  color: #303133;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.total-row {
  margin-top: 15px;
  text-align: right;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 16px;
}

@media (max-width: 768px) {
  .salary-overview {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
}
</style>
