<template>
  <div class="complete-repair">
    <el-form 
      ref="completeForm" 
      :model="completeForm" 
      :rules="completeRules" 
      label-width="120px"
    >
      <el-form-item label="维修结果" prop="result">
        <el-radio-group v-model="completeForm.result">
          <el-radio label="success">维修成功</el-radio>
          <el-radio label="partial">部分修复</el-radio>
          <el-radio label="failed">无法修复</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="问题原因" prop="cause">
        <el-input
          v-model="completeForm.cause"
          type="textarea"
          :rows="3"
          placeholder="请详细说明故障的原因..."
          maxlength="300"
          show-word-limit
        ></el-input>
      </el-form-item>
      
      <el-form-item label="解决方案" prop="solution">
        <el-input
          v-model="completeForm.solution"
          type="textarea"
          :rows="3"
          placeholder="请详细说明采取的解决方案..."
          maxlength="300"
          show-word-limit
        ></el-input>
      </el-form-item>
      
      <el-form-item label="更换配件" v-if="showPartsSection">
        <el-input
          v-model="completeForm.replaced_parts"
          type="textarea"
          :rows="2"
          placeholder="请列出更换的配件名称、型号、数量等"
          maxlength="200"
          show-word-limit
        ></el-input>
      </el-form-item>
      
      <el-form-item>
        <el-checkbox v-model="showPartsSection">更换了配件</el-checkbox>
      </el-form-item>
      
      <el-form-item label="维修费用" prop="cost">
        <el-input-number
          v-model="completeForm.cost"
          :min="0"
          :max="9999"
          :precision="2"
          placeholder="请输入维修费用"
          style="width: 200px"
        ></el-input-number>
        <span style="margin-left: 10px; color: #909399;">元</span>
      </el-form-item>
      
      <el-form-item label="完成图片">
        <el-upload
          class="upload-demo"
          action="#"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :before-upload="beforeUpload"
          :file-list="fileList"
          list-type="picture"
          multiple
          :limit="5"
          :on-exceed="handleExceed"
        >
          <el-button size="small" type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">
            可上传维修完成后的图片，只能上传jpg/png文件，且不超过2MB，最多5张
          </div>
        </el-upload>
      </el-form-item>
      
      <el-form-item label="使用建议">
        <el-input
          v-model="completeForm.suggestions"
          type="textarea"
          :rows="3"
          placeholder="给客户的使用建议和保养提醒（选填）"
          maxlength="300"
          show-word-limit
        ></el-input>
      </el-form-item>
      
      <el-form-item label="保修期限" v-if="completeForm.result === 'success'">
        <el-select v-model="completeForm.warranty_period" placeholder="请选择保修期限">
          <el-option label="1个月" value="1"></el-option>
          <el-option label="3个月" value="3"></el-option>
          <el-option label="6个月" value="6"></el-option>
          <el-option label="1年" value="12"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    
    <div class="actions">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="submitComplete" :loading="submitting">
        完成维修
      </el-button>
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="previewVisible" title="图片预览">
      <img width="100%" :src="previewImageUrl" alt="预览图片">
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CompleteRepair',
  props: {
    order: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      submitting: false,
      previewVisible: false,
      previewImageUrl: '',
      fileList: [],
      showPartsSection: false,
      completeForm: {
        result: 'success',
        cause: '',
        solution: '',
        replaced_parts: '',
        cost: 0,
        suggestions: '',
        warranty_period: '3'
      },
      completeRules: {
        result: [
          { required: true, message: '请选择维修结果', trigger: 'change' }
        ],
        cause: [
          { required: true, message: '请说明问题原因', trigger: 'blur' },
          { min: 3, message: '原因说明至少3个字符', trigger: 'blur' }
        ],
        solution: [
          { required: true, message: '请说明解决方案', trigger: 'blur' },
          { min: 3, message: '解决方案至少3个字符', trigger: 'blur' }
        ],
        cost: [
          { required: true, message: '请输入维修费用', trigger: 'blur' },
          { type: 'number', min: 0, message: '费用不能为负数', trigger: 'blur' }
        ],
        summary: [
          { required: true, message: '请填写维修总结', trigger: 'blur' },
          { min: 20, message: '维修总结至少20个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async submitComplete() {
      try {
        await this.$refs.completeForm.validate()
        
        this.submitting = true
        
        const completeData = {
          orderId: this.order.id,
          ...this.completeForm,
          images: this.fileList.map(file => file.url || file.response?.url).filter(Boolean),
          completed_at: new Date().toISOString()
        }
        
        console.log('[前端] 完成维修:', completeData)
        
        const result = await this.$store.dispatch('completeRepair', {
          orderId: this.order.id,
          repairData: completeData
        })

        if (result.success) {
          this.$message.success('维修已完成')
          this.$emit('submit', completeData)
        } else {
          this.$message.error(result.message || '完成维修失败')
        }
      } catch (error) {
        console.error('[前端] 完成维修失败:', error)
        if (error.message !== 'validation failed') {
          this.$message.error('提交失败，请检查网络连接')
        }
      } finally {
        this.submitting = false
      }
    },
    
    // 图片上传相关方法
    handlePreview(file) {
      this.previewImageUrl = file.url
      this.previewVisible = true
    },
    
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    
    beforeUpload(file) {
      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isJPGOrPNG) {
        this.$message.error('只能上传 JPG/PNG 格式的图片!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      
      // 模拟上传成功
      const reader = new FileReader()
      reader.onload = (e) => {
        this.fileList.push({
          name: file.name,
          url: e.target.result
        })
      }
      reader.readAsDataURL(file)
      
      return false // 阻止自动上传
    },
    
    handleExceed(files, fileList) {
      this.$message.warning(`最多只能上传5张图片，当前选择了 ${files.length} 张图片，共选择了 ${files.length + fileList.length} 张图片`)
    }
  }
}
</script>

<style scoped>
.complete-repair {
  padding: 20px 0;
}

.upload-demo {
  margin-top: 10px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 7px;
}

.actions {
  margin-top: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.actions .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .actions {
    text-align: center;
  }
  
  .actions .el-button {
    width: 45%;
    margin: 0 2.5%;
  }
}
</style>
