<template>
  <div class="progress-update">
    <el-form 
      ref="progressForm" 
      :model="progressForm" 
      :rules="progressRules" 
      label-width="100px"
    >
      <el-form-item label="进度标题" prop="title">
        <el-input 
          v-model="progressForm.title" 
          placeholder="请输入进度标题，如：开始检查、发现问题、更换配件等"
          maxlength="50"
          show-word-limit
        ></el-input>
      </el-form-item>
      
      <el-form-item label="详细描述" prop="description">
        <el-input
          v-model="progressForm.description"
          type="textarea"
          :rows="4"
          placeholder="请详细描述当前的维修进度和发现的问题..."
          maxlength="500"
          show-word-limit
        ></el-input>
      </el-form-item>
      
      <el-form-item label="进度图片">
        <el-upload
          class="upload-demo"
          action="#"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :before-upload="beforeUpload"
          :file-list="fileList"
          list-type="picture"
          multiple
          :limit="5"
          :on-exceed="handleExceed"
        >
          <el-button size="small" type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">
            可上传维修过程图片，只能上传jpg/png文件，且不超过2MB，最多5张
          </div>
        </el-upload>
      </el-form-item>
      
      <el-form-item label="预计完成">
        <el-date-picker
          v-model="progressForm.estimated_completion"
          type="datetime"
          placeholder="选择预计完成时间"
          style="width: 100%"
          :picker-options="datePickerOptions"
        ></el-date-picker>
      </el-form-item>
      
      <el-form-item label="需要配件" v-if="showPartsSection">
        <el-input
          v-model="progressForm.required_parts"
          type="textarea"
          :rows="3"
          placeholder="如需要配件，请详细说明配件名称、型号、数量等"
          maxlength="200"
          show-word-limit
        ></el-input>
      </el-form-item>
      
      <el-form-item>
        <el-checkbox v-model="showPartsSection">需要配件</el-checkbox>
      </el-form-item>
    </el-form>
    
    <div class="actions">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="submitProgress" :loading="submitting">
        提交进度
      </el-button>
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="previewVisible" title="图片预览">
      <img width="100%" :src="previewImageUrl" alt="预览图片">
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ProgressUpdate',
  props: {
    order: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      submitting: false,
      previewVisible: false,
      previewImageUrl: '',
      fileList: [],
      showPartsSection: false,
      progressForm: {
        title: '',
        description: '',
        estimated_completion: '',
        required_parts: ''
      },
      progressRules: {
        title: [
          { required: true, message: '请输入进度标题', trigger: 'blur' },
          { min: 2, message: '标题至少2个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入详细描述', trigger: 'blur' },
          { min: 10, message: '描述至少10个字符', trigger: 'blur' }
        ]
      },
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
        }
      }
    }
  },
  methods: {
    async submitProgress() {
      try {
        await this.$refs.progressForm.validate()
        
        this.submitting = true
        
        const progressData = {
          orderId: this.order.id,
          ...this.progressForm,
          images: this.fileList.map(file => file.url || file.response?.url).filter(Boolean)
        }
        
        console.log('[前端] 提交维修进度:', progressData)
        
        const result = await this.$store.dispatch('updateProgress', {
          orderId: this.order.id,
          progressData
        })

        if (result.success) {
          this.$message.success('维修进度已更新')
          this.$emit('submit', progressData)
        } else {
          this.$message.error(result.message || '更新进度失败')
        }
      } catch (error) {
        console.error('[前端] 提交进度失败:', error)
        if (error.message !== 'validation failed') {
          this.$message.error('提交失败，请检查网络连接')
        }
      } finally {
        this.submitting = false
      }
    },
    
    // 图片上传相关方法
    handlePreview(file) {
      this.previewImageUrl = file.url
      this.previewVisible = true
    },
    
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    
    beforeUpload(file) {
      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isJPGOrPNG) {
        this.$message.error('只能上传 JPG/PNG 格式的图片!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      
      // 模拟上传成功
      const reader = new FileReader()
      reader.onload = (e) => {
        this.fileList.push({
          name: file.name,
          url: e.target.result
        })
      }
      reader.readAsDataURL(file)
      
      return false // 阻止自动上传
    },
    
    handleExceed(files, fileList) {
      this.$message.warning(`最多只能上传5张图片，当前选择了 ${files.length} 张图片，共选择了 ${files.length + fileList.length} 张图片`)
    }
  }
}
</script>

<style scoped>
.progress-update {
  padding: 20px 0;
}

.upload-demo {
  margin-top: 10px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 7px;
}

.actions {
  margin-top: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.actions .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .actions {
    text-align: center;
  }
  
  .actions .el-button {
    width: 45%;
    margin: 0 2.5%;
  }
}
</style>
