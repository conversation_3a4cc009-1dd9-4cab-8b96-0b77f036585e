<template>
  <div class="worker-order-detail">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="工单号">
        <span class="order-id">#{{ order.id }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="getStatusType(order.status)">
          {{ getStatusText(order.status) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="家电类型">
        {{ getApplianceTypeName(order.appliance_type) }}
      </el-descriptions-item>
      <el-descriptions-item label="品牌型号">
        {{ order.brand_model }}
      </el-descriptions-item>
      <el-descriptions-item label="紧急程度">
        <el-tag :type="getUrgencyType(order.urgency)" size="small">
          {{ getUrgencyText(order.urgency) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="购买时间">
        {{ formatDate(order.purchase_date) }}
      </el-descriptions-item>
      <el-descriptions-item label="客户姓名">
        {{ order.contact_name }}
      </el-descriptions-item>
      <el-descriptions-item label="联系电话">
        <a :href="`tel:${order.contact_phone}`" class="phone-link">
          <i class="el-icon-phone"></i>
          {{ order.contact_phone }}
        </a>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDate(order.created_at) }}
      </el-descriptions-item>
      <el-descriptions-item label="预约时间">
        {{ formatDate(order.preferred_date) }} {{ getTimeSlotText(order.preferred_time) }}
      </el-descriptions-item>
    </el-descriptions>
    
    <el-divider content-position="left">详细地址</el-divider>
    <div class="address-section">
      <p class="address">{{ order.address }}</p>
      <el-button size="small" type="primary" @click="openMap">
        <i class="el-icon-location"></i>
        导航
      </el-button>
    </div>
    
    <el-divider content-position="left">故障描述</el-divider>
    <p class="description">{{ order.problem_description }}</p>
    
    <el-divider content-position="left" v-if="order.remarks">备注信息</el-divider>
    <p class="remarks" v-if="order.remarks">{{ order.remarks }}</p>
    
    <el-divider content-position="left" v-if="order.images && order.images.length > 0">故障图片</el-divider>
    <div class="images" v-if="order.images && order.images.length > 0">
      <el-image
        v-for="(image, index) in order.images"
        :key="index"
        :src="image"
        :preview-src-list="order.images"
        class="image-item"
        fit="cover"
      ></el-image>
    </div>
    
    <!-- 维修进度 -->
    <el-divider content-position="left" v-if="order.progress && order.progress.length > 0">维修进度</el-divider>
    <el-timeline v-if="order.progress && order.progress.length > 0">
      <el-timeline-item
        v-for="(item, index) in order.progress"
        :key="index"
        :timestamp="formatDate(item.created_at)"
        placement="top"
      >
        <el-card>
          <h4>{{ item.title }}</h4>
          <p>{{ item.description }}</p>
          <div v-if="item.images && item.images.length > 0" class="progress-images">
            <el-image
              v-for="(img, imgIndex) in item.images"
              :key="imgIndex"
              :src="img"
              :preview-src-list="item.images"
              class="progress-image"
              fit="cover"
            ></el-image>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
    
    <!-- 操作按钮 -->
    <div class="actions">
      <el-button 
        v-if="order.status === 'pending' && showAcceptButton" 
        type="primary" 
        @click="$emit('accept', order)"
      >
        接单
      </el-button>
      
      <el-button 
        v-if="order.status === 'accepted'" 
        type="primary" 
        @click="$emit('start-repair', order)"
      >
        开始维修
      </el-button>
      
      <el-button 
        v-if="order.status === 'in_progress'" 
        type="info" 
        @click="$emit('update-progress', order)"
      >
        更新进度
      </el-button>
      
      <el-button 
        v-if="order.status === 'in_progress'" 
        type="success" 
        @click="$emit('complete-repair', order)"
      >
        完成维修
      </el-button>
      
      <el-button @click="callCustomer">
        <i class="el-icon-phone"></i>
        联系客户
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WorkerOrderDetail',
  props: {
    order: {
      type: Object,
      required: true
    },
    showAcceptButton: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    callCustomer() {
      window.location.href = `tel:${this.order.contact_phone}`
    },
    
    openMap() {
      // 这里可以集成地图API，比如高德地图、百度地图等
      const address = encodeURIComponent(this.order.address)
      // 使用高德地图
      window.open(`https://uri.amap.com/navigation?to=${address}`)
    },
    
    getApplianceTypeName(type) {
      const typeMap = {
        'washing_machine': '洗衣机',
        'refrigerator': '冰箱',
        'air_conditioner': '空调',
        'television': '电视',
        'microwave': '微波炉',
        'water_heater': '热水器',
        'range_hood': '油烟机',
        'gas_stove': '燃气灶',
        'other': '其他'
      }
      return typeMap[type] || type
    },
    
    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'accepted': 'info',
        'in_progress': 'primary',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    getStatusText(status) {
      const statusMap = {
        'pending': '待接单',
        'accepted': '已接单',
        'in_progress': '维修中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || '未知状态'
    },
    
    getUrgencyType(urgency) {
      const urgencyMap = {
        'low': 'info',
        'medium': 'warning',
        'high': 'danger'
      }
      return urgencyMap[urgency] || 'info'
    },
    
    getUrgencyText(urgency) {
      const urgencyMap = {
        'low': '一般',
        'medium': '紧急',
        'high': '非常紧急'
      }
      return urgencyMap[urgency] || '一般'
    },
    
    getTimeSlotText(timeSlot) {
      const slotMap = {
        'morning': '上午 (9:00-12:00)',
        'afternoon': '下午 (13:00-17:00)',
        'evening': '晚上 (18:00-20:00)'
      }
      return slotMap[timeSlot] || ''
    },
    
    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.worker-order-detail {
  padding: 20px 0;
}

.order-id {
  font-weight: 600;
  color: #67C23A;
}

.phone-link {
  color: #409EFF;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 5px;
}

.phone-link:hover {
  text-decoration: underline;
}

.address-section {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin: 10px 0;
}

.address {
  flex: 1;
  margin: 0;
  line-height: 1.6;
}

.description, .remarks {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  line-height: 1.6;
  margin: 10px 0;
}

.images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 10px 0;
}

.image-item {
  width: 100px;
  height: 100px;
  border-radius: 4px;
}

.progress-images {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.progress-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
}

.actions {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.actions .el-button {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .address-section {
    flex-direction: column;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .actions .el-button {
    width: 100%;
  }
}
</style>
