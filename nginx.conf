server {
    listen 80;
    listen 443 ssl http2;
    server_name www.w.chenkk.xyz;
    
    #SSL-START SSL相关配置，请勿删除或修改下一行带注释的404规则
    #error_page 404/404.html;
    #SSL-END
    
    # 前端静态文件 - 直接服务dist目录
    location / {
        root /www/www.w.chenkk.xyz/家电维修系统/frontend/dist;
        try_files $uri $uri/ /index.html;
        index index.html;
        
        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3333;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传目录
    location /uploads/ {
        alias /www/www.w.chenkk.xyz/家电维修系统/server/uploads/;
        expires 30d;
    }
    
    #CERT-APPLY-CHECK--START
    # 用于SSL证书申请时的文件验证，请勿删除
    location ~ \.well-known{
        allow all;
    }
    #CERT-APPLY-CHECK--END
    
    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }
    
    #一键申请SSL证书验证目录相关设置
    location ~ \.well-known{
        allow all;
    }
    
    access_log  /www/wwwlogs/www.w.chenkk.xyz.log;
    error_log  /www/wwwlogs/www.w.chenkk.xyz.error.log;
}


